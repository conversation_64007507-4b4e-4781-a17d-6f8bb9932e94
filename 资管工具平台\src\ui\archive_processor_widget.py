#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
压缩包处理界面组件
"""

import os
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
                            QLabel, QLineEdit, QPushButton, QTextEdit,
                            QProgressBar, QCheckBox, QFileDialog, QMessageBox,
                            QTableWidget, QTableWidgetItem, QHeaderView,
                            QSplitter, QFrame)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer, QUrl
from PyQt6.QtGui import QFont, QDragEnterEvent, QDropEvent

from modules.archive_processor import ArchiveProcessor, ArchiveProcessorWorker
from utils.config_manager import ConfigManager


class ArchiveProcessorWidget(QWidget):
    """压缩包处理界面组件"""
    
    status_changed = pyqtSignal(str)  # 状态变更信号
    
    def __init__(self):
        super().__init__()
        self.processor = ArchiveProcessor()
        self.worker = None
        self.config_manager = ConfigManager()
        self.init_ui()
        self.setup_connections()
        self.load_settings()
        self.setup_drag_drop()
        
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 创建主分割器
        splitter = QSplitter(Qt.Orientation.Vertical)
        layout.addWidget(splitter)
        
        # 上半部分：控制面板
        control_widget = self.create_control_panel()
        splitter.addWidget(control_widget)
        
        # 下半部分：结果显示
        result_widget = self.create_result_panel()
        splitter.addWidget(result_widget)
        
        # 设置分割器比例
        splitter.setSizes([300, 500])
        
    def create_control_panel(self):
        """创建控制面板"""
        widget = QFrame()
        widget.setFrameStyle(QFrame.Shape.StyledPanel)
        layout = QVBoxLayout(widget)
        
        # 目录选择组
        dir_group = QGroupBox("目录选择")
        dir_layout = QVBoxLayout(dir_group)
        
        # 目录路径输入
        path_layout = QHBoxLayout()
        path_layout.addWidget(QLabel("目录路径:"))
        
        self.dir_entry = QLineEdit()
        self.dir_entry.setPlaceholderText("请选择包含压缩包的目录...")
        path_layout.addWidget(self.dir_entry)
        
        self.browse_btn = QPushButton("浏览")
        self.browse_btn.setMinimumWidth(80)
        path_layout.addWidget(self.browse_btn)
        
        dir_layout.addLayout(path_layout)
        layout.addWidget(dir_group)
        
        # 选项设置组
        options_group = QGroupBox("处理选项")
        options_layout = QVBoxLayout(options_group)
        
        self.dry_run_cb = QCheckBox("试运行模式（不实际删除文件）")
        # 不设置默认值，将由load_settings()方法设置
        options_layout.addWidget(self.dry_run_cb)

        self.confirm_delete_cb = QCheckBox("删除前确认")
        # 不设置默认值，将由load_settings()方法设置
        options_layout.addWidget(self.confirm_delete_cb)

        self.backup_cb = QCheckBox("备份原始文件")
        # 不设置默认值，将由load_settings()方法设置
        self.backup_cb.setToolTip("在删除前将所有文件备份到backup文件夹")
        options_layout.addWidget(self.backup_cb)

        self.delete_invalid_cb = QCheckBox("删除无效文件（不符合命名规范）")
        # 不设置默认值，将由load_settings()方法设置
        self.delete_invalid_cb.setToolTip("删除不符合命名规范的文件")
        options_layout.addWidget(self.delete_invalid_cb)
        
        layout.addWidget(options_group)
        
        # 操作按钮组
        button_layout = QHBoxLayout()
        
        self.analyze_btn = QPushButton("分析目录")
        self.analyze_btn.setMinimumHeight(35)
        button_layout.addWidget(self.analyze_btn)
        
        self.process_btn = QPushButton("开始处理")
        self.process_btn.setMinimumHeight(35)
        self.process_btn.setEnabled(False)
        button_layout.addWidget(self.process_btn)
        
        self.clear_btn = QPushButton("清空日志")
        self.clear_btn.setMinimumHeight(35)
        button_layout.addWidget(self.clear_btn)
        
        layout.addLayout(button_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        return widget
        
    def create_result_panel(self):
        """创建结果显示面板"""
        widget = QFrame()
        widget.setFrameStyle(QFrame.Shape.StyledPanel)
        layout = QVBoxLayout(widget)
        
        # 创建选项卡式显示
        result_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧：文件分析表格
        analysis_group = QGroupBox("文件分析")
        analysis_layout = QVBoxLayout(analysis_group)
        
        self.analysis_table = QTableWidget()
        self.analysis_table.setColumnCount(6)
        self.analysis_table.setHorizontalHeaderLabels([
            "文件名", "类别", "描述", "版本", "日期", "状态"
        ])
        
        # 设置表格列宽
        header = self.analysis_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)
        
        analysis_layout.addWidget(self.analysis_table)
        result_splitter.addWidget(analysis_group)
        
        # 右侧：处理日志
        log_group = QGroupBox("处理日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setFont(QFont("Consolas", 9))
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        result_splitter.addWidget(log_group)
        
        # 设置分割器比例
        result_splitter.setSizes([400, 300])
        
        layout.addWidget(result_splitter)
        return widget
        
    def setup_connections(self):
        """设置信号连接"""
        self.browse_btn.clicked.connect(self.browse_directory)
        self.analyze_btn.clicked.connect(self.analyze_directory)
        self.process_btn.clicked.connect(self.process_files)
        self.clear_btn.clicked.connect(self.clear_log)
        self.dir_entry.textChanged.connect(self.on_directory_changed)

        # 勾选框信号连接将在load_settings()中设置

        # 连接处理器信号
        self.processor.log_message.connect(self.add_log_message)
        self.processor.processing_finished.connect(self.on_processing_finished)
        
    def browse_directory(self):
        """浏览目录"""
        directory = QFileDialog.getExistingDirectory(
            self, "选择包含压缩包的目录", self.dir_entry.text())
        if directory:
            self.dir_entry.setText(directory)
            
    def on_directory_changed(self):
        """目录路径变更时的处理"""
        has_path = bool(self.dir_entry.text().strip())
        self.analyze_btn.setEnabled(has_path)
        self.process_btn.setEnabled(False)
        self.analysis_table.setRowCount(0)
        
    def analyze_directory(self):
        """分析目录"""
        directory = self.dir_entry.text().strip()
        if not directory:
            QMessageBox.warning(self, "警告", "请先选择目录")
            return
            
        if not os.path.exists(directory):
            QMessageBox.warning(self, "警告", "目录不存在")
            return
            
        self.add_log_message(f"开始分析目录: {directory}")
        self.status_changed.emit("正在分析目录...")
        
        # 获取分析结果
        analysis = self.processor.get_directory_analysis(directory)
        
        if "error" in analysis:
            QMessageBox.critical(self, "错误", f"分析失败: {analysis['error']}")
            self.status_changed.emit("分析失败")
            return
            
        # 更新分析表格
        self.update_analysis_table(analysis)
        
        # 更新日志
        self.add_log_message(f"分析完成:")
        self.add_log_message(f"  总文件数: {analysis['total_files']}")
        self.add_log_message(f"  有效压缩包: {analysis['valid_archives']}")
        self.add_log_message(f"  将删除文件: {analysis['delete_count']}")
        
        if analysis['invalid_files']:
            self.add_log_message(f"  无效文件: {', '.join(analysis['invalid_files'])}")
            
        # 启用处理按钮
        self.process_btn.setEnabled(analysis['delete_count'] > 0)
        self.status_changed.emit(f"分析完成，找到 {analysis['delete_count']} 个可删除文件")
        
    def update_analysis_table(self, analysis):
        """更新分析表格"""
        self.analysis_table.setRowCount(0)
        
        if "groups" not in analysis:
            return
            
        row = 0
        for group_key, infos in analysis["groups"].items():
            for info in infos:
                self.analysis_table.insertRow(row)
                
                # 文件名
                self.analysis_table.setItem(row, 0, QTableWidgetItem(info.filename))
                
                # 类别
                self.analysis_table.setItem(row, 1, QTableWidgetItem(info.category))
                
                # 描述
                self.analysis_table.setItem(row, 2, QTableWidgetItem(info.description))
                
                # 版本
                self.analysis_table.setItem(row, 3, QTableWidgetItem(info.version))
                
                # 日期
                self.analysis_table.setItem(row, 4, QTableWidgetItem(info.date))
                
                # 状态
                status = "保留" if info.filename in analysis["latest_files"] else "删除"
                status_item = QTableWidgetItem(status)
                if status == "删除":
                    status_item.setBackground(Qt.GlobalColor.red)
                    status_item.setForeground(Qt.GlobalColor.white)
                else:
                    status_item.setBackground(Qt.GlobalColor.green)
                    status_item.setForeground(Qt.GlobalColor.white)
                    
                self.analysis_table.setItem(row, 5, status_item)
                row += 1

    def process_files(self):
        """处理文件"""
        directory = self.dir_entry.text().strip()
        if not directory:
            QMessageBox.warning(self, "警告", "请先选择目录")
            return

        # 获取选项
        dry_run = self.dry_run_cb.isChecked()
        backup_files = self.backup_cb.isChecked()
        delete_invalid = self.delete_invalid_cb.isChecked()

        # 确认对话框
        if self.confirm_delete_cb.isChecked() and not dry_run:
            confirm_text = "确定要删除旧版本的压缩包文件吗？\n此操作不可撤销！"
            if delete_invalid:
                confirm_text += "\n\n同时会删除无效文件（不符合命名规范的文件）"
            if backup_files:
                confirm_text += "\n\n删除前会先备份所有文件到backup文件夹"

            reply = QMessageBox.question(
                self, "确认删除",
                confirm_text,
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            if reply != QMessageBox.StandardButton.Yes:
                return

        # 禁用按钮
        self.set_buttons_enabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 不确定进度

        # 创建工作线程
        self.worker = ArchiveProcessorWorker(directory, dry_run, backup_files, delete_invalid)
        self.worker.log_message.connect(self.add_log_message)
        self.worker.processing_finished.connect(self.on_processing_finished)
        self.worker.start()

        mode_text = "试运行" if dry_run else "处理"
        self.status_changed.emit(f"正在{mode_text}文件...")

    def on_processing_finished(self, success, message):
        """处理完成回调"""
        self.progress_bar.setVisible(False)
        self.set_buttons_enabled(True)

        if success:
            self.status_changed.emit(message)
            # 重新分析目录以更新显示
            QTimer.singleShot(1000, self.analyze_directory)
        else:
            self.status_changed.emit("处理失败")
            QMessageBox.critical(self, "错误", message)

        # 清理工作线程
        if self.worker:
            self.worker.deleteLater()
            self.worker = None

    def set_buttons_enabled(self, enabled):
        """设置按钮启用状态"""
        self.browse_btn.setEnabled(enabled)
        self.analyze_btn.setEnabled(enabled)
        self.process_btn.setEnabled(enabled)

    def add_log_message(self, message):
        """添加日志消息"""
        self.log_text.append(message)
        # 自动滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.log_text.setTextCursor(cursor)

    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
        self.status_changed.emit("日志已清空")

    def load_settings(self):
        """加载设置"""
        try:
            # 安全地断开信号连接，避免在加载时触发保存
            try:
                self.dry_run_cb.stateChanged.disconnect()
            except:
                pass
            try:
                self.confirm_delete_cb.stateChanged.disconnect()
            except:
                pass
            try:
                self.backup_cb.stateChanged.disconnect()
            except:
                pass
            try:
                self.delete_invalid_cb.stateChanged.disconnect()
            except:
                pass

            # 加载勾选框状态，使用配置文件中的实际值
            self.dry_run_cb.setChecked(
                self.config_manager.get_config("archive_processor.dry_run", False))
            self.confirm_delete_cb.setChecked(
                self.config_manager.get_config("archive_processor.confirm_delete", False))
            self.backup_cb.setChecked(
                self.config_manager.get_config("archive_processor.backup_files", False))
            self.delete_invalid_cb.setChecked(
                self.config_manager.get_config("archive_processor.delete_invalid", False))

            # 连接信号（确保连接）
            self.dry_run_cb.stateChanged.connect(self.save_settings)
            self.confirm_delete_cb.stateChanged.connect(self.save_settings)
            self.backup_cb.stateChanged.connect(self.save_settings)
            self.delete_invalid_cb.stateChanged.connect(self.save_settings)

            # 加载上次使用的目录
            last_dir = self.config_manager.get_config("paths.last_archive_dir", "")
            if last_dir and os.path.exists(last_dir):
                self.dir_entry.setText(last_dir)

            print(f"加载设置完成: dry_run={self.dry_run_cb.isChecked()}, confirm_delete={self.confirm_delete_cb.isChecked()}, backup_files={self.backup_cb.isChecked()}, delete_invalid={self.delete_invalid_cb.isChecked()}")

        except Exception as e:
            print(f"加载设置失败: {e}")

    def save_settings(self):
        """保存设置"""
        try:
            # 保存勾选框状态
            self.config_manager.set_config("archive_processor.dry_run",
                                         self.dry_run_cb.isChecked())
            self.config_manager.set_config("archive_processor.confirm_delete",
                                         self.confirm_delete_cb.isChecked())
            self.config_manager.set_config("archive_processor.backup_files",
                                         self.backup_cb.isChecked())
            self.config_manager.set_config("archive_processor.delete_invalid",
                                         self.delete_invalid_cb.isChecked())

            # 保存当前目录
            current_dir = self.dir_entry.text().strip()
            if current_dir:
                self.config_manager.set_config("paths.last_archive_dir", current_dir)

            # 保存到文件
            self.config_manager.save_user_config()

        except Exception as e:
            print(f"保存设置失败: {e}")

    def setup_drag_drop(self):
        """设置拖拽支持"""
        self.setAcceptDrops(True)

    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            # 检查是否包含文件夹
            urls = event.mimeData().urls()
            for url in urls:
                if url.isLocalFile():
                    path = url.toLocalFile()
                    if os.path.isdir(path):
                        event.acceptProposedAction()
                        return
        event.ignore()

    def dropEvent(self, event: QDropEvent):
        """拖拽放下事件"""
        if event.mimeData().hasUrls():
            urls = event.mimeData().urls()
            for url in urls:
                if url.isLocalFile():
                    path = url.toLocalFile()
                    if os.path.isdir(path):
                        self.dir_entry.setText(path)
                        self.status_changed.emit(f"已设置目录: {path}")
                        event.acceptProposedAction()
                        return
        event.ignore()
