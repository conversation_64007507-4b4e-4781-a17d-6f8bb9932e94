{% extends "admin/base.html" %}

{% block title %}仪表板 - 激活码管理系统{% endblock %}
{% block page_title %}仪表板{% endblock %}

{% block content %}
<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            总激活码数量
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats[0] or 0 }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-key fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            活跃激活码
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats[1] or 0 }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            已使用激活码
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats[2] or 0 }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-shield-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            过期/禁用
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ (stats[3] or 0) + (stats[4] or 0) }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-exclamation-triangle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最近的激活码 -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">最近创建的激活码</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>激活码</th>
                        <th>类型</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>过期时间</th>
                        <th>使用情况</th>
                    </tr>
                </thead>
                <tbody>
                    {% for code in recent_codes %}
                    <tr>
                        <td class="code-display">{{ code[0] }}</td>
                        <td>
                            {% if code[1] == 'trial' %}
                                <span class="badge bg-info">试用版</span>
                            {% elif code[1] == 'standard' %}
                                <span class="badge bg-primary">标准版</span>
                            {% elif code[1] == 'professional' %}
                                <span class="badge bg-success">专业版</span>
                            {% elif code[1] == 'enterprise' %}
                                <span class="badge bg-warning">企业版</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if code[4] == 'active' %}
                                <span class="badge bg-success">活跃</span>
                            {% elif code[4] == 'used' %}
                                <span class="badge bg-info">已使用</span>
                            {% elif code[4] == 'expired' %}
                                <span class="badge bg-warning">已过期</span>
                            {% elif code[4] == 'disabled' %}
                                <span class="badge bg-danger">已禁用</span>
                            {% endif %}
                        </td>
                        <td>{{ code[2] }}</td>
                        <td>{{ code[3] }}</td>
                        <td>
                            {% if code[5] %}
                                <small class="text-muted">
                                    机器ID: {{ code[5][:8] }}...<br>
                                    激活时间: {{ code[6] }}
                                </small>
                            {% else %}
                                <span class="text-muted">未使用</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 快速操作 -->
<div class="row">
    <div class="col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">快速操作</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-primary" onclick="generateCodes()">
                        <i class="bi bi-plus-circle"></i> 生成新激活码
                    </button>
                    <a href="{{ url_for('admin.manage_codes') }}" class="btn btn-outline-primary">
                        <i class="bi bi-key"></i> 管理所有激活码
                    </a>
                    <a href="{{ url_for('admin.active_licenses') }}" class="btn btn-outline-success">
                        <i class="bi bi-shield-check"></i> 查看活跃许可证
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">系统信息</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-sm-6">
                        <p><strong>服务器时间:</strong><br>
                        <span id="server-time">{{ moment().format('YYYY-MM-DD HH:mm:ss') }}</span></p>
                    </div>
                    <div class="col-sm-6">
                        <p><strong>在线状态:</strong><br>
                        <span class="badge bg-success">正常运行</span></p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-6">
                        <p><strong>数据库状态:</strong><br>
                        <span class="badge bg-success">连接正常</span></p>
                    </div>
                    <div class="col-sm-6">
                        <p><strong>API状态:</strong><br>
                        <span class="badge bg-success">服务正常</span></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 更新服务器时间
    function updateServerTime() {
        const now = new Date();
        document.getElementById('server-time').textContent = 
            now.getFullYear() + '-' + 
            String(now.getMonth() + 1).padStart(2, '0') + '-' + 
            String(now.getDate()).padStart(2, '0') + ' ' + 
            String(now.getHours()).padStart(2, '0') + ':' + 
            String(now.getMinutes()).padStart(2, '0') + ':' + 
            String(now.getSeconds()).padStart(2, '0');
    }
    
    // 每秒更新时间
    setInterval(updateServerTime, 1000);
    updateServerTime();
</script>
{% endblock %}
