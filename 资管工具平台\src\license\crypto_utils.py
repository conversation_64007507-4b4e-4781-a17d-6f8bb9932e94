#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
加密工具模块
"""

import os
import hashlib
import base64
import json
from typing import Optional, Dict, Any
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC


class CryptoUtils:
    """加密工具类"""
    
    def __init__(self):
        self._master_key = None
        self._private_key = None
        self._public_key = None
    
    def generate_master_key(self, password: str, salt: Optional[bytes] = None) -> bytes:
        """生成主密钥"""
        if salt is None:
            salt = os.urandom(16)
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        return key
    
    def encrypt_data(self, data: str, key: bytes) -> str:
        """使用对称加密加密数据"""
        f = Fernet(key)
        encrypted_data = f.encrypt(data.encode())
        return base64.urlsafe_b64encode(encrypted_data).decode()
    
    def decrypt_data(self, encrypted_data: str, key: bytes) -> Optional[str]:
        """使用对称加密解密数据"""
        try:
            f = Fernet(key)
            decoded_data = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_data = f.decrypt(decoded_data)
            return decrypted_data.decode()
        except Exception:
            return None
    
    def generate_rsa_keys(self) -> tuple:
        """生成RSA密钥对"""
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048,
        )
        public_key = private_key.public_key()
        
        # 序列化私钥
        private_pem = private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption()
        )
        
        # 序列化公钥
        public_pem = public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )
        
        return private_pem, public_pem
    
    def sign_data(self, data: str, private_key_pem: bytes) -> str:
        """使用私钥签名数据"""
        private_key = serialization.load_pem_private_key(
            private_key_pem,
            password=None,
        )
        
        signature = private_key.sign(
            data.encode(),
            padding.PSS(
                mgf=padding.MGF1(hashes.SHA256()),
                salt_length=padding.PSS.MAX_LENGTH
            ),
            hashes.SHA256()
        )
        
        return base64.urlsafe_b64encode(signature).decode()
    
    def verify_signature(self, data: str, signature: str, public_key_pem: bytes) -> bool:
        """使用公钥验证签名"""
        try:
            public_key = serialization.load_pem_public_key(public_key_pem)
            signature_bytes = base64.urlsafe_b64decode(signature.encode())
            
            public_key.verify(
                signature_bytes,
                data.encode(),
                padding.PSS(
                    mgf=padding.MGF1(hashes.SHA256()),
                    salt_length=padding.PSS.MAX_LENGTH
                ),
                hashes.SHA256()
            )
            return True
        except Exception:
            return False
    
    def hash_data(self, data: str, algorithm: str = "sha256") -> str:
        """计算数据哈希值"""
        if algorithm == "sha256":
            return hashlib.sha256(data.encode()).hexdigest()
        elif algorithm == "md5":
            return hashlib.md5(data.encode()).hexdigest()
        else:
            raise ValueError(f"Unsupported hash algorithm: {algorithm}")
    
    def generate_random_string(self, length: int = 32) -> str:
        """生成随机字符串"""
        return base64.urlsafe_b64encode(os.urandom(length))[:length].decode()


class SecureStorage:
    """安全存储类"""
    
    def __init__(self, storage_path: str, master_password: str):
        self.storage_path = storage_path
        self.crypto = CryptoUtils()
        self.master_key = self._get_or_create_master_key(master_password)
    
    def _get_or_create_master_key(self, password: str) -> bytes:
        """获取或创建主密钥"""
        key_file = self.storage_path + ".key"
        
        if os.path.exists(key_file):
            # 读取现有密钥
            with open(key_file, 'rb') as f:
                salt = f.read(16)
                return self.crypto.generate_master_key(password, salt)
        else:
            # 创建新密钥
            salt = os.urandom(16)
            key = self.crypto.generate_master_key(password, salt)
            
            # 保存salt
            os.makedirs(os.path.dirname(key_file), exist_ok=True)
            with open(key_file, 'wb') as f:
                f.write(salt)
            
            return key
    
    def save_data(self, data: Dict[str, Any]) -> bool:
        """保存加密数据"""
        try:
            json_data = json.dumps(data, ensure_ascii=False, indent=2)
            encrypted_data = self.crypto.encrypt_data(json_data, self.master_key)
            
            os.makedirs(os.path.dirname(self.storage_path), exist_ok=True)
            with open(self.storage_path, 'w', encoding='utf-8') as f:
                f.write(encrypted_data)
            
            return True
        except Exception as e:
            print(f"保存数据失败: {e}")
            return False
    
    def load_data(self) -> Optional[Dict[str, Any]]:
        """加载解密数据"""
        try:
            if not os.path.exists(self.storage_path):
                return None
            
            with open(self.storage_path, 'r', encoding='utf-8') as f:
                encrypted_data = f.read()
            
            json_data = self.crypto.decrypt_data(encrypted_data, self.master_key)
            if json_data is None:
                return None
            
            return json.loads(json_data)
        except Exception as e:
            print(f"加载数据失败: {e}")
            return None
    
    def delete_data(self) -> bool:
        """删除存储的数据"""
        try:
            if os.path.exists(self.storage_path):
                os.remove(self.storage_path)
            
            key_file = self.storage_path + ".key"
            if os.path.exists(key_file):
                os.remove(key_file)
            
            return True
        except Exception:
            return False


def generate_hardware_id() -> str:
    """生成硬件指纹"""
    import platform
    import uuid
    
    # 获取系统信息
    system_info = {
        'platform': platform.platform(),
        'processor': platform.processor(),
        'machine': platform.machine(),
        'node': platform.node(),
    }
    
    # 获取MAC地址
    try:
        mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                       for elements in range(0, 2*6, 2)][::-1])
        system_info['mac'] = mac
    except:
        system_info['mac'] = 'unknown'
    
    # 生成硬件ID
    info_string = json.dumps(system_info, sort_keys=True)
    hardware_id = hashlib.sha256(info_string.encode()).hexdigest()
    
    return hardware_id[:32].upper()  # 返回32位大写硬件ID
