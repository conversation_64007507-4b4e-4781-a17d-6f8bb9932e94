#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
版本管理工具
"""

import json
import os
import hashlib
from datetime import datetime
from pathlib import Path

VERSION_INFO_FILE = Path(__file__).parent / "version_info.json"
FILES_DIR = Path(__file__).parent / "files"

def load_version_info():
    """加载版本信息"""
    if VERSION_INFO_FILE.exists():
        with open(VERSION_INFO_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    return {}

def save_version_info(data):
    """保存版本信息"""
    with open(VERSION_INFO_FILE, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

def calculate_file_hash(file_path):
    """计算文件哈希"""
    sha256_hash = hashlib.sha256()
    with open(file_path, "rb") as f:
        for byte_block in iter(lambda: f.read(4096), b""):
            sha256_hash.update(byte_block)
    return sha256_hash.hexdigest()

def add_version():
    """添加新版本"""
    print("📦 添加新版本")
    print("-" * 30)
    
    # 获取版本信息
    version = input("版本号 (如 1.0.2): ").strip()
    if not version:
        print("❌ 版本号不能为空")
        return
    
    channel = input("更新渠道 (stable/beta/alpha) [stable]: ").strip() or "stable"
    if channel not in ["stable", "beta", "alpha"]:
        print("❌ 无效的更新渠道")
        return
    
    update_type = input("更新类型 (patch/minor/major/hotfix) [minor]: ").strip() or "minor"
    if update_type not in ["patch", "minor", "major", "hotfix"]:
        print("❌ 无效的更新类型")
        return
    
    filename = input(f"文件名 [update_v{version}.zip]: ").strip() or f"update_v{version}.zip"
    
    changelog = input("更新日志 (多行用\\n分隔): ").strip()
    if not changelog:
        changelog = f"• 版本 {version} 更新\n• 修复已知问题\n• 性能优化"
    else:
        changelog = changelog.replace('\\n', '\n')
    
    required = input("是否强制更新 (y/N): ").strip().lower() == 'y'
    
    # 检查文件是否存在
    file_path = FILES_DIR / filename
    if not file_path.exists():
        print(f"⚠️ 文件 {filename} 不存在，将创建模拟文件")
        FILES_DIR.mkdir(exist_ok=True)
        with open(file_path, 'wb') as f:
            f.write(f"Mock update file for version {version}\n".encode())
            f.write(f"Channel: {channel}\n".encode())
            f.write(f"Type: {update_type}\n".encode())
            f.write(f"Created: {datetime.now().isoformat()}\n".encode())
    
    # 计算文件信息
    file_size = os.path.getsize(file_path)
    file_hash = calculate_file_hash(file_path)
    
    # 创建版本信息
    version_info = {
        "version": version,
        "build_number": int(version.replace('.', '')) * 10,
        "release_date": datetime.now().isoformat(),
        "channel": channel,
        "update_type": update_type,
        "changelog": changelog,
        "filename": filename,
        "file_size": file_size,
        "file_hash": file_hash,
        "required": required,
        "signature": f"mock_signature_{hash(version + channel)}"
    }
    
    # 加载现有版本信息
    all_versions = load_version_info()
    all_versions[channel] = version_info
    
    # 保存版本信息
    save_version_info(all_versions)
    
    print(f"✅ 版本 {version} ({channel}) 添加成功")
    print(f"📁 文件: {filename} ({file_size} 字节)")
    print(f"🔒 哈希: {file_hash[:16]}...")

def list_versions():
    """列出所有版本"""
    print("📋 当前版本列表")
    print("-" * 50)
    
    versions = load_version_info()
    
    if not versions:
        print("❌ 暂无版本信息")
        return
    
    for channel, info in versions.items():
        print(f"\n🔸 {channel.upper()} 渠道:")
        print(f"  版本: {info.get('version', 'N/A')}")
        print(f"  类型: {info.get('update_type', 'N/A')}")
        print(f"  发布: {info.get('release_date', 'N/A')[:19]}")

        filename = info.get('filename', 'N/A')
        file_size = info.get('file_size', 0)
        print(f"  文件: {filename} ({file_size} 字节)")

        print(f"  强制: {'是' if info.get('required', False) else '否'}")
        changelog = info.get('changelog', '无更新日志')
        print(f"  更新日志: {changelog[:50]}...")

def delete_version():
    """删除版本"""
    print("🗑️ 删除版本")
    print("-" * 30)
    
    versions = load_version_info()
    
    if not versions:
        print("❌ 暂无版本信息")
        return
    
    print("可用渠道:")
    for channel in versions.keys():
        print(f"  - {channel}")
    
    channel = input("选择要删除的渠道: ").strip()
    
    if channel not in versions:
        print("❌ 渠道不存在")
        return
    
    version_info = versions[channel]
    print(f"将删除: {channel} - {version_info['version']}")
    
    confirm = input("确认删除? (y/N): ").strip().lower()
    if confirm == 'y':
        # 删除文件
        file_path = FILES_DIR / version_info['filename']
        if file_path.exists():
            file_path.unlink()
            print(f"✅ 已删除文件: {version_info['filename']}")
        
        # 删除版本信息
        del versions[channel]
        save_version_info(versions)
        
        print(f"✅ 已删除版本: {channel} - {version_info['version']}")
    else:
        print("❌ 取消删除")

def show_server_info():
    """显示服务器信息"""
    print("🌐 服务器信息")
    print("-" * 30)
    print(f"📍 服务器地址: http://127.0.0.1:8080")
    print(f"📁 文件目录: {FILES_DIR}")
    print(f"📄 版本文件: {VERSION_INFO_FILE}")
    print(f"🔗 API端点:")
    print(f"  - GET  /api/status")
    print(f"  - GET  /api/version/check")
    print(f"  - GET  /api/download/<filename>")
    print(f"  - POST /api/admin/version")

def main():
    """主菜单"""
    while True:
        print("\n" + "=" * 50)
        print("🛠️ 更新服务器版本管理工具")
        print("=" * 50)
        print("1. 📦 添加新版本")
        print("2. 📋 列出所有版本")
        print("3. 🗑️ 删除版本")
        print("4. 🌐 显示服务器信息")
        print("5. 🚪 退出")
        print("-" * 50)
        
        choice = input("请选择操作 (1-5): ").strip()
        
        if choice == '1':
            add_version()
        elif choice == '2':
            list_versions()
        elif choice == '3':
            delete_version()
        elif choice == '4':
            show_server_info()
        elif choice == '5':
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重试")

if __name__ == "__main__":
    main()
