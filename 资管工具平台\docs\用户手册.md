# 资管工具平台用户手册

## 目录
1. [快速开始](#快速开始)
2. [压缩包处理工具](#压缩包处理工具)
3. [整包工具](#整包工具)
4. [载包上传工具](#载包上传工具)
5. [常见问题](#常见问题)

## 快速开始

### 系统要求
- Windows 10 或更高版本
- Python 3.8 或更高版本
- 至少 4GB 内存
- 1GB 可用磁盘空间

### 安装步骤
1. 确保已安装 Python 3.8+
2. 双击运行 `启动资管工具平台.bat`
3. 首次运行会自动安装依赖包
4. 等待应用程序启动

### 界面概览
应用程序采用选项卡式设计，包含三个主要功能模块：
- **压缩包处理** - 管理压缩包版本
- **整包工具** - 文件合并和整合
- **载包上传** - SVN检出和文件上传

## 压缩包处理工具

### 功能说明
压缩包处理工具用于智能管理压缩包文件，自动识别版本并保留最新版本。

### 支持的文件格式
- `.zip` 压缩包
- `.rar` 压缩包

### 文件命名规范
文件名必须符合以下格式：
```
YYYYMMDD【手机魔域】[类别]描述(额外信息)vX.X(作者).zip
```

示例：
```
20250130【手机魔域】[任务包]新年活动v1.0(张三).zip
```

### 使用步骤
1. 点击"浏览"按钮选择包含压缩包的目录
2. 点击"分析目录"查看文件分析结果
3. 在分析表格中查看哪些文件将被保留或删除
4. 选择是否启用"试运行模式"（推荐首次使用）
5. 点击"开始处理"执行操作

### 注意事项
- 建议首次使用时启用"试运行模式"
- 处理前请确保重要文件已备份
- 只有符合命名规范的文件才会被处理

## 整包工具

### 功能说明
整包工具用于合并原始资源文件和增量文件（+文件），支持15种不同的合并规则。

### 支持的文件类型
- `.ini` 配置文件
- `.txt` 文本文件
- `.csv` 数据文件
- `.xml` XML文件
- `.ani` 动画文件
- `.size` 尺寸文件

### 合并规则说明
工具支持15种不同的合并规则，每种规则适用于不同类型的文件：

1. **规则1** - 按[xxx]标识且不排序
2. **规则2** - 按第一位ID排序
3. **规则3** - 按=前的ID排序
4. **规则4** - 保留注释的段落处理
5. **规则5** - 按[xxx]标识并排序
6. **规则6** - 带注释的段落排序
7. **规则7** - 直接替换（不合并）
8. **规则8** - 按段落和key查重追加
9. **规则9** - task.xml特殊处理
10. **规则10** - 数字排序
11. **规则11** - replaceaction.xml特殊处理
12. **规则12** - 其他XML文件处理
13. **规则13** - IDRange值追加
14. **规则14** - str类文件处理
15. **规则15** - 军团图鉴文件处理

### 使用步骤
1. 选择源目录（原始资源目录）
2. 选择+目录（包含增量文件的目录）
3. 选择输出目录（合并结果保存位置）
4. 可选：选择-目录（包含删除文件的目录）
5. 选择文件类型（ini 或 ani）
6. 如果处理ini_x类型文件，填写ini_x名称
7. 点击"验证配置"检查设置
8. 点击"开始合并"执行操作

### 注意事项
- 确保源目录和+目录结构正确
- 合并前建议备份原始文件
- 不同文件类型使用不同的合并规则

## 载包上传工具

### 功能说明
载包上传工具提供SVN检出和文件压缩上传功能。

### SVN检出功能
1. 在"SVN检出"选项卡中操作
2. 输入SVN仓库地址
3. 选择检出目录
4. 点击"开始检出"

#### 自动处理功能
检出完成后，工具会自动：
- 创建规范的文件夹结构
- 移动+script和+ui文件夹到"客户端更新"目录
- 删除.svn文件夹和.log文件
- 创建"修改说明.txt"文件

### 文件上传功能
1. 在"文件上传"选项卡中操作
2. 选择要上传的文件夹
3. 点击"压缩并上传"
4. 系统会自动压缩为7z格式并上传到指定目录

### 注意事项
- 确保已安装SVN客户端
- 确保已安装py7zr库（用于7z压缩）
- 上传目录需要有写入权限

## 常见问题

### Q: 应用程序无法启动
A: 请检查：
1. Python版本是否为3.8+
2. 是否已安装所需依赖包
3. 运行`pip install -r requirements.txt`安装依赖

### Q: SVN检出失败
A: 请检查：
1. SVN客户端是否已安装
2. SVN地址是否正确
3. 网络连接是否正常
4. 是否有访问权限

### Q: 压缩包处理没有找到文件
A: 请检查：
1. 文件名是否符合命名规范
2. 目录路径是否正确
3. 文件是否为.zip或.rar格式

### Q: 整包工具合并失败
A: 请检查：
1. 源目录和+目录结构是否正确
2. 文件编码是否支持
3. 是否有足够的磁盘空间
4. 输出目录是否有写入权限

### Q: 上传功能不可用
A: 请检查：
1. 是否已安装py7zr库：`pip install py7zr`
2. 上传目录是否存在
3. 是否有网络驱动器访问权限

## 技术支持

如遇到其他问题，请联系技术支持团队。

---
*资管工具平台 v1.0.0*  
*资源管理部 © 2025*
