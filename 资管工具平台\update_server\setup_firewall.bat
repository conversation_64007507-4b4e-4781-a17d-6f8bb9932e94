@echo off
chcp 65001 >nul
title 配置防火墙 - 允许更新服务器

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    配置防火墙 - 允许更新服务器                ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔥 正在配置Windows防火墙...
echo.

echo 📝 添加入站规则 - 允许端口8080...
netsh advfirewall firewall add rule name="资管工具更新服务器-入站" dir=in action=allow protocol=TCP localport=8080
if errorlevel 1 (
    echo ❌ 添加入站规则失败，请以管理员身份运行
) else (
    echo ✅ 入站规则添加成功
)

echo.
echo 📝 添加出站规则 - 允许端口8080...
netsh advfirewall firewall add rule name="资管工具更新服务器-出站" dir=out action=allow protocol=TCP localport=8080
if errorlevel 1 (
    echo ❌ 添加出站规则失败，请以管理员身份运行
) else (
    echo ✅ 出站规则添加成功
)

echo.
echo 📝 检查防火墙规则...
netsh advfirewall firewall show rule name="资管工具更新服务器-入站"
netsh advfirewall firewall show rule name="资管工具更新服务器-出站"

echo.
echo 🎯 防火墙配置完成！
echo.
echo 📋 如果仍然无法访问，请手动配置:
echo 1. 打开 Windows Defender 防火墙
echo 2. 点击 "允许应用或功能通过 Windows Defender 防火墙"
echo 3. 点击 "更改设置" 按钮
echo 4. 点击 "允许其他应用..." 按钮
echo 5. 浏览并选择 Python.exe
echo 6. 确保勾选 "专用" 和 "公用" 网络
echo.

pause
