# 🔑 激活码管理系统完整指南

## 📋 系统概述

这是一个完整的激活码管理系统，包含：
- **Web管理界面** - 可视化管理激活码
- **REST API** - 客户端验证接口
- **客户端SDK** - 简单易用的验证库
- **数据库存储** - 完整的数据持久化

## 🚀 快速开始

### 1. 启动服务端
```bash
cd 资管工具平台服务端
python src/app.py
```

### 2. 访问管理界面
- **地址**: http://localhost:5000/admin
- **用户名**: admin
- **密码**: admin123

### 3. 客户端验证
```python
from license.license_validator import LicenseValidator

validator = LicenseValidator("http://localhost:5000")
success, result = validator.validate_activation_code("DEMO-1234-5678-ABCD")
```

## 🎯 管理界面功能

### 📊 仪表板
- **统计概览** - 总数、活跃、已用、过期激活码数量
- **最近活动** - 最新创建的激活码和激活记录
- **系统状态** - 服务器运行状态和数据库连接
- **快速操作** - 一键生成激活码和管理入口

### 🔑 激活码管理
- **批量生成** - 支持生成1-100个激活码
- **类型选择** - 试用版、标准版、专业版、企业版
- **参数配置** - 设备数量限制、有效期、备注信息
- **状态控制** - 启用/禁用/延期激活码
- **搜索过滤** - 按状态、类型筛选激活码

### 🛡️ 许可证监控
- **活跃设备** - 查看所有已激活的设备
- **设备信息** - 机器ID、激活时间、最后检查时间
- **使用统计** - 每个激活码的设备使用情况

## 🔧 API接口说明

### 验证激活码
```http
POST /api/license/validate
Content-Type: application/json
X-API-Key: your-api-key

{
  "activation_code": "DEMO-1234-5678-ABCD",
  "machine_id": "ABC123DEF456",
  "machine_info": {
    "platform": "Windows-10",
    "processor": "Intel64 Family 6 Model 142"
  }
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "激活成功",
  "license_info": {
    "activation_code": "DEMO-1234-5678-ABCD",
    "license_type": "trial",
    "expires_at": "2024-09-03T23:59:59",
    "max_devices": 1,
    "current_devices": 1
  }
}
```

### 检查许可证状态
```http
POST /api/license/status
Content-Type: application/json
X-API-Key: your-api-key

{
  "activation_code": "DEMO-1234-5678-ABCD",
  "machine_id": "ABC123DEF456"
}
```

### 停用许可证
```http
POST /api/license/deactivate
Content-Type: application/json
X-API-Key: your-api-key

{
  "activation_code": "DEMO-1234-5678-ABCD",
  "machine_id": "ABC123DEF456"
}
```

## 💻 客户端集成

### 基本使用
```python
from license.license_validator import LicenseValidator

# 初始化验证器
validator = LicenseValidator(
    server_url="http://your-server.com:5000",
    api_key="your-api-key"
)

# 验证激活码
success, result = validator.validate_activation_code("DEMO-1234-5678-ABCD")
if success:
    print("激活成功!")
    license_info = result['license_info']
    print(f"许可证类型: {license_info['license_type']}")
    print(f"过期时间: {license_info['expires_at']}")
else:
    print(f"激活失败: {result['message']}")

# 检查许可证状态
success, result = validator.check_license_status()
if success:
    if result.get('offline_mode'):
        print("离线模式 - 使用本地许可证")
    else:
        print("在线验证成功")
else:
    print(f"验证失败: {result['message']}")
```

### 高级功能
```python
# 获取本地许可证信息
local_info = validator.get_local_license_info()
if local_info:
    print(f"本地激活码: {local_info['activation_code']}")
    print(f"机器ID: {local_info['machine_id']}")

# 停用许可证
success, result = validator.deactivate_license()
if success:
    print("许可证已停用")
```

## 🛡️ 安全特性

### 硬件绑定
- **机器ID生成** - 基于硬件信息生成唯一标识
- **多因子识别** - 平台、处理器、MAC地址等
- **防克隆保护** - 同一激活码不能在多台机器使用

### 设备限制
- **数量控制** - 每个激活码可设置最大设备数
- **实时监控** - 跟踪每个激活码的使用情况
- **超限保护** - 超过设备数量限制时拒绝激活

### 时效管理
- **过期控制** - 支持设置激活码有效期
- **自动过期** - 过期后自动禁用
- **延期功能** - 管理员可延长有效期

### 状态管理
- **多种状态** - active(活跃)、used(已用)、expired(过期)、disabled(禁用)
- **状态转换** - 支持管理员手动修改状态
- **批量操作** - 支持批量启用/禁用激活码

## 📊 数据库结构

### activation_codes (激活码表)
- `code` - 激活码
- `license_type` - 许可证类型
- `max_devices` - 最大设备数
- `expires_at` - 过期时间
- `status` - 状态
- `notes` - 备注

### active_licenses (活跃许可证表)
- `activation_code` - 激活码
- `machine_id` - 机器ID
- `machine_info` - 机器信息
- `activated_at` - 激活时间
- `last_check` - 最后检查时间

### license_check_logs (检查日志表)
- `activation_code` - 激活码
- `machine_id` - 机器ID
- `check_time` - 检查时间
- `status` - 检查结果
- `ip_address` - IP地址

## 🔧 部署配置

### 服务端配置
```yaml
# config/config.yaml
app:
  host: "0.0.0.0"
  port: 5000
  debug: false

security:
  api_keys:
    client: "your-client-api-key"
    admin: "your-admin-api-key"

database:
  license_db: "data/database/license.db"
```

### 客户端配置
```json
{
  "license_server": "https://your-server.com:5000",
  "api_key": "your-client-api-key",
  "check_interval": 3600,
  "offline_grace_period": 86400
}
```

## 🎯 使用场景

### 软件授权
- **试用版控制** - 30天试用期限制
- **版本区分** - 不同功能的许可证类型
- **设备管理** - 控制软件安装数量

### 企业部署
- **批量激活** - 企业版支持多设备激活
- **集中管理** - 统一的激活码管理平台
- **使用监控** - 实时监控软件使用情况

### SaaS服务
- **订阅管理** - 基于时间的许可证控制
- **用户绑定** - 每个用户独立的激活码
- **自动续费** - 支持许可证延期

## 📞 技术支持

### 常见问题
1. **激活失败** - 检查网络连接和激活码有效性
2. **设备超限** - 联系管理员增加设备数量或停用旧设备
3. **许可证过期** - 联系管理员延长有效期

### 错误代码
- `invalid_code` - 激活码不存在
- `code_expired` - 激活码已过期
- `device_limit_exceeded` - 设备数量超限
- `network_error` - 网络连接失败

### 日志查看
- **服务端日志** - `data/logs/license.log`
- **客户端日志** - 程序运行日志
- **数据库日志** - `license_check_logs` 表

---

## ✅ 系统已就绪

激活码管理系统现在已经完全配置好，包括：
- ✅ Web管理界面
- ✅ REST API接口
- ✅ 客户端验证库
- ✅ 数据库存储
- ✅ 安全保护机制

现在您可以：
1. 启动服务端开始管理激活码
2. 在客户端集成验证功能
3. 通过Web界面可视化管理
4. 监控许可证使用情况
