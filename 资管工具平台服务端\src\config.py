#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any


class Config:
    """配置基类"""
    
    def __init__(self, config_name='production'):
        self.config_name = config_name
        self.config_data = self._load_config()
        self._setup_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        config_file = Path(__file__).parent.parent / 'config' / 'config.yaml'
        
        if not config_file.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_file}")
        
        with open(config_file, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def _setup_config(self):
        """设置配置属性"""
        # 应用配置
        app_config = self.config_data.get('app', {})
        self.APP_NAME = app_config.get('name', '资管工具平台服务端')
        self.APP_VERSION = app_config.get('version', '1.0.0')
        self.DEBUG = app_config.get('debug', False)
        self.HOST = app_config.get('host', '0.0.0.0')
        self.PORT = app_config.get('port', 5000)
        self.SECRET_KEY = os.getenv('SECRET_KEY', app_config.get('secret_key'))
        
        # 数据库配置
        db_config = self.config_data.get('database', {})
        self.SQLALCHEMY_DATABASE_URI = os.getenv('DATABASE_URL', db_config.get('url'))
        self.SQLALCHEMY_ECHO = db_config.get('echo', False)
        self.SQLALCHEMY_TRACK_MODIFICATIONS = False
        self.SQLALCHEMY_ENGINE_OPTIONS = {
            'pool_size': db_config.get('pool_size', 10),
            'max_overflow': db_config.get('max_overflow', 20),
            'pool_timeout': db_config.get('pool_timeout', 30),
            'pool_recycle': db_config.get('pool_recycle', 3600)
        }
        
        # Redis配置
        redis_config = self.config_data.get('redis', {})
        self.REDIS_HOST = redis_config.get('host', 'localhost')
        self.REDIS_PORT = redis_config.get('port', 6379)
        self.REDIS_DB = redis_config.get('db', 0)
        self.REDIS_PASSWORD = redis_config.get('password')
        
        # JWT配置
        jwt_config = self.config_data.get('jwt', {})
        self.JWT_SECRET_KEY = os.getenv('JWT_SECRET', jwt_config.get('secret_key'))
        self.JWT_ALGORITHM = jwt_config.get('algorithm', 'HS256')
        self.JWT_ACCESS_TOKEN_EXPIRES = jwt_config.get('access_token_expires', 3600)
        self.JWT_REFRESH_TOKEN_EXPIRES = jwt_config.get('refresh_token_expires', 604800)
        
        # 激活码服务配置
        license_config = self.config_data.get('license', {})
        self.LICENSE_MASTER_SECRET = license_config.get('master_secret', 'ResourceManagementTool2024')
        self.LICENSE_VALIDATION_CACHE_TTL = license_config.get('validation_cache_ttl', 300)
        self.LICENSE_MAX_DEVICES = license_config.get('max_devices_per_license', 5)
        self.LICENSE_TRIAL_DURATION = license_config.get('trial_duration_days', 30)
        self.LICENSE_GRACE_PERIOD = license_config.get('grace_period_days', 7)
        self.LICENSE_TYPES = license_config.get('types', {})
        
        # 更新服务配置
        update_config = self.config_data.get('update', {})
        self.UPDATE_CACHE_TTL = update_config.get('version_check_cache_ttl', 3600)
        self.UPDATE_RATE_LIMIT = update_config.get('download_rate_limit', '10/minute')
        self.UPDATE_MAX_FILE_SIZE = update_config.get('max_file_size', 104857600)
        self.UPDATE_ALLOWED_EXTENSIONS = update_config.get('allowed_extensions', ['.zip', '.7z'])
        self.UPDATE_CURRENT_VERSION = update_config.get('current_version', '1.0.0')
        self.UPDATE_MINIMUM_VERSION = update_config.get('minimum_version', '0.9.0')
        self.UPDATE_FORCE_BELOW = update_config.get('force_update_below', '0.8.0')
        self.UPDATE_UPLOAD_FOLDER = update_config.get('upload_folder', 'data/uploads')
        self.UPDATE_TEMP_FOLDER = update_config.get('temp_folder', 'data/temp')
        
        # 安全配置
        security_config = self.config_data.get('security', {})
        self.API_KEYS = security_config.get('api_keys', {})
        self.RATE_LIMITS = security_config.get('rate_limits', {})
        self.DEFAULT_RATE_LIMIT = self.RATE_LIMITS.get('default', '100/hour')
        
        # CORS配置
        cors_config = security_config.get('cors', {})
        self.CORS_ORIGINS = cors_config.get('origins', ['*'])
        self.CORS_METHODS = cors_config.get('methods', ['GET', 'POST'])
        self.CORS_HEADERS = cors_config.get('headers', ['Content-Type', 'Authorization'])
        
        # 日志配置
        logging_config = self.config_data.get('logging', {})
        self.LOG_LEVEL = logging_config.get('level', 'INFO')
        self.LOG_FORMAT = logging_config.get('format')
        self.LOG_FILES = logging_config.get('files', {})
        self.LOG_ROTATION = logging_config.get('rotation', '1 day')
        self.LOG_RETENTION = logging_config.get('retention', '30 days')
        
        # 监控配置
        monitoring_config = self.config_data.get('monitoring', {})
        self.MONITORING_ENABLED = monitoring_config.get('enabled', True)
        self.METRICS_ENDPOINT = monitoring_config.get('metrics_endpoint', '/metrics')
        self.HEALTH_ENDPOINT = monitoring_config.get('health_endpoint', '/health')
        
        # 邮件配置
        email_config = self.config_data.get('email', {})
        self.EMAIL_ENABLED = email_config.get('enabled', False)
        self.SMTP_SERVER = email_config.get('smtp_server')
        self.SMTP_PORT = email_config.get('smtp_port', 587)
        self.EMAIL_USERNAME = email_config.get('username')
        self.EMAIL_PASSWORD = email_config.get('password')
        self.EMAIL_USE_TLS = email_config.get('use_tls', True)
        
        # 备份配置
        backup_config = self.config_data.get('backup', {})
        self.BACKUP_ENABLED = backup_config.get('enabled', True)
        self.BACKUP_SCHEDULE = backup_config.get('schedule', '0 2 * * *')
        self.BACKUP_RETENTION = backup_config.get('retention_days', 30)
        self.BACKUP_FOLDER = backup_config.get('backup_folder', 'backups')
        
        # 环境特定配置
        if self.config_name == 'development':
            self._setup_development_config()
        elif self.config_name == 'production':
            self._setup_production_config()
        elif self.config_name == 'testing':
            self._setup_testing_config()
    
    def _setup_development_config(self):
        """开发环境配置"""
        dev_config = self.config_data.get('development', {})
        self.DEBUG = True
        self.SQLALCHEMY_ECHO = True
        self.AUTO_RELOAD = dev_config.get('auto_reload', True)
        self.DEBUG_TOOLBAR = dev_config.get('debug_toolbar', True)
        self.CREATE_TEST_DATA = dev_config.get('create_test_data', True)
    
    def _setup_production_config(self):
        """生产环境配置"""
        prod_config = self.config_data.get('production', {})
        self.DEBUG = False
        self.WORKERS = prod_config.get('workers', 4)
        self.WORKER_CLASS = prod_config.get('worker_class', 'gevent')
        self.WORKER_CONNECTIONS = prod_config.get('worker_connections', 1000)
        self.MAX_REQUESTS = prod_config.get('max_requests', 1000)
        self.TIMEOUT = prod_config.get('timeout', 30)
        
        # SSL配置
        ssl_config = prod_config.get('ssl', {})
        self.SSL_ENABLED = ssl_config.get('enabled', False)
        self.SSL_CERT_FILE = ssl_config.get('cert_file')
        self.SSL_KEY_FILE = ssl_config.get('key_file')
    
    def _setup_testing_config(self):
        """测试环境配置"""
        self.DEBUG = True
        self.TESTING = True
        self.SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
        self.WTF_CSRF_ENABLED = False
    
    def get(self, key: str, default=None):
        """获取配置值"""
        return getattr(self, key, default)
    
    def get_nested(self, path: str, default=None):
        """获取嵌套配置值"""
        keys = path.split('.')
        value = self.config_data
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        
        return value
