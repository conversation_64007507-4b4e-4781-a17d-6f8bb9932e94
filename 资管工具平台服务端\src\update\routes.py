#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新服务API路由
"""

import os
from pathlib import Path
from flask import Blueprint, request, jsonify, send_file, current_app
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from loguru import logger
from datetime import datetime

from src.auth.middleware import api_key_required
from src.utils.response import success_response, error_response
from src.utils.validators import validate_json
from .models import VersionInfo, DownloadRecord
from .version import VersionManager
from src.database import db

# 创建蓝图
update_bp = Blueprint('update', __name__)

# 创建版本管理器实例
version_manager = VersionManager()


@update_bp.route('/check', methods=['GET'])
@api_key_required
def check_update():
    """检查更新"""
    try:
        # 获取客户端版本信息
        current_version = request.args.get('version', '0.0.0')
        platform = request.args.get('platform', 'windows')
        architecture = request.args.get('arch', 'x64')
        
        # 检查是否有更新
        update_info = version_manager.check_for_updates(
            current_version, platform, architecture
        )
        
        if update_info['has_update']:
            logger.info(f"发现更新: {current_version} -> {update_info['latest_version']}")
        else:
            logger.info(f"无需更新: {current_version}")
        
        return success_response(update_info)
    
    except Exception as e:
        logger.error(f"检查更新异常: {e}")
        return error_response('服务器内部错误', 500)


@update_bp.route('/download', methods=['GET'])
@api_key_required
def download_update():
    """下载更新包"""
    try:
        # 获取下载参数
        version = request.args.get('version')
        platform = request.args.get('platform', 'windows')
        architecture = request.args.get('arch', 'x64')
        
        if not version:
            return error_response('缺少版本参数', 400)
        
        # 查找版本信息
        version_info = VersionInfo.query.filter_by(
            version=version,
            platform=platform,
            architecture=architecture,
            is_active=True
        ).first()
        
        if not version_info:
            return error_response('版本不存在或不可用', 404)
        
        # 检查文件是否存在
        file_path = Path(current_app.config['UPDATE_UPLOAD_FOLDER']) / version_info.file_path
        if not file_path.exists():
            logger.error(f"更新文件不存在: {file_path}")
            return error_response('更新文件不存在', 404)
        
        # 记录下载
        download_record = DownloadRecord(
            version_id=version_info.id,
            client_ip=request.remote_addr,
            user_agent=request.headers.get('User-Agent', ''),
            download_time=datetime.utcnow()
        )
        db.session.add(download_record)
        
        # 更新下载计数
        version_info.download_count += 1
        db.session.commit()
        
        logger.info(f"开始下载更新: {version} - {request.remote_addr}")
        
        # 发送文件
        return send_file(
            file_path,
            as_attachment=True,
            download_name=f"update_{version}_{platform}_{architecture}.zip",
            mimetype='application/zip'
        )
    
    except Exception as e:
        logger.error(f"下载更新异常: {e}")
        return error_response('服务器内部错误', 500)


@update_bp.route('/changelog', methods=['GET'])
@api_key_required
def get_changelog():
    """获取更新日志"""
    try:
        version = request.args.get('version')
        limit = min(int(request.args.get('limit', 10)), 50)
        
        # 构建查询
        query = VersionInfo.query.filter_by(is_active=True)
        
        if version:
            query = query.filter_by(version=version)
        
        # 按版本号降序排列
        versions = query.order_by(VersionInfo.release_date.desc()).limit(limit).all()
        
        # 格式化结果
        changelog = []
        for version_info in versions:
            changelog.append({
                'version': version_info.version,
                'release_date': version_info.release_date.isoformat(),
                'description': version_info.description,
                'changelog': version_info.changelog,
                'file_size': version_info.file_size,
                'is_force_update': version_info.is_force_update,
                'download_count': version_info.download_count
            })
        
        return success_response({
            'changelog': changelog,
            'total': len(changelog)
        })
    
    except Exception as e:
        logger.error(f"获取更新日志异常: {e}")
        return error_response('服务器内部错误', 500)


@update_bp.route('/versions', methods=['GET'])
@api_key_required
def list_versions():
    """列出所有版本"""
    try:
        platform = request.args.get('platform')
        architecture = request.args.get('arch')
        active_only = request.args.get('active_only', 'true').lower() == 'true'
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 20)), 100)
        
        # 构建查询
        query = VersionInfo.query
        
        if platform:
            query = query.filter_by(platform=platform)
        
        if architecture:
            query = query.filter_by(architecture=architecture)
        
        if active_only:
            query = query.filter_by(is_active=True)
        
        # 分页查询
        pagination = query.order_by(VersionInfo.release_date.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        # 格式化结果
        versions = []
        for version_info in pagination.items:
            versions.append({
                'id': version_info.id,
                'version': version_info.version,
                'platform': version_info.platform,
                'architecture': version_info.architecture,
                'release_date': version_info.release_date.isoformat(),
                'description': version_info.description,
                'file_size': version_info.file_size,
                'download_count': version_info.download_count,
                'is_active': version_info.is_active,
                'is_force_update': version_info.is_force_update
            })
        
        return success_response({
            'versions': versions,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': pagination.total,
                'pages': pagination.pages,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev
            }
        })
    
    except Exception as e:
        logger.error(f"列出版本异常: {e}")
        return error_response('服务器内部错误', 500)


@update_bp.route('/statistics', methods=['GET'])
@api_key_required
def get_update_statistics():
    """获取更新统计信息"""
    try:
        # 统计版本数量
        total_versions = VersionInfo.query.count()
        active_versions = VersionInfo.query.filter_by(is_active=True).count()
        
        # 统计下载次数
        total_downloads = db.session.query(
            db.func.sum(VersionInfo.download_count)
        ).scalar() or 0
        
        # 统计最近下载
        from datetime import timedelta
        yesterday = datetime.utcnow() - timedelta(days=1)
        recent_downloads = DownloadRecord.query.filter(
            DownloadRecord.download_time >= yesterday
        ).count()
        
        # 按平台统计
        platform_stats = db.session.query(
            VersionInfo.platform,
            db.func.count(VersionInfo.id),
            db.func.sum(VersionInfo.download_count)
        ).group_by(VersionInfo.platform).all()
        
        platform_data = {}
        for platform, version_count, download_count in platform_stats:
            platform_data[platform] = {
                'versions': version_count,
                'downloads': download_count or 0
            }
        
        # 最受欢迎的版本
        popular_versions = VersionInfo.query.order_by(
            VersionInfo.download_count.desc()
        ).limit(5).all()
        
        popular_list = []
        for version in popular_versions:
            popular_list.append({
                'version': version.version,
                'platform': version.platform,
                'downloads': version.download_count
            })
        
        return success_response({
            'total_versions': total_versions,
            'active_versions': active_versions,
            'total_downloads': total_downloads,
            'recent_downloads': recent_downloads,
            'platform_stats': platform_data,
            'popular_versions': popular_list
        })
    
    except Exception as e:
        logger.error(f"获取更新统计异常: {e}")
        return error_response('服务器内部错误', 500)


@update_bp.route('/upload', methods=['POST'])
@api_key_required
def upload_version():
    """上传新版本"""
    try:
        # 检查文件
        if 'file' not in request.files:
            return error_response('缺少文件', 400)
        
        file = request.files['file']
        if file.filename == '':
            return error_response('未选择文件', 400)
        
        # 获取版本信息
        version = request.form.get('version')
        platform = request.form.get('platform', 'windows')
        architecture = request.form.get('architecture', 'x64')
        description = request.form.get('description', '')
        changelog = request.form.get('changelog', '')
        is_force_update = request.form.get('is_force_update', 'false').lower() == 'true'
        
        if not version:
            return error_response('缺少版本号', 400)
        
        # 检查版本是否已存在
        existing_version = VersionInfo.query.filter_by(
            version=version,
            platform=platform,
            architecture=architecture
        ).first()
        
        if existing_version:
            return error_response('版本已存在', 400)
        
        # 保存文件
        upload_folder = Path(current_app.config['UPDATE_UPLOAD_FOLDER'])
        upload_folder.mkdir(parents=True, exist_ok=True)
        
        filename = f"{version}_{platform}_{architecture}_{file.filename}"
        file_path = upload_folder / filename
        file.save(file_path)
        
        # 创建版本记录
        version_info = VersionInfo(
            version=version,
            platform=platform,
            architecture=architecture,
            release_date=datetime.utcnow(),
            description=description,
            changelog=changelog,
            file_path=filename,
            file_size=file_path.stat().st_size,
            is_active=True,
            is_force_update=is_force_update
        )
        
        db.session.add(version_info)
        db.session.commit()
        
        logger.info(f"新版本上传成功: {version} - {platform} - {architecture}")
        
        return success_response({
            'message': '版本上传成功',
            'version_id': version_info.id,
            'file_size': version_info.file_size
        })
    
    except Exception as e:
        logger.error(f"上传版本异常: {e}")
        db.session.rollback()
        return error_response('服务器内部错误', 500)
