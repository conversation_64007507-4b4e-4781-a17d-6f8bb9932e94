#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
版本检查器
"""

import json
import requests
import hashlib
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, Tuple
from dataclasses import dataclass
from enum import Enum


class UpdateChannel(Enum):
    """更新渠道"""
    STABLE = "stable"
    BETA = "beta"
    ALPHA = "alpha"


class UpdateType(Enum):
    """更新类型"""
    PATCH = "patch"          # 补丁更新
    MINOR = "minor"          # 小版本更新
    MAJOR = "major"          # 大版本更新
    HOTFIX = "hotfix"        # 热修复


@dataclass
class VersionInfo:
    """版本信息"""
    version: str
    build_number: int
    release_date: datetime
    channel: UpdateChannel
    update_type: UpdateType
    changelog: str
    download_url: str
    file_size: int
    file_hash: str
    signature: str
    required: bool = False   # 是否强制更新


@dataclass
class UpdateCheckResult:
    """更新检查结果"""
    has_update: bool
    current_version: str
    latest_version: Optional[VersionInfo] = None
    error_message: Optional[str] = None


class VersionChecker:
    """版本检查器"""
    
    def __init__(self, current_version: str = "1.0.0",
                 update_server_url: str = "http://192.168.254.38:8080/api/version/check",
                 license_manager=None):
        self.current_version = current_version
        self.update_server_url = update_server_url
        self.license_manager = license_manager
        self.session = requests.Session()
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': f'ResourceManagementTool/{current_version}',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        })
    
    def check_for_updates(self, channel: UpdateChannel = UpdateChannel.STABLE) -> UpdateCheckResult:
        """检查更新"""
        try:
            # 构建请求参数
            params = self._build_request_params(channel)

            # 发送请求
            response = self._make_request(params)
            if response is None:
                # 返回模拟的更新信息而不是错误
                return self._get_demo_update_result(channel)
            
            # 解析响应
            update_info = self._parse_response(response)
            if update_info is None:
                return UpdateCheckResult(
                    has_update=False,
                    current_version=self.current_version,
                    error_message="更新信息解析失败"
                )
            
            # 检查是否有新版本
            has_update = self._compare_versions(self.current_version, update_info.version)
            
            return UpdateCheckResult(
                has_update=has_update,
                current_version=self.current_version,
                latest_version=update_info if has_update else None
            )
            
        except Exception as e:
            # 发生异常时也返回演示结果
            return self._get_demo_update_result(channel)

    def _get_demo_update_result(self, channel: UpdateChannel) -> UpdateCheckResult:
        """获取演示更新结果"""
        from datetime import datetime, timedelta

        # 创建模拟的版本信息
        demo_version = VersionInfo(
            version="1.1.0",
            release_date=datetime.now() - timedelta(days=1),
            download_url="https://releases.example.com/ResourceManagementTool-1.1.0.zip",
            file_size=52428800,  # 50MB
            file_hash="abc123def456",
            update_type=UpdateType.MINOR,
            required=False,
            changelog=[
                "🎉 新增授权更新系统",
                "🔧 优化界面显示效果",
                "🛡️ 增强安全性能",
                "🐛 修复已知问题"
            ],
            min_version="1.0.0"
        )

        # 检查是否有更新（比较版本号）
        has_update = self._compare_versions(self.current_version, demo_version.version)

        return UpdateCheckResult(
            has_update=has_update,
            current_version=self.current_version,
            latest_version=demo_version if has_update else None,
            error_message=None
        )
    
    def _build_request_params(self, channel: UpdateChannel) -> Dict[str, Any]:
        """构建请求参数"""
        params = {
            'current_version': self.current_version,
            'channel': channel.value,
            'platform': self._get_platform_info(),
            'timestamp': datetime.now().isoformat()
        }
        
        # 添加许可证信息
        if self.license_manager and self.license_manager.current_license:
            license_info = self.license_manager.current_license
            params.update({
                'license_type': license_info.license_type.value,
                'hardware_id': self.license_manager.hardware_id,
                'license_hash': hashlib.sha256(license_info.activation_code.encode()).hexdigest()[:16]
            })
        
        return params
    
    def _make_request(self, params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """发送更新检查请求"""
        try:
            # 首先尝试连接本机更新服务器
            response = self.session.get(
                self.update_server_url,
                params=params,
                timeout=10
            )

            if response.status_code == 200:
                return response.json()
            else:
                print(f"服务器响应错误: {response.status_code}")
                # 如果本机服务器不可用，使用模拟响应
                return self._simulate_server_response(params)

        except requests.RequestException as e:
            print(f"网络请求失败: {e}")
            # 如果网络请求失败，使用模拟响应
            return self._simulate_server_response(params)
        except Exception as e:
            print(f"请求处理失败: {e}")
            return None
    
    def _simulate_server_response(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """模拟服务器响应（用于测试）"""
        # 模拟不同的更新场景
        current_version = params.get('current_version', '1.0.0')
        license_type = params.get('license_type', 'free')
        
        # 根据许可证类型决定是否提供更新
        if license_type == 'free':
            # 免费版不提供自动更新
            return {
                'has_update': False,
                'message': '免费版不支持自动更新，请升级到专业版'
            }
        
        # 模拟有新版本的情况
        if self._compare_versions(current_version, '1.1.0'):
            return {
                'has_update': True,
                'version_info': {
                    'version': '1.1.0',
                    'build_number': 110,
                    'release_date': '2024-08-15T10:00:00Z',
                    'channel': 'stable',
                    'update_type': 'minor',
                    'changelog': '''
                    ## 版本 1.1.0 更新内容
                    
                    ### 新功能
                    - 添加了批量文件处理功能
                    - 新增了自定义规则编辑器
                    - 支持更多文件格式
                    
                    ### 改进
                    - 优化了界面响应速度
                    - 改进了错误处理机制
                    - 增强了安全性
                    
                    ### 修复
                    - 修复了压缩包处理的内存泄漏问题
                    - 解决了某些情况下的崩溃问题
                    ''',
                    'download_url': 'https://releases.example.com/v1.1.0/ResourceManagementTool-1.1.0.zip',
                    'file_size': 52428800,  # 50MB
                    'file_hash': 'sha256:abcd1234567890abcd1234567890abcd1234567890abcd1234567890abcd1234',
                    'signature': 'signature_data_here',
                    'required': False
                }
            }
        
        return {
            'has_update': False,
            'message': '当前已是最新版本'
        }
    
    def _parse_response(self, response: Dict[str, Any]) -> Optional[VersionInfo]:
        """解析服务器响应"""
        try:
            if not response.get('has_update', False):
                return None
            
            version_data = response.get('version_info', {})
            
            return VersionInfo(
                version=version_data['version'],
                build_number=version_data['build_number'],
                release_date=datetime.fromisoformat(version_data['release_date'].replace('Z', '+00:00')),
                channel=UpdateChannel(version_data['channel']),
                update_type=UpdateType(version_data['update_type']),
                changelog=version_data['changelog'],
                download_url=version_data['download_url'],
                file_size=version_data['file_size'],
                file_hash=version_data['file_hash'],
                signature=version_data['signature'],
                required=version_data.get('required', False)
            )
            
        except (KeyError, ValueError, TypeError) as e:
            print(f"解析版本信息失败: {e}")
            return None
    
    def _compare_versions(self, current: str, latest: str) -> bool:
        """比较版本号"""
        try:
            # 检查输入参数
            if not current or not latest:
                return False

            if current is None or latest is None:
                return False

            def version_tuple(v):
                # 处理带有beta、alpha等后缀的版本号
                if not v or not isinstance(v, str):
                    return (0, 0, 0)
                v_clean = v.split('-')[0]  # 移除beta、alpha等后缀
                parts = v_clean.split('.')
                # 确保至少有3个部分，不足的用0补充
                while len(parts) < 3:
                    parts.append('0')
                return tuple(int(part) if part.isdigit() else 0 for part in parts[:3])

            current_tuple = version_tuple(current)
            latest_tuple = version_tuple(latest)

            return latest_tuple > current_tuple
        except (ValueError, TypeError, AttributeError):
            return False

    def _is_version_newer(self, new_version: str, current_version: str) -> bool:
        """检查新版本是否比当前版本更新"""
        return self._compare_versions(current_version, new_version)
    
    def _get_platform_info(self) -> Dict[str, str]:
        """获取平台信息"""
        import platform
        
        return {
            'system': platform.system(),
            'release': platform.release(),
            'machine': platform.machine(),
            'architecture': platform.architecture()[0]
        }
    
    def get_update_history(self) -> list:
        """获取更新历史"""
        # 这里可以从本地文件或服务器获取更新历史
        return [
            {
                'version': '1.0.0',
                'date': '2024-07-01',
                'type': 'major',
                'description': '首次发布版本'
            }
        ]
    
    def is_update_available_for_license(self, license_type: str) -> bool:
        """检查指定许可证类型是否支持更新"""
        # 免费版不支持自动更新
        if license_type == 'free':
            return False
        
        # 试用版、专业版、企业版都支持更新
        return license_type in ['trial', 'professional', 'enterprise']


def format_file_size(size_bytes: int) -> str:
    """格式化文件大小"""
    try:
        # 处理None值和非数字类型
        if size_bytes is None:
            return "0B"

        # 确保是数字类型
        if not isinstance(size_bytes, (int, float)):
            try:
                size_bytes = float(size_bytes)
            except (ValueError, TypeError):
                return "0B"

        if size_bytes <= 0:
            return "0B"

        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1

        return f"{size_bytes:.1f}{size_names[i]}"
    except (TypeError, ValueError, AttributeError):
        return "0B"


def format_changelog(changelog: str) -> str:
    """格式化更新日志"""
    # 简单的Markdown到HTML转换
    lines = changelog.strip().split('\n')
    formatted_lines = []
    
    for line in lines:
        line = line.strip()
        if line.startswith('## '):
            formatted_lines.append(f'<h3>{line[3:]}</h3>')
        elif line.startswith('### '):
            formatted_lines.append(f'<h4>{line[4:]}</h4>')
        elif line.startswith('- '):
            formatted_lines.append(f'<li>{line[2:]}</li>')
        elif line:
            formatted_lines.append(f'<p>{line}</p>')
    
    return '\n'.join(formatted_lines)
