@echo off
chcp 65001 >nul
echo 🚀 启动资管工具平台更新服务器...
echo.

cd /d "%~dp0"

echo 📦 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

echo 📦 检查依赖包...
pip show Flask >nul 2>&1
if errorlevel 1 (
    echo 📥 安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ 依赖包安装失败
        pause
        exit /b 1
    )
)

echo ✅ 环境检查完成
echo.
echo 🌐 启动更新服务器...
echo 📍 本机地址: http://127.0.0.1:8080
echo 🔗 状态页面: http://127.0.0.1:8080/api/status
echo.
echo ℹ️  如需外部访问，请运行: python setup_ip_server.py
echo 按 Ctrl+C 停止服务器
echo.

python server.py

pause
