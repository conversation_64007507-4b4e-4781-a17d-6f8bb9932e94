[Rules]
# 规则1：按[xxx]标识且不排序但独有的文件
rule1_files = 3deffect3.ini,3deffect2.ini,3deffects3.ini,

# 规则2：按第一位ID排序且独有的文件
rule2_files = abysssoulinfo.csv,abysssoulinfo.txt,additionattr.csv,additionlevattr.csv,additionlevattr.txt,additiontype.csv,addspeed.csv,addspeed.txt,androidchannelattr.csv,androidchannelattr.txt,arena_season.csv,arena_season.txt,asyncresource.csv,asyncresource.txt,attr.csv,attr.txt,autoreplacequickkeymagic.csv,autoreplacequickkeymagic.txt,backagainprivexpand.csv,backagainprivexpand.txt,batlevbalance.csv,batlevbalance.txt,battlefielddivision.csv,battlefielddivision.txt,blessingtoolscore.csv,blessingtoolscore.txt,bossscorerankaward.csv,bossscorerankaward.txt,buffestatusbanaction.csv,buffestatusbanaction.txt,changemapviewportresolution.csv,changemapviewportresolution.txt,changemilestone.csv,changemilestone.txt,changepartsrankconfig.csv,changepartsrankconfig.txt,changerebuildconfig.csv,changerebuildconfig.txt,chaoticgodimprint.csv,chaoticgodimprint.txt,closetitem.csv,closetui.csv,closetui.txt,composeitem.csv,composeitem.txt,confidantsharebattlelimit.csv,confidantsharebattlelimit.txt,craftstonecost.csv,craftstonecost.txt,crazysoulaction.csv,crazysoulaction.txt,crossfamehalldata.csv,crossfamehalldata.txt,crosspublicline.csv,crosspublicline.txt,crossserverbooth.csv,crossserverbooth.txt,crossserverpersonalboothunlock.csv,crossserverpersonalboothunlock.txt,crossserversaleableitem.csv,crossserversaleableitem.txt,crossserversharableboothsellitemnumconfig.csv,crossserversharableboothsellitemnumconfig.txt,crosstypename.csv,crosstypename.txt,cryinfo.csv,cryinfo.txt,customattr.csv,customattr.txt,danmu.csv,dharmavessel.csv,dharmavessel.txt,dragoneudemoninfo.csv,dragoneudemoninfo.txt,dreameudequipattradd.csv,dreameudequipattradd.txt,dreameudgrowthstage.csv,dreameudgrowthstage.txt,dreameudrecall.csv,dreameudrecall.txt,dreameudskill.csv,dreameudskill.txt,dreameudtasktype.csv,dreameudtasktype.txt,dreamlevupconfig.csv,dreamlevupconfig.txt,equipscore.csv,equipscore.txt,equipspirit.csv,equipspirit.txt,eud_attr.ini,eudawakenrebuild.csv,eudawakenrebuild.txt,euddirectuplev.csv,euddirectuplev.txt,eudeffectfootprint.csv,eudeffectfootprint.txt,eudemon_rbn_rqr.csv,eudemon_rbn_rqr.txt,eudemon_rbn_type.csv,eudemon_rbn_type.txt,eudemonbook.csv,eudemonbook.txt,eudemonlimitedtimeeffect.csv,eudemonlimitedtimeeffect.txt,eudequipbreakcontrol.csv,eudequipbreakcontrol.txt,eudequipdecomposeattr.csv,eudequipdecomposeattr.txt,eudequipevolve.csv,eudequipevolve.txt,eudequipluckused.csv,eudequipluckused.txt,eudequipslotinheritreq.csv,eudequipslotinheritreq.txt,eudequipsuitattr.csv,eudequipsuitattr.txt,eudequipsuitparts.csv,eudequipsuitparts.txt,eudequipviceattr.csv,eudequipviceattr.txt,eudevolution.csv,eudevolution.txt,eudexchginit.csv,eudexchginit.txt,eudfeedexpitempoint.csv,eudfeedexpitempoint.txt,eudfeedgodequipcore.csv,eudfeedgodequipcore.txt,eudhelp.csv,eudhelp.txt,eudmystery.csv,eudmystery.txt,eudskillupgrade.csv,eudskillupgrade.txt,eudsouldesc.csv,eudsouldesc.txt,eudsoulreset.csv,eudsoulreset.txt,eudsoulsacrifice.csv,eudsoulsacrifice.txt,eudswallowupperlimit.csv,eudswallowupperlimit.txt,eudtalentget.csv,eudtalentget.txt,eudtattoo.csv,eudtattoo.txt,evocatorjiejieinfo.csv,evocatorjiejieinfo.txt,famehallcost.ini,familybattlerank.csv,familybattlerank.txt,fightingspiritskilldesc.csv,fightingspiritskilldesc.txt,fiveeudsoulappearattr.csv,fiveeudsoulappearattr.txt,gemaddiattr.csv,gemaddiattr.txt,gemcraftsconvert.csv,gemcraftsconvert.txt,gemforge.csv,gemforge.txt,giftboxmessagefrom.csv,giftboxmessagefrom.txt,gifttype.csv,gifttype.txt,godattrrelation.csv,godattrrelation.txt,goddragonmagicbuffdesc.csv,goddragonmagicbuffdesc.txt,godequip.csv,godequip.txt,godequipmaketype.csv,godequipmaketype.txt,godfire.csv,godfire.txt,godfireattr.csv,godfireattr.txt,godfireattrvaluetorate.csv,godfireattrvaluetorate.txt,godfirerealmsort.csv,godfirerealmsort.txt,godfiresmelt.csv,godfiresmelt.txt,godfiresuitpriority.csv,godfiresuitpriority.txt,godfiresuper.csv,godfiresuper.txt,godfireviceattrrefine.csv,godfireviceattrrefine.txt,godflyupmount.csv,godflyupmount.txt,godlevel.csv,godlevel.txt,grade.csv,grade.txt,groupmount.csv,groupmount.txt,itememoneyvalueconfig.csv,itememoneyvalueconfig.txt,itemtype.csv,levelexp.csv,levelexp.txt,magicattreffect.csv,magicattreffect.txt,magicimprovemagicattr.csv,magicimprovemagicattr.txt,magicrandomsound.csv,magicrandomsound.txt,magictraceeffect.csv,magictraceeffect.txt,magictype.csv,manorfurniture.csv,manorfurniture.txt,manorfurnituresuit.csv,manorfurnituresuit.txt,manorthumbnail.csv,monster.csv,monster.txt,mountshow.csv,mulmountinfo.csv,mulmountinfo.txt,multieudtowerboatfusionstaraddattr.csv,multieudtowerboatfusionstaraddattr.txt,multieudtowerfusiontemplate.csv,multieudtowerfusiontemplate.txt,multieudtowerlayer.csv,multieudtowerlayer.txt,multieudtowerrankreward.csv,multieudtowerrankreward.txt,mvpbattlereward.csv,mvpbattlereward.txt,neweudbook.csv,neweudbook.txt,newstylelotteryitem.csv,newstylelotteryitem.txt,notification.csv,notification.txt,npc.csv,npc.txt,npctaskchangelook.csv,patternattr.csv,patternattr.txt,patterninancientartifact.csv,patterninancientartifact.txt,preloadresource.csv,preloadresource.txt,rebuildstatetimespreview.csv,rebuildstatetimespreview.txt,recommendkillmonster.csv,recommendkillmonster.txt,regressthirdphaseenergyreduce.csv,regressthirdphaseenergyreduce.txt,sacredsoulmaterialexp.csv,sacredsoulmaterialexp.txt,sacredupgrade.csv,sacredupgrade.txt,soulsprite.ini,spiritbattleconvert.csv,spiritbattleconvert.txt,starmonstermaxexp.csv,starmonstermaxexp.txt,statusparteffect.csv,statusparteffect.txt,storageexpand.ini,surprisedata.csv,surprisedata.txt,surpriseequiplevadd.csv,surpriseequiplevadd.txt,synduplicatetype.csv,synduplicatetype.txt,synexpeditionduplicate.csv,synexpeditionduplicate.txt,synexploitsrebuild.csv,synexploitsrebuild.txt,synflagset.csv,synflagset.txt,synmedalinfo.csv,synmedalinfo.txt,talenttype.csv,talenttype.txt,tattoosuitshow.csv,tattoosuitshow.txt,temporaryitemunfreeze.csv,temporaryitemunfreeze.txt,tipgetwaynew.csv,tipgetwaynew.txt,vampirejiejiecost.csv,vampirejiejiecost.txt,vampirejiejielev.csv,vampirejiejielev.txt,weaponshowinfo.csv,weaponshowinfo.txt,yatebirthdaygift.csv,yatebirthdaygift.txt,yateboothprivileges.csv,yateboothprivileges.txt

# 规则3：按=前的ID排序且独有的文件
rule3_files = arenachangemagic.ini,partviewbyarmor.ini,soundreplace.ini,statusattradditiondata.ini,synduplicateinfo.ini,teamaddvalue.ini,3deffectobj.ini,3dtexture.ini,3dmotion.ini,3dmotion_ex.ini,3dobj.ini,3dskeleton.ini,extendmotion.ini,extendmotion_ex.ini,footmotion.ini,footmotion_ex.ini,forearmmotion.ini,forearmmotion_ex.ini,hairmotion.ini,hairmotion_ex.ini,headmotion.ini,headmotion_ex.ini,mantlemotion.ini,mantlemotion_ex.ini,miscmotion.ini,miscmotion_ex.ini,mountmotion.ini,mountmotion_ex.ini,pelvismotion.ini,pelvismotion_ex.ini,sashmotion.ini,sashmotion_ex.ini,silkmotion.ini,silkmotion_ex.ini,spinemotion.ini,spinemotion_ex.ini,weaponmotion.ini,weaponmotion_ex.ini,actionsound.ini

# 规则4：每个段落前有注释的文件（如control.ani）
rule4_files = control.ani,control.ani.size,socialemotion.ini

# 规则5：与规则1相同，但对title进行排序
rule5_files = eudskin.ini,geartransform.ini,actionctrlheight.ini,actionctrltarg.ini,addbaglimit.ini,alchemy.ini,asktype.ini,backagaintalk.ini,batchrebuildshopinfo.ini,battlefieldmapinfo.ini,boothnpcpos.ini,boothstyle.ini,chatmgrsystemreason.ini,chessboardevent.ini,chessboardinfo.ini,closetad.ini,closetsuit.ini,counsellorappointextraattr.ini,createplayer.ini,createplayerface.ini,crosscamp.ini,crossmapblock.ini,crossserverboothposition.ini,dailyreward.ini,danmu.ini,depotnpc.ini,dharmavessebook.ini,discountpush.ini,disdain.ini,dragoondefsharedinfo.ini,dragoonsacrifice.ini,dragoonscrificeattrinfo.ini,dungeoncountdown.ini,duplicate.ini,energy.ini,eudconsultanttutorial.ini,euddeadroleview.ini,eudinheritspecitem.ini,eudlabel.ini,eudmodeltype.ini,eudrecommendex.ini,eudspecialbackground.ini,eudstatuestarimage.ini,eudstorageextend.ini,everydayweal.ini,exchangearmor.ini,exchangehair.ini,extendpart.ini,extradmg.ini,familybattleballetscreen.ini,familyconfig.ini,familyhelp.ini,familyspeak.ini,firstrecharge.ini,flower.ini,floweraddnum.ini,flyingmount.ini,furniturecategory.ini,gamemap.ini,gatedestiny.ini,gemlevel.ini,geomancyevaluate.ini,giftbagforstar.ini,godadditionexp.ini,godequipancientname.ini,godfireachive.ini,godfireeffectmagicattr.ini,grade.ini,hallowbook.ini,hallowsellinfo.ini,holycity.ini,keyword.ini,knightenablerecommend.ini,landconfiginfo.ini,landcourtyardinfo.ini,landeventconfiginfo.ini,landtypeinfo.ini,layeralpha.ini,lookfaceeffect.ini,lookpart.ini,luatalk.ini,manorfurnituresuitaddattr.ini,mapbanconfig.ini,mapmagicitem.ini,markettargets.ini,marryshowexp.ini,moonbox.ini,mountbody.ini,mountdumy.ini,mounttype.ini,movingtrapconfig.ini,mvpvipprivilege.ini,necromancerdeathsoulboundary.ini,necromancerdeathsouleudemon.ini,necromancerdeathsouloffer.ini,normalbody.ini,npcdestory.ini,officialtype.ini,pkfooteffect.ini,prestigelevexp.ini,privilegeinstruct.ini,probabilityinfo.ini,professionalactioninfo.ini,professionalexchgid.ini,professionalname.ini,professionstr.ini,publicsale.ini,queenhouseopen.ini,rankbattle.ini,reddotprompt.ini,region.ini,roofcovergroup.ini,shakescreeneffect.ini,socialityaction.ini,soullegendnovel.ini,soundemotion.ini,spiritrealminfo.ini,starnumscondition.ini,statueinfo.ini,statuseffect.ini,statuseffectnew.ini,statusmapping.ini,statustips.ini,swordkendo.ini,synactivity.ini,synassistant.ini,syncampfire.ini,syncoat.ini,syncopymap.ini,syndicatecoat.ini,syndicatemember.ini,synexpeditionduplicatearea.ini,synhatch.ini,synlevinfo.ini,synoccupywarcity.ini,synownermapfindroad.ini,synstatueuplevcondition.ini,synwarkilltext.ini,synwartutorial.ini,taskshowstory.ini,terrainsound.ini,testcommand.ini,thirdphaseregressprivileges.ini,titlesystem.ini,titlesystemcustom.ini,todolist.ini,transfer.ini,trumpattr.ini,trumplevel.ini,trumprefine.ini,trumpupinfo.ini,tutoreverydaytask.ini,tutortype.ini,unbinditemlimit.ini,weaponcoattransferrule.ini,worldboss.ini,yatenameeffect.ini,3dsimpleobj.ini,3dsimpleobjex.ini,armor.ini,extend.ini,foot.ini,forearm.ini,hair.ini,head.ini,mantle.ini,misc.ini,mount.ini,pelvis.ini,sash.ini,silk.ini,spine.ini,weapon.ini,actionmap3deffect.ini

# 规则6：每个段落前有注释的文件（如npcface.ani）
rule6_files = emotionico.ani,emotionico.ani.size,npcface.ani,npcface.ani.size,itemminicon.ani,itemminicon.ani.size,magic.ani,magic.ani.size,minimap.ani,minimap.ani.size,monsterface.ani,monsterface.ani.size,petface.ani,petface.ani.size,pettantleicon.ani,pettantleicon.ani.size,playerface.ani,playerface.ani.size

# 规则7：不整合除非整份替换
rule7_files = newshop.ini,shop.ini,refinery.ini,achievetype.xml,battletutorial.xml,boothitemrecommend.xml,bullkingacting.xml,bullkingreward.xml,camprankconfig.xml,eudreleaseseal.xml,helpequip.xml,helpguide.xml,magicbuffspec.xml,monthcard.xml,pkcompete.xml,weaponinfo.xml,abnormaldevice.ini,actionctrl.ini,captureflaginfo.ini,device_close3d.ini,device_wallpaper.ini,font.ini,gloabconfig.ini,graphic.ini,immediate.ini,material.ini,notchdevice.ini,package.ini,server_wd.dat,server_wd.ini,server_wd_inside.ini,rolepart.ini,activityexp.ini,aitutorconfig.ini,announce.ini,charschecklegal.ini,clientconfig.ini,deviceclose3dmode.ini,emoneyprice.ini,encrypt.ini,equipspirit_cfg.ini,eudawaken_cfg.ini,eudbook.ini,eudpurchasetype.ini,exclusivemall.ini,faq.ini,gamemsg.ini,general.ini,itemshowoffdesc.ini,jettonnpcshop.ini,leveldata.ini,localchat.ini,monsterpermanentbuff.ini,myuelogger.ini,optimize_cfg.ini,pinyindata.ini,postpic.ini,posttype.ini,randomflagname.ini,randomname.ini,recharge_platform.ini,server.ini,server_inside.ini,serveravegodlev.ini,serveravelev.ini,slient_ex.ini,slient_py.ini,specialserverdes.ini,sweettips.ini,synmembertitle.ini,systemmagictalentconfig.ini,taskquick.ini,treasuresay.ini,user_agreement_cfg.ini,useragreement.ini,viplevreward.ini,worldauctiondrop.ini,androidchannelpermissions.txt,config.txt,crossattrpercent.txt,crossprofessionattrbase.txt,dharmavesselupexp.txt,dharmavesselupgradestar.txt,dominionwardefender.txt,dreameudbloodsoulscoreadd.txt,dreameudequiplevadd.txt,dreameudgrowthstagebattlereward.txt,entryupgradesuccesspercent.txt,equipinheritcost.txt,eudawakenadvance.txt,eudawakenlook.txt,eudemon_vip_rebuild.txt,eudemonconnectaddition.txt,eudequipaddattr.txt,eudequipbreak.txt,eudequippartaddition.txt,eudequippartmagic.txt,eudequippositionandlev.txt,eudfeedequiptype.txt,eudnianappearanceflaunt.txt,eudrandomactionsound.txt,eudrebuildcost.txt,eudrelationattr.txt,eudrelationgroup.txt,eudrelationincrearate.txt,eudsnakeinfo.txt,eudsnakexinfaupgrade.txt,eudsoullevel.txt,eudsoullevelreward.txt,eudspiritrune.txt,eudswallow.txt,eudtattoosuit.txt,eudtexture.txt,eudtigerholyspiritrune.txt,eudtigerholyspiritsacrifice.txt,eudtokenrebuild.txt,eudviceexp.txt,eudvicelev.txt,evocatorwingdata.txt,expstonedata.txt,gemcrave.txt,godequipaddlev.txt,godequipattr.txt,godequipmakeattr.txt,godequipupgrade.txt,godfireaddition.txt,godfireadditionstuff.txt,godfirebreak.txt,godfiredevourpriority.txt,godfireprefix.txt,godfireprefixidinfo.txt,godfirerealm.txt,godfirerefine.txt,godfirestardevour.txt,godfirestrikematerial.txt,godfiresuit.txt,godfiresuitaffix.txt,godwayconfig.txt,godwayplot.txt,godwayquality.txt,godwaytask.txt,godwaytasktype.txt,helpequiplev.txt,improveequiplevel.txt,improveequipquality.txt,improvemagicsoul.txt,itemaddition.txt,kernelpreview.txt,luxiferaddattr.txt,magictalent.txt,magictalenttask.txt,manorfurnitureupstar.txt,manorplacelist.txt,multieudtowerfusionstaraddattr.txt,necromancerjiejieinfo.txt,patternrefine.txt,petenergy.txt,privilegeitemtype.txt,privilegesetmeal.txt,professionalrealmsacrifice.txt,rebuildluckcv.txt,sacredsoulupgrade.txt,scaredvessletips.txt,sealpower.txt,skillortalentinherit.txt,starpowerawakemagic.txt,starsongodwayupgrade.txt,swordsfate.txt,swordsfeature.txt,swordssacrificeupgrade.txt,synbuildingtype.txt,synoccupywardefender.txt,trainquickmove.txt,trumpdmg.txt,unchartedrebates.txt,bg.ani,bg.ani.size,cartoonwater.ani,cartoonwater.ani.size,chatboardname.ani,chatboardname.ani.size,city.ani,city.ani.size,deity.ani,deity.ani.size,equip.ani,equip.ani.size,eudmodeltype.ani,eudmodeltype.ani.size,god.ani,god.ani.size,godcity.ani,godcity.ani.size,godweapon.ani,godweapon.ani.size,headframe.ani,headframe.ani.size,home.ani,home.ani.size,inn.ani,inn.ani.size,kasbah.ani,kasbah.ani.size,legion.ani,legion.ani.size,lovelake.ani,lovelake.ani.size,mapscene.ani,mapscene.ani.size,my.ani,my.ani.size,q4.ani,q4.ani.size,qixi.ani,qixi.ani.size,sea.ani,sea.ani.size,star.ani,star.ani.size,tower.ani,tower.ani.size,winter.ani,winter.ani.size

# 规则8：按[xxx]标识，通过=号前的title查重追加
rule8_files = action3deffect.ini,exchangearmor.ini,exchangehair.ini,exchangeheadtexture.ini

# 规则9：task.xml文件
rule9_files = 

#规则10：默认数字排序
rule10_files = intelligentautobattlemap.ini,nobilitydata.ini,autofinishtype.ini,changeegg.ini,dynarank.ini,emotionico.ini,eudbookexp.ini,eudrbntype.ini,familybattletime.ini,guideclosewnd.ini,mergeinfo.ini,starsonspecialeffect.ini,synofferprofferlowest.ini,totempole.ini,totempower.ini,vip.ini

# 规则11：replaceaction.xml文件
rule11_files = 

# 规则12：其他.xml文件
rule12_files = 

# 规则13：按[xxx]分段，将IDRange值追加到对应段落
rule13_files = reqbuyitem.ini,itemcategory.ini

# 规则14：str类文件
rule14_files = strres.ini,motionhairid.ini,strres_new.ini

# 规则15：军团图鉴文件
rule15_files = syntotempillar.ini

# 规则16：closetitem.txt文件，基于第二位物品ID进行处理
rule16_files = closetitem.txt

#需要加密的文件
[Encryption]
required_files=3DEffect.ini,3DEffect2.ini,3DEffectObj.ini,3dmotion.ini,3DObj.ini,3DObjProp.ini,3DScene.ini,3DSimpleObj.ini,3DSkeleton.ini,3DTexture.ini,armor.ini,EmotionIco.ini,extend.ini,extendmotion.ini,foot.ini,footmotion.ini,forearm.ini,forearmmotion.ini,hair.ini,hairmotion.ini,head.ini,headmotion.ini,mantle.ini,mantlemotion.ini,Material.ini,misc.ini,miscmotion.ini,mount.ini,mountmotion.ini,pelvis.ini,pelvismotion.ini,RolePart.ini,sash.ini,sashmotion.ini,silk.ini,silkmotion.ini,sound.ini,spine.ini,spinemotion.ini,weapon.ini,weaponmotion.ini