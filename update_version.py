#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
版本号统一更新工具
"""

import re
import os
import json
from pathlib import Path
from datetime import datetime

class VersionUpdater:
    """版本号更新器"""
    
    def __init__(self, project_root=None):
        if project_root is None:
            project_root = Path(__file__).parent
        self.project_root = Path(project_root)
        
        # 定义需要更新的文件和模式
        self.update_targets = [
            {
                'file': '资管工具平台/src/main.py',
                'pattern': r'app\.setApplicationVersion\("([^"]+)"\)',
                'replacement': r'app.setApplicationVersion("{version}")',
                'description': '主程序版本'
            },
            {
                'file': '资管工具平台/src/ui/main_window.py',
                'pattern': r'self\.setWindowTitle\("资管工具平台 v([^"]+)"\)',
                'replacement': r'self.setWindowTitle("资管工具平台 v{version}")',
                'description': '主窗口标题'
            },
            {
                'file': '资管工具平台/src/ui/main_window.py',
                'pattern': r'version_label = QLabel\("v([^"]+)"\)',
                'replacement': r'version_label = QLabel("v{version}")',
                'description': '状态栏版本'
            },
            {
                'file': '资管工具平台/src/ui/about_dialog.py',
                'pattern': r'version_label = QLabel\("版本 ([^"]+)"\)',
                'replacement': r'version_label = QLabel("版本 {version}")',
                'description': '关于对话框版本标签'
            },
            {
                'file': '资管工具平台/src/ui/about_dialog.py',
                'pattern': r'<b>版本：</b>([^<]+)<br>',
                'replacement': r'<b>版本：</b>{version}<br>',
                'description': '关于对话框版本信息'
            }
        ]
    
    def get_current_version(self):
        """获取当前版本号"""
        main_py = self.project_root / '资管工具平台/src/main.py'
        if main_py.exists():
            content = main_py.read_text(encoding='utf-8')
            match = re.search(r'app\.setApplicationVersion\("([^"]+)"\)', content)
            if match:
                return match.group(1)
        return "1.0.0"
    
    def update_version(self, new_version):
        """更新版本号"""
        print(f"🔄 更新版本号: {self.get_current_version()} → {new_version}")
        print("=" * 50)
        
        updated_files = []
        
        for target in self.update_targets:
            file_path = self.project_root / target['file']
            
            if not file_path.exists():
                print(f"⚠️  文件不存在: {target['file']}")
                continue
            
            try:
                # 读取文件内容
                content = file_path.read_text(encoding='utf-8')
                original_content = content
                
                # 执行替换
                pattern = target['pattern']
                replacement = target['replacement'].format(version=new_version)
                
                # 查找匹配
                matches = re.findall(pattern, content)
                if matches:
                    # 执行替换
                    content = re.sub(pattern, replacement, content)
                    
                    # 写回文件
                    file_path.write_text(content, encoding='utf-8')
                    
                    print(f"✅ {target['description']}: {target['file']}")
                    print(f"   旧版本: {matches[0] if matches else '未找到'}")
                    print(f"   新版本: {new_version}")
                    updated_files.append(target['file'])
                else:
                    print(f"❌ 未找到匹配: {target['description']} in {target['file']}")
                    print(f"   搜索模式: {pattern}")
                
            except Exception as e:
                print(f"❌ 更新失败: {target['file']} - {e}")
        
        print(f"\n✅ 成功更新 {len(updated_files)} 个文件")
        return updated_files
    
    def update_config_version(self, new_version):
        """更新配置文件中的版本号"""
        print(f"\n🔧 更新配置文件版本号...")
        
        # 更新默认配置
        config_file = self.project_root / '资管工具平台/config/default_config.json'
        if config_file.exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # 添加版本信息
                if 'app' not in config:
                    config['app'] = {}
                config['app']['version'] = new_version
                
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(config, f, ensure_ascii=False, indent=4)
                
                print(f"✅ 更新配置文件: {config_file}")
            except Exception as e:
                print(f"❌ 更新配置文件失败: {e}")
    
    def update_about_dialog_date(self):
        """更新关于对话框的发布日期"""
        about_file = self.project_root / '资管工具平台/src/ui/about_dialog.py'
        if about_file.exists():
            try:
                content = about_file.read_text(encoding='utf-8')
                
                # 更新发布日期
                current_date = datetime.now().strftime('%Y年%m月%d日')
                pattern = r'<b>发布日期：</b>[^<]+<br>'
                replacement = f'<b>发布日期：</b>{current_date}<br>'
                
                content = re.sub(pattern, replacement, content)
                about_file.write_text(content, encoding='utf-8')
                
                print(f"✅ 更新发布日期: {current_date}")
            except Exception as e:
                print(f"❌ 更新发布日期失败: {e}")
    
    def validate_version_format(self, version):
        """验证版本号格式"""
        pattern = r'^\d+\.\d+\.\d+(-[a-zA-Z0-9]+)?$'
        return re.match(pattern, version) is not None
    
    def show_current_versions(self):
        """显示当前所有版本号"""
        print("📋 当前版本号分布:")
        print("-" * 30)
        
        for target in self.update_targets:
            file_path = self.project_root / target['file']
            if file_path.exists():
                content = file_path.read_text(encoding='utf-8')
                matches = re.findall(target['pattern'], content)
                if matches:
                    print(f"{target['description']}: {matches[0]}")
                else:
                    print(f"{target['description']}: 未找到")
            else:
                print(f"{target['description']}: 文件不存在")

def main():
    """主函数"""
    print("🚀 资管工具平台版本号更新工具")
    print("=" * 50)
    
    updater = VersionUpdater()
    
    while True:
        print("\n📋 选择操作:")
        print("1. 查看当前版本号")
        print("2. 更新版本号")
        print("3. 退出")
        
        choice = input("\n请选择 (1-3): ").strip()
        
        if choice == '1':
            print()
            updater.show_current_versions()
            
        elif choice == '2':
            current_version = updater.get_current_version()
            print(f"\n当前版本: {current_version}")
            
            new_version = input("请输入新版本号 (如 1.0.1): ").strip()
            
            if not new_version:
                print("❌ 版本号不能为空")
                continue
            
            if not updater.validate_version_format(new_version):
                print("❌ 版本号格式错误，请使用 x.y.z 格式")
                continue
            
            if new_version == current_version:
                print("⚠️  新版本号与当前版本相同")
                continue
            
            # 确认更新
            confirm = input(f"\n确认更新版本号 {current_version} → {new_version}? (y/N): ").strip().lower()
            if confirm in ['y', 'yes']:
                updated_files = updater.update_version(new_version)
                updater.update_config_version(new_version)
                updater.update_about_dialog_date()
                
                print(f"\n🎉 版本号更新完成!")
                print(f"📦 新版本: {new_version}")
                print(f"📁 更新文件数: {len(updated_files)}")
                print("\n💡 建议:")
                print("1. 重新启动应用程序")
                print("2. 检查所有功能是否正常")
                print("3. 更新服务端版本配置")
            else:
                print("❌ 取消更新")
                
        elif choice == '3':
            print("👋 再见!")
            break
            
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == '__main__':
    main()
