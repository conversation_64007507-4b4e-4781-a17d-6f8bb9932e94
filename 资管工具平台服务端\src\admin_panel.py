#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
激活码管理面板
"""

from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for
from datetime import datetime, timedelta
import secrets
import hashlib
from .database import get_db_connection
from .auth.auth_manager import require_admin_auth

admin_bp = Blueprint('admin', __name__, url_prefix='/admin')

@admin_bp.route('/')
@require_admin_auth
def dashboard():
    """管理面板首页"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取激活码统计
        cursor.execute("""
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active,
                SUM(CASE WHEN status = 'used' THEN 1 ELSE 0 END) as used,
                SUM(CASE WHEN status = 'expired' THEN 1 ELSE 0 END) as expired,
                SUM(CASE WHEN status = 'disabled' THEN 1 ELSE 0 END) as disabled
            FROM activation_codes
        """)
        stats = cursor.fetchone()
        
        # 获取最近的激活记录
        cursor.execute("""
            SELECT ac.code, ac.license_type, ac.created_at, ac.expires_at, ac.status,
                   al.machine_id, al.activated_at
            FROM activation_codes ac
            LEFT JOIN active_licenses al ON ac.code = al.activation_code
            ORDER BY ac.created_at DESC
            LIMIT 10
        """)
        recent_codes = cursor.fetchall()
        
        conn.close()
        
        return render_template('admin/dashboard.html', stats=stats, recent_codes=recent_codes)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/codes')
@require_admin_auth
def manage_codes():
    """激活码管理页面"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取查询参数
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        status_filter = request.args.get('status', '')
        license_type_filter = request.args.get('license_type', '')
        
        # 构建查询条件
        where_conditions = []
        params = []
        
        if status_filter:
            where_conditions.append("status = ?")
            params.append(status_filter)
        
        if license_type_filter:
            where_conditions.append("license_type = ?")
            params.append(license_type_filter)
        
        where_clause = ""
        if where_conditions:
            where_clause = "WHERE " + " AND ".join(where_conditions)
        
        # 获取总数
        cursor.execute(f"SELECT COUNT(*) FROM activation_codes {where_clause}", params)
        total = cursor.fetchone()[0]
        
        # 获取分页数据
        offset = (page - 1) * per_page
        cursor.execute(f"""
            SELECT code, license_type, max_devices, created_at, expires_at, status, notes
            FROM activation_codes 
            {where_clause}
            ORDER BY created_at DESC
            LIMIT ? OFFSET ?
        """, params + [per_page, offset])
        
        codes = cursor.fetchall()
        conn.close()
        
        # 计算分页信息
        total_pages = (total + per_page - 1) // per_page
        
        return render_template('admin/codes.html', 
                             codes=codes, 
                             page=page, 
                             total_pages=total_pages,
                             total=total,
                             status_filter=status_filter,
                             license_type_filter=license_type_filter)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/codes/generate', methods=['POST'])
@require_admin_auth
def generate_codes():
    """批量生成激活码"""
    try:
        data = request.get_json()
        
        count = int(data.get('count', 1))
        license_type = data.get('license_type', 'standard')
        max_devices = int(data.get('max_devices', 1))
        validity_days = int(data.get('validity_days', 365))
        notes = data.get('notes', '')
        
        if count > 100:
            return jsonify({'error': '单次最多生成100个激活码'}), 400
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        generated_codes = []
        expires_at = datetime.now() + timedelta(days=validity_days)
        
        for _ in range(count):
            # 生成激活码
            code = generate_activation_code()
            
            cursor.execute("""
                INSERT INTO activation_codes 
                (code, license_type, max_devices, expires_at, status, notes)
                VALUES (?, ?, ?, ?, 'active', ?)
            """, (code, license_type, max_devices, expires_at, notes))
            
            generated_codes.append({
                'code': code,
                'license_type': license_type,
                'max_devices': max_devices,
                'expires_at': expires_at.isoformat(),
                'notes': notes
            })
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': f'成功生成 {count} 个激活码',
            'codes': generated_codes
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/codes/<code>/status', methods=['PUT'])
@require_admin_auth
def update_code_status():
    """更新激活码状态"""
    try:
        code = request.view_args['code']
        data = request.get_json()
        new_status = data.get('status')
        
        if new_status not in ['active', 'disabled', 'expired']:
            return jsonify({'error': '无效的状态'}), 400
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            UPDATE activation_codes 
            SET status = ?, updated_at = CURRENT_TIMESTAMP
            WHERE code = ?
        """, (new_status, code))
        
        if cursor.rowcount == 0:
            return jsonify({'error': '激活码不存在'}), 404
        
        conn.commit()
        conn.close()
        
        return jsonify({'success': True, 'message': '状态更新成功'})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/codes/<code>/extend', methods=['POST'])
@require_admin_auth
def extend_code_validity():
    """延长激活码有效期"""
    try:
        code = request.view_args['code']
        data = request.get_json()
        extend_days = int(data.get('days', 30))
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            UPDATE activation_codes 
            SET expires_at = datetime(expires_at, '+{} days'),
                updated_at = CURRENT_TIMESTAMP
            WHERE code = ?
        """.format(extend_days), (code,))
        
        if cursor.rowcount == 0:
            return jsonify({'error': '激活码不存在'}), 404
        
        conn.commit()
        conn.close()
        
        return jsonify({'success': True, 'message': f'有效期延长 {extend_days} 天'})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/licenses')
@require_admin_auth
def active_licenses():
    """查看活跃许可证"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT al.activation_code, al.machine_id, al.activated_at, al.last_check,
                   ac.license_type, ac.max_devices, ac.expires_at
            FROM active_licenses al
            JOIN activation_codes ac ON al.activation_code = ac.code
            ORDER BY al.activated_at DESC
        """)
        
        licenses = cursor.fetchall()
        conn.close()
        
        return render_template('admin/licenses.html', licenses=licenses)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

def generate_activation_code():
    """生成激活码"""
    # 生成16位随机字符串
    random_part = secrets.token_hex(8).upper()
    
    # 格式化为 XXXX-XXXX-XXXX-XXXX
    formatted_code = '-'.join([random_part[i:i+4] for i in range(0, 16, 4)])
    
    return formatted_code

@admin_bp.route('/login', methods=['GET', 'POST'])
def login():
    """管理员登录"""
    if request.method == 'GET':
        return render_template('admin/login.html')
    
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        
        # 这里应该从配置文件或数据库验证管理员账号
        # 临时硬编码，实际应该使用加密存储
        if username == 'admin' and password == 'admin123':
            session['admin_logged_in'] = True
            session['admin_username'] = username
            return jsonify({'success': True, 'redirect': url_for('admin.dashboard')})
        else:
            return jsonify({'error': '用户名或密码错误'}), 401
            
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/logout')
def logout():
    """管理员登出"""
    session.pop('admin_logged_in', None)
    session.pop('admin_username', None)
    return redirect(url_for('admin.login'))
