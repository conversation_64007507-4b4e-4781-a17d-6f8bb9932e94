#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
许可证验证器
"""

import json
import hashlib
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from loguru import logger

from .models import LicenseRecord, LicenseValidation
from src.database import db


class LicenseValidator:
    """许可证验证器"""
    
    def __init__(self):
        self.master_secret = "ResourceManagementTool2024"
    
    def validate_license(self, activation_code: str, hardware_id: str, user_info: Dict = None) -> Dict[str, Any]:
        """验证许可证"""
        try:
            # 查找许可证记录
            license_record = LicenseRecord.query.filter_by(
                activation_code=activation_code
            ).first()
            
            if not license_record:
                return {
                    'success': False,
                    'error': '许可证不存在'
                }
            
            # 检查许可证是否激活
            if not license_record.is_active:
                return {
                    'success': False,
                    'error': '许可证已被停用'
                }
            
            # 检查过期时间
            if license_record.expire_date and license_record.expire_date < datetime.utcnow():
                return {
                    'success': False,
                    'error': '许可证已过期'
                }
            
            # 检查硬件绑定
            if license_record.hardware_id and license_record.hardware_id != hardware_id:
                # 检查是否超过最大激活数
                active_devices = LicenseValidation.query.filter_by(
                    activation_code=activation_code,
                    is_valid=True
                ).distinct(LicenseValidation.hardware_id).count()
                
                if active_devices >= license_record.max_activations:
                    return {
                        'success': False,
                        'error': f'已达到最大激活设备数限制 ({license_record.max_activations})'
                    }
            
            # 更新许可证记录
            if not license_record.hardware_id:
                license_record.hardware_id = hardware_id
            
            license_record.last_validation = datetime.utcnow()
            license_record.validation_count += 1
            
            # 更新用户信息
            if user_info:
                if user_info.get('user_name'):
                    license_record.user_name = user_info['user_name']
                if user_info.get('email'):
                    license_record.email = user_info['email']
                if user_info.get('company_name'):
                    license_record.company_name = user_info['company_name']
            
            db.session.commit()
            
            # 构建许可证信息
            license_info = {
                'license_type': license_record.license_type,
                'user_name': license_record.user_name,
                'email': license_record.email,
                'company_name': license_record.company_name,
                'created_date': license_record.created_date.isoformat(),
                'validation_count': license_record.validation_count
            }
            
            # 获取功能列表
            features = self._get_license_features(license_record.license_type)
            
            result = {
                'success': True,
                'license_info': license_info,
                'features': features
            }
            
            if license_record.expire_date:
                result['expires_at'] = license_record.expire_date.isoformat()
            
            return result
            
        except Exception as e:
            logger.error(f"许可证验证异常: {e}")
            return {
                'success': False,
                'error': '验证过程中发生错误'
            }
    
    def get_license_status(self, activation_code: str, hardware_id: str = None) -> Dict[str, Any]:
        """获取许可证状态"""
        try:
            license_record = LicenseRecord.query.filter_by(
                activation_code=activation_code
            ).first()
            
            if not license_record:
                return {
                    'exists': False,
                    'error': '许可证不存在'
                }
            
            # 计算状态
            status = 'active'
            if not license_record.is_active:
                status = 'deactivated'
            elif license_record.expire_date and license_record.expire_date < datetime.utcnow():
                status = 'expired'
            
            # 计算剩余天数
            days_remaining = None
            if license_record.expire_date:
                delta = license_record.expire_date - datetime.utcnow()
                days_remaining = max(0, delta.days)
            
            # 获取设备信息
            device_count = LicenseValidation.query.filter_by(
                activation_code=activation_code,
                is_valid=True
            ).distinct(LicenseValidation.hardware_id).count()
            
            return {
                'exists': True,
                'status': status,
                'license_type': license_record.license_type,
                'user_name': license_record.user_name,
                'email': license_record.email,
                'created_date': license_record.created_date.isoformat(),
                'expire_date': license_record.expire_date.isoformat() if license_record.expire_date else None,
                'days_remaining': days_remaining,
                'validation_count': license_record.validation_count,
                'device_count': device_count,
                'max_devices': license_record.max_activations,
                'is_current_device': license_record.hardware_id == hardware_id if hardware_id else False
            }
            
        except Exception as e:
            logger.error(f"获取许可证状态异常: {e}")
            return {
                'exists': False,
                'error': '获取状态时发生错误'
            }
    
    def deactivate_license(self, activation_code: str, hardware_id: str, reason: str = None) -> Dict[str, Any]:
        """停用许可证"""
        try:
            license_record = LicenseRecord.query.filter_by(
                activation_code=activation_code
            ).first()
            
            if not license_record:
                return {
                    'success': False,
                    'error': '许可证不存在'
                }
            
            # 验证硬件ID
            if license_record.hardware_id and license_record.hardware_id != hardware_id:
                return {
                    'success': False,
                    'error': '硬件ID不匹配'
                }
            
            # 停用许可证
            license_record.is_active = False
            db.session.commit()
            
            logger.info(f"许可证已停用: {activation_code[:8]}... - 原因: {reason}")
            
            return {
                'success': True,
                'message': '许可证已停用'
            }
            
        except Exception as e:
            logger.error(f"停用许可证异常: {e}")
            return {
                'success': False,
                'error': '停用过程中发生错误'
            }
    
    def _get_license_features(self, license_type: str) -> list:
        """获取许可证功能列表"""
        feature_map = {
            'free': ['basic_features'],
            'trial': ['archive_processing', 'package_merger', 'svn_uploader'],
            'professional': ['archive_processing', 'package_merger', 'svn_uploader', 'batch_operations'],
            'enterprise': ['archive_processing', 'package_merger', 'svn_uploader', 'batch_operations', 'advanced_settings', 'api_access']
        }
        
        return feature_map.get(license_type.lower(), ['basic_features'])
    
    def _generate_hardware_fingerprint(self, hardware_info: Dict) -> str:
        """生成硬件指纹"""
        # 组合硬件信息
        fingerprint_data = {
            'cpu': hardware_info.get('cpu', ''),
            'motherboard': hardware_info.get('motherboard', ''),
            'disk': hardware_info.get('disk', ''),
            'mac': hardware_info.get('mac', '')
        }
        
        # 生成哈希
        data_str = json.dumps(fingerprint_data, sort_keys=True)
        return hashlib.sha256(data_str.encode()).hexdigest()
