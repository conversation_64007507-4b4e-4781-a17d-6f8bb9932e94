#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
初始化激活码数据库
"""

import sqlite3
import os
from datetime import datetime, timedelta

def init_license_database():
    """初始化激活码数据库"""
    
    # 确保数据目录存在
    db_dir = os.path.join(os.path.dirname(__file__), '..', 'data', 'database')
    os.makedirs(db_dir, exist_ok=True)
    
    db_path = os.path.join(db_dir, 'license.db')
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 创建激活码表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS activation_codes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            code TEXT UNIQUE NOT NULL,
            license_type TEXT NOT NULL DEFAULT 'standard',
            max_devices INTEGER NOT NULL DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP NOT NULL,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status TEXT NOT NULL DEFAULT 'active',
            notes TEXT,
            created_by TEXT DEFAULT 'system'
        )
    ''')
    
    # 创建活跃许可证表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS active_licenses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            activation_code TEXT NOT NULL,
            machine_id TEXT NOT NULL,
            machine_info TEXT,
            activated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_check TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status TEXT NOT NULL DEFAULT 'active',
            UNIQUE(activation_code, machine_id),
            FOREIGN KEY (activation_code) REFERENCES activation_codes (code)
        )
    ''')
    
    # 创建许可证检查日志表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS license_check_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            activation_code TEXT NOT NULL,
            machine_id TEXT NOT NULL,
            check_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status TEXT NOT NULL,
            ip_address TEXT,
            user_agent TEXT,
            result TEXT
        )
    ''')
    
    # 创建管理员操作日志表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS admin_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            admin_user TEXT NOT NULL,
            action TEXT NOT NULL,
            target TEXT,
            details TEXT,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            ip_address TEXT
        )
    ''')
    
    # 创建索引
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_activation_codes_status ON activation_codes(status)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_activation_codes_expires ON activation_codes(expires_at)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_active_licenses_code ON active_licenses(activation_code)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_active_licenses_machine ON active_licenses(machine_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_license_logs_code ON license_check_logs(activation_code)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_license_logs_time ON license_check_logs(check_time)')
    
    # 插入一些示例数据
    now = datetime.now()
    trial_expire = now + timedelta(days=30)
    full_expire = now + timedelta(days=365)

    sample_codes = [
        ('DEMO-1234-5678-ABCD', 'trial', 1, now.strftime('%Y-%m-%d %H:%M:%S'),
         trial_expire.strftime('%Y-%m-%d %H:%M:%S'),
         'active', '演示用试用版激活码'),
        ('FULL-ABCD-EFGH-1234', 'professional', 3, now.strftime('%Y-%m-%d %H:%M:%S'),
         full_expire.strftime('%Y-%m-%d %H:%M:%S'),
         'active', '专业版激活码'),
        ('ENTP-5678-9012-WXYZ', 'enterprise', 10, now.strftime('%Y-%m-%d %H:%M:%S'),
         full_expire.strftime('%Y-%m-%d %H:%M:%S'),
         'active', '企业版激活码')
    ]
    
    for code_data in sample_codes:
        try:
            cursor.execute('''
                INSERT OR IGNORE INTO activation_codes 
                (code, license_type, max_devices, created_at, expires_at, status, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', code_data)
        except sqlite3.IntegrityError:
            pass  # 忽略重复插入
    
    conn.commit()
    conn.close()
    
    print(f"激活码数据库初始化完成: {db_path}")
    print("示例激活码:")
    for code_data in sample_codes:
        print(f"  {code_data[0]} - {code_data[1]} ({code_data[6]})")

if __name__ == '__main__':
    init_license_database()
