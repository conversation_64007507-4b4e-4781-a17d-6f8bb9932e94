#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
激活码局域网配置测试脚本
"""

import json
import socket
import requests
from pathlib import Path


def get_local_ip():
    """获取本机局域网IP地址"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except Exception:
        return "127.0.0.1"


def test_network_connectivity(server_ip, port=5000):
    """测试网络连通性"""
    print(f"🌐 测试网络连通性: {server_ip}:{port}")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((server_ip, port))
        sock.close()
        
        if result == 0:
            print("   ✅ 端口连通")
            return True
        else:
            print("   ❌ 端口不通")
            return False
    except Exception as e:
        print(f"   ❌ 连接测试失败: {e}")
        return False


def test_http_connection(server_url):
    """测试HTTP连接"""
    print(f"🔗 测试HTTP连接: {server_url}")
    
    try:
        response = requests.get(f"{server_url}/health", timeout=10)
        if response.status_code == 200:
            print("   ✅ HTTP连接正常")
            return True
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
            return False
    except requests.RequestException as e:
        print(f"   ❌ HTTP连接失败: {e}")
        return False


def test_license_api(server_url, api_key="client-api-key-123"):
    """测试激活码API"""
    print(f"🔑 测试激活码API")
    
    try:
        # 测试数据
        test_data = {
            "activation_code": "TEST-1234-5678-ABCD",
            "machine_id": "TEST-MACHINE-ID"
        }
        
        headers = {
            "Content-Type": "application/json",
            "X-API-Key": api_key
        }
        
        response = requests.post(
            f"{server_url}/api/license/validate",
            json=test_data,
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print("   ✅ API响应正常")
            print(f"   响应: {result}")
            return True
        else:
            print(f"   ❌ API错误: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
            
    except requests.RequestException as e:
        print(f"   ❌ API测试失败: {e}")
        return False


def check_client_config():
    """检查客户端配置"""
    print("📋 检查客户端配置")
    
    config_file = Path(__file__).parent.parent / "config" / "user_config.json"
    
    if not config_file.exists():
        print("   ❌ 配置文件不存在")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        license_config = config.get('license_server', {})
        
        print(f"   启用状态: {license_config.get('enabled', False)}")
        print(f"   服务器地址: {license_config.get('server_url', 'N/A')}")
        print(f"   API密钥: {license_config.get('api_key', 'N/A')}")
        print(f"   超时时间: {license_config.get('timeout', 'N/A')}秒")
        
        if license_config.get('enabled') and license_config.get('server_url'):
            print("   ✅ 客户端配置正常")
            return True
        else:
            print("   ❌ 客户端配置不完整")
            return False
            
    except Exception as e:
        print(f"   ❌ 读取配置失败: {e}")
        return False


def check_server_config():
    """检查服务端配置"""
    print("🖥️  检查服务端配置")
    
    server_config_file = Path(__file__).parent.parent.parent / "资管工具平台服务端" / "config" / "config.yaml"
    
    if not server_config_file.exists():
        print("   ❌ 服务端配置文件不存在")
        return False
    
    try:
        with open(server_config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 简单检查关键配置
        if 'host: "0.0.0.0"' in content:
            print("   ✅ 服务器配置为监听所有接口")
        else:
            print("   ⚠️  服务器可能未配置为监听所有接口")
        
        if 'port: 5000' in content:
            print("   ✅ 端口配置为5000")
        else:
            print("   ⚠️  端口配置可能不是5000")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 读取服务端配置失败: {e}")
        return False


def main():
    """主函数"""
    print("🧪 激活码局域网配置测试")
    print("=" * 40)
    
    # 获取本机IP
    local_ip = get_local_ip()
    print(f"🔍 本机IP: {local_ip}")
    
    # 检查配置文件
    print("\n📋 配置检查")
    print("-" * 20)
    client_config_ok = check_client_config()
    server_config_ok = check_server_config()
    
    # 获取服务器地址
    config_file = Path(__file__).parent.parent / "config" / "user_config.json"
    server_url = f"http://{local_ip}:5000"
    
    if config_file.exists():
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            server_url = config.get('license_server', {}).get('server_url', server_url)
        except:
            pass
    
    # 解析服务器地址
    import re
    match = re.match(r'http://([^:]+):(\d+)', server_url)
    if match:
        server_ip = match.group(1)
        server_port = int(match.group(2))
    else:
        server_ip = local_ip
        server_port = 5000
        server_url = f"http://{server_ip}:{server_port}"
    
    print(f"\n🎯 测试目标: {server_url}")
    
    # 网络测试
    print("\n🌐 网络测试")
    print("-" * 20)
    network_ok = test_network_connectivity(server_ip, server_port)
    http_ok = test_http_connection(server_url)
    api_ok = test_license_api(server_url)
    
    # 测试结果汇总
    print("\n📊 测试结果汇总")
    print("=" * 40)
    
    tests = [
        ("客户端配置", client_config_ok),
        ("服务端配置", server_config_ok),
        ("网络连通性", network_ok),
        ("HTTP连接", http_ok),
        ("激活码API", api_ok)
    ]
    
    passed = sum(1 for _, ok in tests if ok)
    total = len(tests)
    
    for test_name, ok in tests:
        status = "✅ 通过" if ok else "❌ 失败"
        print(f"{test_name:12} {status}")
    
    print(f"\n总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！激活码局域网配置正常")
    else:
        print("⚠️  部分测试失败，请检查配置和网络")
        
        print("\n💡 故障排除建议:")
        if not client_config_ok:
            print("- 运行配置脚本更新客户端配置")
        if not server_config_ok:
            print("- 检查服务端配置文件")
        if not network_ok:
            print("- 检查服务器是否启动")
            print("- 检查防火墙设置")
        if not http_ok:
            print("- 确认服务器正在运行")
            print("- 检查端口是否被占用")
        if not api_ok:
            print("- 检查API密钥配置")
            print("- 查看服务器日志")


if __name__ == "__main__":
    main()
