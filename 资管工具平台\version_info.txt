# UTF-8
#
# 版本信息文件，用于Windows可执行文件
#
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1, 0, 0, 0),
    prodvers=(1, 0, 0, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
        StringTable(
          u'080404B0',
          [
            StringStruct(u'CompanyName', u'资管-龚杰翔'),
            StringStruct(u'FileDescription', u'资管工具平台 - 专业的资源管理工具'),
            StringStruct(u'FileVersion', u'*******'),
            StringStruct(u'InternalName', u'ResourceManagementTool'),
            StringStruct(u'LegalCopyright', u'Copyright © 2025 资管-龚杰翔'),
            StringStruct(u'OriginalFilename', u'资管工具平台.exe'),
            StringStruct(u'ProductName', u'资管工具平台'),
            StringStruct(u'ProductVersion', u'*******'),
            StringStruct(u'Comments', u'集成压缩包处理、整包工具、SVN上传等功能的专业工具'),
          ]
        )
      ]
    ),
    VarFileInfo([VarStruct(u'Translation', [2052, 1200])])
  ]
)
