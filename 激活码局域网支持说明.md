# 🌐 激活码系统局域网支持

## 📋 功能概述

激活码系统现已支持局域网部署，允许在局域网内设置统一的激活码服务器，所有客户端都可以连接到这个服务器进行许可证验证。

### ✨ 主要特性

- 🏠 **局域网部署** - 支持在局域网内部署激活码服务器
- 🔄 **在线/离线混合验证** - 优先在线验证，支持离线备用
- 🛡️ **安全认证** - API密钥认证，确保通信安全
- 📊 **集中管理** - Web管理界面，统一管理所有激活码
- 🔧 **自动配置** - 一键配置脚本，简化部署过程

## 🚀 快速开始

### 1. 自动配置（推荐）

运行自动配置脚本：

```bash
# 方法1：Python脚本
cd 资管工具平台\scripts
python setup_lan_license_server.py

# 方法2：批处理脚本
双击运行: setup_lan_license.bat
```

### 2. 启动服务器

```bash
cd 资管工具平台服务端
python start_license_server.py
```

### 3. 测试配置

```bash
# 测试局域网配置
cd 资管工具平台\scripts
python test_lan_license.py

# 或双击运行
test_lan_license.bat
```

## 📁 新增文件说明

### 配置脚本

- `scripts/setup_lan_license_server.py` - 局域网配置主脚本
- `scripts/setup_lan_license.bat` - 配置脚本快捷启动
- `scripts/setup_firewall.bat` - 防火墙规则配置（自动生成）

### 测试脚本

- `scripts/test_lan_license.py` - 局域网配置测试脚本
- `scripts/test_lan_license.bat` - 测试脚本快捷启动

### 启动脚本

- `资管工具平台服务端/start_license_server.py` - Python启动脚本（自动生成）
- `资管工具平台服务端/start_license_server.bat` - 批处理启动脚本（自动生成）

### 文档

- `激活码局域网部署指南.md` - 详细部署指南
- `激活码局域网支持说明.md` - 功能说明（本文件）

## 🔧 配置说明

### 自动配置内容

配置脚本会自动完成以下操作：

1. **检测网络环境**
   - 自动获取本机局域网IP地址
   - 提供多种配置选项

2. **更新客户端配置**
   - 修改 `config/user_config.json`
   - 修改 `config/default_config.json`
   - 设置服务器地址和API密钥

3. **更新服务端配置**
   - 修改 `资管工具平台服务端/config/config.yaml`
   - 设置监听地址为 `0.0.0.0`（支持局域网访问）

4. **更新代码配置**
   - 修改在线验证器默认服务器地址
   - 确保各组件配置一致

5. **生成启动脚本**
   - 创建便捷的服务器启动脚本
   - 支持Windows批处理和Python脚本

6. **配置防火墙**
   - 生成防火墙规则配置脚本
   - 检查当前防火墙状态

## 🌐 网络架构

```
局域网环境:
┌─────────────────────────────────────────┐
│                局域网                    │
│                                         │
│  ┌─────────────┐    ┌─────────────┐    │
│  │   客户端1    │    │   客户端2    │    │
│  │192.168.1.10 │    │192.168.1.20 │    │
│  └─────────────┘    └─────────────┘    │
│         │                   │          │
│         └───────────┬───────────┘      │
│                     │                  │
│            ┌─────────────┐             │
│            │ 激活码服务器  │             │
│            │*************│             │
│            │   端口:5000  │             │
│            └─────────────┘             │
└─────────────────────────────────────────┘
```

## 🔑 使用流程

### 服务器端

1. **部署服务器**
   - 选择一台稳定的局域网设备
   - 运行配置脚本
   - 启动激活码服务器

2. **管理激活码**
   - 访问 `http://服务器IP:5000/admin`
   - 使用管理员账号登录（admin/admin123）
   - 创建和管理激活码

### 客户端

1. **配置连接**
   - 配置脚本会自动更新客户端配置
   - 或手动修改配置文件中的服务器地址

2. **使用激活码**
   - 在软件中输入激活码
   - 系统会自动连接服务器验证
   - 支持离线使用（有宽限期）

## 🛠️ 手动配置

如果需要手动配置，请参考以下步骤：

### 客户端配置

编辑 `config/user_config.json`：

```json
{
  "license_server": {
    "enabled": true,
    "server_url": "http://*************:5000",
    "api_key": "client-api-key-123",
    "timeout": 30,
    "offline_grace_period": 86400
  }
}
```

### 服务端配置

编辑 `资管工具平台服务端/config/config.yaml`：

```yaml
app:
  host: "0.0.0.0"  # 监听所有网络接口
  port: 5000       # 服务端口

security:
  api_keys:
    client: "client-api-key-123"
    admin: "admin-api-key-456"
```

## 🔍 故障排除

### 常见问题

1. **无法连接服务器**
   - 检查服务器是否启动
   - 确认IP地址和端口
   - 检查防火墙设置

2. **激活码验证失败**
   - 检查API密钥配置
   - 确认激活码有效性
   - 查看服务器日志

3. **网络连接问题**
   - 使用测试脚本检查连通性
   - 确认局域网配置
   - 检查路由设置

### 测试工具

使用内置测试脚本：

```bash
python scripts/test_lan_license.py
```

测试内容包括：
- 客户端配置检查
- 服务端配置检查
- 网络连通性测试
- HTTP连接测试
- API接口测试

## 📊 优势对比

| 功能 | 本地验证 | 局域网验证 |
|------|----------|------------|
| 部署复杂度 | 简单 | 中等 |
| 管理便利性 | 分散 | 集中 |
| 安全性 | 中等 | 高 |
| 扩展性 | 有限 | 良好 |
| 离线支持 | 完全 | 有宽限期 |
| 统计分析 | 无 | 完整 |

## 🎯 适用场景

- **企业内部部署** - 统一管理企业内所有软件许可证
- **局域网环境** - 无法或不便连接外网的环境
- **集中管理需求** - 需要统一管理和监控许可证使用情况
- **安全要求高** - 不希望许可证信息传输到外网

---

**现在您的激活码系统已经支持局域网部署了！** 🎉

如有问题，请查看详细的部署指南或使用测试脚本进行诊断。
