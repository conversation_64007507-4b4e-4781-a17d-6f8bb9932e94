#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
激活码服务API路由
"""

from flask import Blueprint, request, jsonify, current_app
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from loguru import logger
from datetime import datetime, timedelta

from src.auth.middleware import api_key_required
from src.utils.response import success_response, error_response
from src.utils.validators import validate_json, validate_activation_code
from .models import LicenseRecord, LicenseValidation
from .validator import LicenseValidator
from src.database import db

# 创建蓝图
license_bp = Blueprint('license', __name__)

# 创建验证器实例
validator = LicenseValidator()


@license_bp.route('/validate', methods=['POST'])
@api_key_required
def validate_license():
    """验证激活码"""
    try:
        # 验证请求数据
        data = validate_json(request, required_fields=['activation_code', 'hardware_id'])
        
        activation_code = data['activation_code']
        hardware_id = data['hardware_id']
        user_info = data.get('user_info', {})
        
        # 验证激活码格式
        if not validate_activation_code(activation_code):
            return error_response('激活码格式无效', 400)
        
        # 执行验证
        result = validator.validate_license(activation_code, hardware_id, user_info)
        
        if result['success']:
            # 记录验证日志
            validation_record = LicenseValidation(
                activation_code=activation_code,
                hardware_id=hardware_id,
                validation_time=datetime.utcnow(),
                is_valid=True,
                client_ip=request.remote_addr,
                user_agent=request.headers.get('User-Agent', '')
            )
            db.session.add(validation_record)
            db.session.commit()
            
            logger.info(f"激活码验证成功: {activation_code[:8]}...")
            
            return success_response({
                'valid': True,
                'license_info': result['license_info'],
                'expires_at': result.get('expires_at'),
                'features': result.get('features', []),
                'validation_id': validation_record.id
            })
        else:
            # 记录失败日志
            validation_record = LicenseValidation(
                activation_code=activation_code,
                hardware_id=hardware_id,
                validation_time=datetime.utcnow(),
                is_valid=False,
                error_message=result.get('error', 'Unknown error'),
                client_ip=request.remote_addr,
                user_agent=request.headers.get('User-Agent', '')
            )
            db.session.add(validation_record)
            db.session.commit()
            
            logger.warning(f"激活码验证失败: {activation_code[:8]}... - {result.get('error')}")
            
            return error_response(result.get('error', '激活码验证失败'), 400)
    
    except Exception as e:
        logger.error(f"激活码验证异常: {e}")
        return error_response('服务器内部错误', 500)


@license_bp.route('/status', methods=['GET'])
@api_key_required
def get_license_status():
    """查询许可证状态"""
    try:
        activation_code = request.args.get('activation_code')
        hardware_id = request.args.get('hardware_id')
        
        if not activation_code:
            return error_response('缺少激活码参数', 400)
        
        # 查询许可证记录
        license_record = LicenseRecord.query.filter_by(
            activation_code=activation_code
        ).first()
        
        if not license_record:
            return error_response('许可证不存在', 404)
        
        # 获取状态信息
        status_info = validator.get_license_status(activation_code, hardware_id)
        
        return success_response(status_info)
    
    except Exception as e:
        logger.error(f"查询许可证状态异常: {e}")
        return error_response('服务器内部错误', 500)


@license_bp.route('/deactivate', methods=['POST'])
@api_key_required
def deactivate_license():
    """停用许可证"""
    try:
        data = validate_json(request, required_fields=['activation_code', 'hardware_id'])
        
        activation_code = data['activation_code']
        hardware_id = data['hardware_id']
        reason = data.get('reason', 'User requested')
        
        # 执行停用
        result = validator.deactivate_license(activation_code, hardware_id, reason)
        
        if result['success']:
            logger.info(f"许可证停用成功: {activation_code[:8]}...")
            return success_response({'message': '许可证已停用'})
        else:
            return error_response(result.get('error', '停用失败'), 400)
    
    except Exception as e:
        logger.error(f"停用许可证异常: {e}")
        return error_response('服务器内部错误', 500)


@license_bp.route('/register', methods=['POST'])
@api_key_required
def register_license():
    """注册新许可证"""
    try:
        data = validate_json(request, required_fields=[
            'activation_code', 'license_type', 'user_name', 'email'
        ])
        
        # 创建许可证记录
        license_record = LicenseRecord(
            activation_code=data['activation_code'],
            license_type=data['license_type'],
            user_name=data['user_name'],
            email=data['email'],
            company_name=data.get('company_name', ''),
            created_date=datetime.utcnow(),
            expire_date=data.get('expire_date'),
            max_activations=data.get('max_activations', 1),
            is_active=True
        )
        
        db.session.add(license_record)
        db.session.commit()
        
        logger.info(f"许可证注册成功: {data['activation_code'][:8]}...")
        
        return success_response({
            'message': '许可证注册成功',
            'license_id': license_record.id
        })
    
    except Exception as e:
        logger.error(f"注册许可证异常: {e}")
        db.session.rollback()
        return error_response('服务器内部错误', 500)


@license_bp.route('/statistics', methods=['GET'])
@api_key_required
def get_license_statistics():
    """获取许可证统计信息"""
    try:
        # 统计各种状态的许可证数量
        total_licenses = LicenseRecord.query.count()
        active_licenses = LicenseRecord.query.filter_by(is_active=True).count()
        expired_licenses = LicenseRecord.query.filter(
            LicenseRecord.expire_date < datetime.utcnow()
        ).count()
        
        # 统计验证次数
        total_validations = LicenseValidation.query.count()
        successful_validations = LicenseValidation.query.filter_by(is_valid=True).count()
        
        # 统计最近24小时的验证
        yesterday = datetime.utcnow() - timedelta(days=1)
        recent_validations = LicenseValidation.query.filter(
            LicenseValidation.validation_time >= yesterday
        ).count()
        
        # 按许可证类型统计
        license_types = db.session.query(
            LicenseRecord.license_type,
            db.func.count(LicenseRecord.id)
        ).group_by(LicenseRecord.license_type).all()
        
        type_stats = {license_type: count for license_type, count in license_types}
        
        return success_response({
            'total_licenses': total_licenses,
            'active_licenses': active_licenses,
            'expired_licenses': expired_licenses,
            'total_validations': total_validations,
            'successful_validations': successful_validations,
            'recent_validations': recent_validations,
            'license_types': type_stats,
            'success_rate': round(successful_validations / max(total_validations, 1) * 100, 2)
        })
    
    except Exception as e:
        logger.error(f"获取许可证统计异常: {e}")
        return error_response('服务器内部错误', 500)


@license_bp.route('/search', methods=['GET'])
@api_key_required
def search_licenses():
    """搜索许可证"""
    try:
        # 获取搜索参数
        query = request.args.get('q', '')
        license_type = request.args.get('type')
        status = request.args.get('status')
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 20)), 100)
        
        # 构建查询
        licenses_query = LicenseRecord.query
        
        if query:
            licenses_query = licenses_query.filter(
                db.or_(
                    LicenseRecord.activation_code.contains(query),
                    LicenseRecord.user_name.contains(query),
                    LicenseRecord.email.contains(query),
                    LicenseRecord.company_name.contains(query)
                )
            )
        
        if license_type:
            licenses_query = licenses_query.filter_by(license_type=license_type)
        
        if status == 'active':
            licenses_query = licenses_query.filter_by(is_active=True)
        elif status == 'inactive':
            licenses_query = licenses_query.filter_by(is_active=False)
        elif status == 'expired':
            licenses_query = licenses_query.filter(
                LicenseRecord.expire_date < datetime.utcnow()
            )
        
        # 分页查询
        pagination = licenses_query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        # 格式化结果
        licenses = []
        for license_record in pagination.items:
            licenses.append({
                'id': license_record.id,
                'activation_code': license_record.activation_code,
                'license_type': license_record.license_type,
                'user_name': license_record.user_name,
                'email': license_record.email,
                'company_name': license_record.company_name,
                'created_date': license_record.created_date.isoformat(),
                'expire_date': license_record.expire_date.isoformat() if license_record.expire_date else None,
                'is_active': license_record.is_active,
                'validation_count': license_record.validation_count
            })
        
        return success_response({
            'licenses': licenses,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': pagination.total,
                'pages': pagination.pages,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev
            }
        })
    
    except Exception as e:
        logger.error(f"搜索许可证异常: {e}")
        return error_response('服务器内部错误', 500)
