#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
许可证管理后台界面
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTabWidget,
                            QWidget, QLabel, QLineEdit, QPushButton, QTextEdit,
                            QGroupBox, QGridLayout, QMessageBox, QTableWidget,
                            QTableWidgetItem, QHeaderView, QComboBox, QSpinBox,
                            QDateEdit, QCheckBox, QProgressBar, QSplitter)
from PyQt6.QtCore import Qt, pyqtSignal, QDate, QTimer, QThread
from PyQt6.QtGui import QFont, QIcon

from license.license_server import LicenseServer, LicenseServerAPI
from license.activation_code import ActivationCodeGenerator
from license.license_types import LicenseType
from datetime import datetime, timedelta


class LicenseGeneratorWorker(QThread):
    """许可证生成工作线程"""
    
    generation_finished = pyqtSignal(list)  # 生成完成信号
    progress_updated = pyqtSignal(int)      # 进度更新信号
    
    def __init__(self, license_data_list):
        super().__init__()
        self.license_data_list = license_data_list
    
    def run(self):
        """执行批量生成"""
        generator = ActivationCodeGenerator()
        generated_codes = []
        
        for i, license_data in enumerate(self.license_data_list):
            activation_code = generator.generate_activation_code(license_data)
            generated_codes.append({
                'activation_code': activation_code,
                'license_data': license_data
            })
            
            # 更新进度
            progress = int((i + 1) / len(self.license_data_list) * 100)
            self.progress_updated.emit(progress)
        
        self.generation_finished.emit(generated_codes)


class LicenseAdminDialog(QDialog):
    """许可证管理后台对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.server = LicenseServer()
        self.api = LicenseServerAPI(self.server)
        self.generator = ActivationCodeGenerator()
        
        self.init_ui()
        self.setup_connections()
        self.load_data()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("许可证管理后台")
        self.setModal(True)
        self.resize(1000, 700)
        
        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 创建标题
        self.create_header(layout)
        
        # 创建主要内容区域
        self.create_main_content(layout)
        
        # 创建状态栏
        self.create_status_bar(layout)
    
    def create_header(self, layout):
        """创建标题区域"""
        header_layout = QHBoxLayout()
        
        title_label = QLabel("许可证管理后台")
        title_label.setFont(QFont("Microsoft YaHei UI", 16, QFont.Weight.Bold))
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # 刷新按钮
        self.refresh_button = QPushButton("刷新数据")
        self.refresh_button.setMinimumSize(100, 30)
        header_layout.addWidget(self.refresh_button)
        
        layout.addLayout(header_layout)
    
    def create_main_content(self, layout):
        """创建主要内容区域"""
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧：统计和控制面板
        self.create_control_panel(splitter)
        
        # 右侧：数据表格
        self.create_data_panel(splitter)
        
        # 设置分割比例
        splitter.setSizes([300, 700])
        
        layout.addWidget(splitter)
    
    def create_control_panel(self, parent):
        """创建控制面板"""
        control_widget = QWidget()
        layout = QVBoxLayout(control_widget)
        
        # 统计信息组
        stats_group = QGroupBox("统计信息")
        stats_layout = QGridLayout(stats_group)
        
        self.total_licenses_label = QLabel("0")
        self.active_licenses_label = QLabel("0")
        self.today_validations_label = QLabel("0")
        
        stats_layout.addWidget(QLabel("总许可证数:"), 0, 0)
        stats_layout.addWidget(self.total_licenses_label, 0, 1)
        stats_layout.addWidget(QLabel("活跃许可证:"), 1, 0)
        stats_layout.addWidget(self.active_licenses_label, 1, 1)
        stats_layout.addWidget(QLabel("今日验证:"), 2, 0)
        stats_layout.addWidget(self.today_validations_label, 2, 1)
        
        layout.addWidget(stats_group)
        
        # 生成许可证组
        generate_group = QGroupBox("生成许可证")
        generate_layout = QVBoxLayout(generate_group)
        
        # 许可证类型
        type_layout = QHBoxLayout()
        type_layout.addWidget(QLabel("类型:"))
        self.license_type_combo = QComboBox()
        self.license_type_combo.addItems(["试用版", "专业版", "企业版"])
        type_layout.addWidget(self.license_type_combo)
        generate_layout.addLayout(type_layout)
        
        # 数量
        count_layout = QHBoxLayout()
        count_layout.addWidget(QLabel("数量:"))
        self.license_count_spin = QSpinBox()
        self.license_count_spin.setRange(1, 1000)
        self.license_count_spin.setValue(1)
        count_layout.addWidget(self.license_count_spin)
        generate_layout.addLayout(count_layout)
        
        # 有效期
        expire_layout = QHBoxLayout()
        expire_layout.addWidget(QLabel("有效期:"))
        self.expire_date_edit = QDateEdit()
        self.expire_date_edit.setDate(QDate.currentDate().addDays(365))
        expire_layout.addWidget(self.expire_date_edit)
        generate_layout.addLayout(expire_layout)
        
        # 生成按钮
        self.generate_button = QPushButton("批量生成")
        self.generate_button.setMinimumHeight(35)
        generate_layout.addWidget(self.generate_button)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        generate_layout.addWidget(self.progress_bar)
        
        layout.addWidget(generate_group)
        
        # 操作组
        actions_group = QGroupBox("操作")
        actions_layout = QVBoxLayout(actions_group)
        
        self.export_button = QPushButton("导出许可证")
        self.import_button = QPushButton("导入许可证")
        self.cleanup_button = QPushButton("清理过期")
        
        actions_layout.addWidget(self.export_button)
        actions_layout.addWidget(self.import_button)
        actions_layout.addWidget(self.cleanup_button)
        
        layout.addWidget(actions_group)
        
        layout.addStretch()
        parent.addWidget(control_widget)
    
    def create_data_panel(self, parent):
        """创建数据面板"""
        data_widget = QWidget()
        layout = QVBoxLayout(data_widget)
        
        # 搜索栏
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("搜索:"))
        self.search_entry = QLineEdit()
        self.search_entry.setPlaceholderText("输入激活码或用户名...")
        search_layout.addWidget(self.search_entry)
        
        self.search_button = QPushButton("搜索")
        search_layout.addWidget(self.search_button)
        
        layout.addLayout(search_layout)
        
        # 许可证表格
        self.license_table = QTableWidget()
        self.license_table.setColumnCount(8)
        self.license_table.setHorizontalHeaderLabels([
            "激活码", "类型", "用户", "邮箱", "状态", "创建日期", "到期日期", "验证次数"
        ])
        
        # 设置表格属性
        header = self.license_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)
        
        self.license_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.license_table.setAlternatingRowColors(True)
        
        layout.addWidget(self.license_table)
        
        # 详情面板
        details_group = QGroupBox("许可证详情")
        details_layout = QVBoxLayout(details_group)
        
        self.details_text = QTextEdit()
        self.details_text.setMaximumHeight(150)
        self.details_text.setReadOnly(True)
        details_layout.addWidget(self.details_text)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        self.deactivate_button = QPushButton("停用")
        self.activate_button = QPushButton("激活")
        self.delete_button = QPushButton("删除")
        
        button_layout.addWidget(self.deactivate_button)
        button_layout.addWidget(self.activate_button)
        button_layout.addWidget(self.delete_button)
        button_layout.addStretch()
        
        details_layout.addLayout(button_layout)
        layout.addWidget(details_group)
        
        parent.addWidget(data_widget)
    
    def create_status_bar(self, layout):
        """创建状态栏"""
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("QLabel { padding: 5px; background-color: #f0f0f0; }")
        layout.addWidget(self.status_label)
    
    def setup_connections(self):
        """设置信号连接"""
        self.refresh_button.clicked.connect(self.load_data)
        self.generate_button.clicked.connect(self.generate_licenses)
        self.search_button.clicked.connect(self.search_licenses)
        self.license_table.itemSelectionChanged.connect(self.on_selection_changed)
        
        self.deactivate_button.clicked.connect(self.deactivate_license)
        self.activate_button.clicked.connect(self.activate_license)
        self.delete_button.clicked.connect(self.delete_license)
        
        self.export_button.clicked.connect(self.export_licenses)
        self.import_button.clicked.connect(self.import_licenses)
        self.cleanup_button.clicked.connect(self.cleanup_expired)
    
    def load_data(self):
        """加载数据"""
        self.status_label.setText("正在加载数据...")
        
        # 加载统计信息
        stats = self.server.get_license_statistics()
        self.total_licenses_label.setText(str(stats.get('total_licenses', 0)))
        self.active_licenses_label.setText(str(stats.get('active_licenses', 0)))
        self.today_validations_label.setText(str(stats.get('today_validations', 0)))
        
        # 加载许可证列表
        self.load_license_table()
        
        self.status_label.setText("数据加载完成")
    
    def load_license_table(self):
        """加载许可证表格"""
        # 这里应该从数据库加载所有许可证
        # 暂时使用示例数据
        self.license_table.setRowCount(0)
        
        # 示例数据
        sample_licenses = [
            {
                'activation_code': '0003-01Z0-FBQU-FBQM-9PBE-X005-9A',
                'license_type': 'professional',
                'user_name': '专业用户',
                'email': '<EMAIL>',
                'is_active': True,
                'created_date': '2024-01-15',
                'expire_date': '2024-12-31',
                'validation_count': 15
            }
        ]
        
        for license_data in sample_licenses:
            self.add_license_to_table(license_data)
    
    def add_license_to_table(self, license_data):
        """添加许可证到表格"""
        row = self.license_table.rowCount()
        self.license_table.insertRow(row)
        
        self.license_table.setItem(row, 0, QTableWidgetItem(license_data['activation_code']))
        self.license_table.setItem(row, 1, QTableWidgetItem(license_data['license_type']))
        self.license_table.setItem(row, 2, QTableWidgetItem(license_data['user_name']))
        self.license_table.setItem(row, 3, QTableWidgetItem(license_data['email']))
        self.license_table.setItem(row, 4, QTableWidgetItem('激活' if license_data['is_active'] else '停用'))
        self.license_table.setItem(row, 5, QTableWidgetItem(license_data['created_date']))
        self.license_table.setItem(row, 6, QTableWidgetItem(license_data['expire_date']))
        self.license_table.setItem(row, 7, QTableWidgetItem(str(license_data['validation_count'])))
    
    def generate_licenses(self):
        """生成许可证"""
        license_type_map = {
            0: LicenseType.TRIAL,
            1: LicenseType.PROFESSIONAL,
            2: LicenseType.ENTERPRISE
        }
        
        license_type = license_type_map[self.license_type_combo.currentIndex()]
        count = self.license_count_spin.value()
        expire_date = self.expire_date_edit.date().toPython()
        
        # 创建许可证数据列表
        license_data_list = []
        for i in range(count):
            license_data = {
                'license_type': license_type,
                'user_name': f'用户{i+1:03d}',
                'email': f'user{i+1:03d}@example.com',
                'expire_date': expire_date.isoformat()
            }
            license_data_list.append(license_data)
        
        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.generate_button.setEnabled(False)
        
        # 启动生成线程
        self.generator_worker = LicenseGeneratorWorker(license_data_list)
        self.generator_worker.progress_updated.connect(self.progress_bar.setValue)
        self.generator_worker.generation_finished.connect(self.on_generation_finished)
        self.generator_worker.start()
    
    def on_generation_finished(self, generated_codes):
        """生成完成处理"""
        self.progress_bar.setVisible(False)
        self.generate_button.setEnabled(True)
        
        # 注册生成的许可证
        success_count = 0
        for code_data in generated_codes:
            license_data = code_data['license_data'].copy()
            license_data['activation_code'] = code_data['activation_code']
            
            success, message = self.server.register_license(license_data)
            if success:
                success_count += 1
        
        QMessageBox.information(
            self, "生成完成",
            f"成功生成并注册了 {success_count}/{len(generated_codes)} 个许可证"
        )
        
        # 刷新数据
        self.load_data()
    
    def search_licenses(self):
        """搜索许可证"""
        search_text = self.search_entry.text().strip()
        if not search_text:
            self.load_license_table()
            return
        
        # 实现搜索逻辑
        self.status_label.setText(f"搜索: {search_text}")
    
    def on_selection_changed(self):
        """选择变更处理"""
        current_row = self.license_table.currentRow()
        if current_row >= 0:
            activation_code = self.license_table.item(current_row, 0).text()
            license_info = self.server.get_license_info(activation_code)
            
            if license_info:
                details = f"""
激活码: {license_info['activation_code']}
许可证类型: {license_info['license_type']}
用户名: {license_info['user_name']}
邮箱: {license_info['email']}
公司: {license_info['company_name']}
硬件ID: {license_info['hardware_id']}
创建时间: {license_info['created_date']}
到期时间: {license_info['expire_date']}
验证次数: {license_info['validation_count']}
状态: {'激活' if license_info['is_active'] else '停用'}
                """.strip()
                
                self.details_text.setText(details)
    
    def deactivate_license(self):
        """停用许可证"""
        current_row = self.license_table.currentRow()
        if current_row >= 0:
            activation_code = self.license_table.item(current_row, 0).text()
            
            reply = QMessageBox.question(
                self, "确认停用",
                f"确定要停用许可证 {activation_code} 吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                success, message = self.server.deactivate_license(activation_code)
                if success:
                    QMessageBox.information(self, "成功", message)
                    self.load_data()
                else:
                    QMessageBox.warning(self, "失败", message)
    
    def activate_license(self):
        """激活许可证"""
        QMessageBox.information(self, "提示", "激活功能待实现")
    
    def delete_license(self):
        """删除许可证"""
        QMessageBox.information(self, "提示", "删除功能待实现")
    
    def export_licenses(self):
        """导出许可证"""
        QMessageBox.information(self, "提示", "导出功能待实现")
    
    def import_licenses(self):
        """导入许可证"""
        QMessageBox.information(self, "提示", "导入功能待实现")
    
    def cleanup_expired(self):
        """清理过期许可证"""
        QMessageBox.information(self, "提示", "清理功能待实现")
