#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资管工具平台 - 主程序入口
集成压缩包处理、整包工具、载包上传等功能的统一桌面应用
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt, QDir
from PyQt6.QtGui import QIcon, QFont

from ui.main_window import MainWindow
from utils.config_manager import ConfigManager
from utils.theme_manager import ThemeManager


class ResourceManager:
    """资源管理器"""
    
    @staticmethod
    def get_resource_path(relative_path):
        """获取资源文件的绝对路径"""
        if hasattr(sys, '_MEIPASS'):
            # PyInstaller打包后的路径
            return os.path.join(sys._MEIPASS, relative_path)
        return os.path.join(project_root, 'resources', relative_path)
    
    @staticmethod
    def get_config_path(relative_path):
        """获取配置文件的绝对路径"""
        if hasattr(sys, '_MEIPASS'):
            return os.path.join(sys._MEIPASS, 'config', relative_path)
        return os.path.join(project_root, 'config', relative_path)


def setup_application():
    """设置应用程序基本配置"""
    app = QApplication(sys.argv)
    
    # 设置应用程序基本信息
    app.setApplicationName("资管工具平台")
    app.setApplicationVersion("1.0.1")
    app.setOrganizationName("资源管理部")
    app.setOrganizationDomain("internal.company.com")
    
    # 设置应用程序图标
    icon_path = ResourceManager.get_resource_path("icons/app_icon.png")
    if os.path.exists(icon_path):
        app.setWindowIcon(QIcon(icon_path))
    
    # 设置默认字体
    font = QFont("Microsoft YaHei UI", 9)
    app.setFont(font)
    
    # 启用高DPI支持 (PyQt6中这些属性已经默认启用)
    try:
        app.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
        app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
    except AttributeError:
        # PyQt6中这些属性可能不存在或已默认启用
        pass
    
    return app


def main():
    """主函数"""
    try:
        # 创建应用程序
        app = setup_application()
        
        # 初始化配置管理器
        config_manager = ConfigManager()
        
        # 初始化主题管理器
        theme_manager = ThemeManager()
        theme_manager.apply_theme(app)
        
        # 创建主窗口
        main_window = MainWindow()
        main_window.show()
        
        # 运行应用程序
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"应用程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
