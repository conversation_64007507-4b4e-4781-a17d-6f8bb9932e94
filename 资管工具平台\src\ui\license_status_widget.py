#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
许可证状态显示组件
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QFrame, QProgressBar, QGroupBox)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QPixmap, QIcon

from license.license_manager import LicenseManager
from license.license_types import LicenseType, LicenseStatus, LicenseFeatures


class LicenseStatusWidget(QWidget):
    """许可证状态显示组件"""
    
    # 信号定义
    activation_requested = pyqtSignal()  # 请求激活信号
    upgrade_requested = pyqtSignal()    # 请求升级信号
    
    def __init__(self, license_manager: LicenseManager, parent=None):
        super().__init__(parent)
        self.license_manager = license_manager
        self.init_ui()
        self.setup_connections()
        self.update_status()
        
        # 定时更新状态
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_status)
        self.update_timer.start(60000)  # 每分钟更新一次
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)  # 增加边距
        layout.setSpacing(15)  # 增加间距
        
        # 创建状态显示区域
        self.create_status_display(layout)

        # 移除功能状态区域，界面更简洁
        # self.create_feature_status(layout)

        # 创建操作按钮区域
        self.create_action_buttons(layout)
    
    def create_status_display(self, layout):
        """创建状态显示区域"""
        status_frame = QFrame()
        status_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        status_frame.setMinimumHeight(180)  # 增加状态区域高度
        status_layout = QVBoxLayout(status_frame)
        status_layout.setContentsMargins(20, 20, 20, 20)  # 增加边距
        status_layout.setSpacing(10)  # 增加间距
        
        # 许可证类型和状态
        header_layout = QHBoxLayout()
        
        # 状态图标
        self.status_icon = QLabel()
        self.status_icon.setFixedSize(32, 32)
        self.status_icon.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(self.status_icon)
        
        # 状态信息
        status_info_layout = QVBoxLayout()
        
        self.license_type_label = QLabel()
        self.license_type_label.setFont(QFont("Microsoft YaHei UI", 14, QFont.Weight.Bold))  # 增加字体大小
        status_info_layout.addWidget(self.license_type_label)

        self.status_label = QLabel()
        self.status_label.setFont(QFont("Microsoft YaHei UI", 11))  # 增加字体大小
        status_info_layout.addWidget(self.status_label)
        
        header_layout.addLayout(status_info_layout)
        header_layout.addStretch()
        
        status_layout.addLayout(header_layout)
        
        # 到期信息
        self.expiry_layout = QVBoxLayout()
        
        self.expiry_label = QLabel()
        self.expiry_label.setFont(QFont("Microsoft YaHei UI", 9))
        self.expiry_layout.addWidget(self.expiry_label)
        
        # 到期进度条
        self.expiry_progress = QProgressBar()
        self.expiry_progress.setVisible(False)
        self.expiry_progress.setMaximumHeight(8)
        self.expiry_layout.addWidget(self.expiry_progress)
        
        status_layout.addLayout(self.expiry_layout)
        
        # 用户信息
        self.user_info_label = QLabel()
        self.user_info_label.setFont(QFont("Microsoft YaHei UI", 10))  # 增加字体大小
        self.user_info_label.setStyleSheet("color: #666666; padding: 5px;")
        status_layout.addWidget(self.user_info_label)
        
        layout.addWidget(status_frame)
    
    def create_feature_status(self, layout):
        """创建功能状态区域"""
        feature_group = QGroupBox("功能状态")
        feature_group.setFont(QFont("Microsoft YaHei UI", 12))  # 增加组标题字体
        feature_group.setMinimumHeight(250)  # 增加最小高度
        feature_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 8px;
                margin-top: 5px;
                padding-top: 5px;
                background-color: #f8f9fa;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                background-color: white;
            }
        """)
        feature_layout = QVBoxLayout(feature_group)
        feature_layout.setSpacing(5)  # 减少间距以显示更多项目
        feature_layout.setContentsMargins(15, 15, 15, 15)  # 增加内边距

        self.feature_labels = {}

        # 主要功能列表
        main_features = [
            ("archive_processor", "压缩包处理"),
            ("package_merger", "整包工具"),
            ("svn_uploader", "SVN上传"),
            ("themes", "主题切换"),
            ("auto_update", "自动更新")
        ]

        for feature_key, feature_name in main_features:
            # 创建功能项容器
            feature_item_widget = QWidget()
            feature_item_widget.setMinimumHeight(30)  # 减少高度以显示更多项目
            feature_item_widget.setStyleSheet("""
                QWidget {
                    background-color: white;
                    border: 1px solid #e0e0e0;
                    border-radius: 4px;
                    margin: 2px 0px;
                }
                QWidget:hover {
                    background-color: #f0f8ff;
                }
            """)
            feature_layout_item = QHBoxLayout(feature_item_widget)
            feature_layout_item.setSpacing(15)  # 增加间距
            feature_layout_item.setContentsMargins(10, 5, 10, 5)  # 增加内边距

            # 功能名称
            name_label = QLabel(feature_name)
            name_label.setFont(QFont("Microsoft YaHei UI", 11))  # 增加字体大小
            name_label.setMinimumWidth(120)  # 增加宽度
            feature_layout_item.addWidget(name_label)

            # 状态标签
            status_label = QLabel("⏳ 检查中...")
            status_label.setFont(QFont("Microsoft YaHei UI", 11))  # 增加字体大小
            status_label.setMinimumWidth(100)  # 增加宽度
            self.feature_labels[feature_key] = status_label
            feature_layout_item.addWidget(status_label)

            feature_layout_item.addStretch()
            feature_layout.addWidget(feature_item_widget)

        layout.addWidget(feature_group)

    def create_action_buttons(self, layout):
        """创建操作按钮区域"""
        # 添加一些间距
        layout.addSpacing(20)

        button_layout = QHBoxLayout()
        
        self.activate_button = QPushButton("激活软件")
        self.activate_button.setMinimumHeight(35)
        button_layout.addWidget(self.activate_button)
        
        self.upgrade_button = QPushButton("升级版本")
        self.upgrade_button.setMinimumHeight(35)
        button_layout.addWidget(self.upgrade_button)
        
        layout.addLayout(button_layout)
    
    def setup_connections(self):
        """设置信号连接"""
        self.activate_button.clicked.connect(self.activation_requested.emit)
        self.upgrade_button.clicked.connect(self.upgrade_requested.emit)
        
        # 连接许可证管理器信号
        self.license_manager.license_changed.connect(self.on_license_changed)
        self.license_manager.license_expired.connect(self.on_license_expired)
        self.license_manager.license_warning.connect(self.on_license_warning)
    
    def update_status(self):
        """更新状态显示"""
        license_info = self.license_manager.current_license
        if license_info is None:
            self._show_no_license_status()
            return
        
        # 更新许可证类型
        license_type_name = LicenseFeatures.get_license_display_name(license_info.license_type)
        self.license_type_label.setText(license_type_name)
        
        # 更新状态
        status = self.license_manager.get_license_status()
        self._update_status_display(status)
        
        # 更新到期信息
        self._update_expiry_info(license_info)
        
        # 更新用户信息
        self._update_user_info(license_info)
        
        # 更新功能状态 - 已移除功能状态区域
        # self._update_feature_status(license_info)
        
        # 更新按钮状态
        self._update_button_status(license_info, status)
    
    def _show_no_license_status(self):
        """显示无许可证状态"""
        self.license_type_label.setText("未激活")
        self.status_label.setText("请激活软件以使用完整功能")
        self.status_label.setStyleSheet("color: #ff6b6b;")
        self.status_icon.setText("❌")
        
        self.expiry_label.setText("")
        self.expiry_progress.setVisible(False)
        self.user_info_label.setText("")
        
        # 更新功能状态
        for feature_key, status_label in self.feature_labels.items():
            status_label.setText("❌ 未启用")
            status_label.setStyleSheet("color: #ff6b6b;")
        
        self.activate_button.setText("激活软件")
        self.activate_button.setEnabled(True)
        self.upgrade_button.setEnabled(False)
    
    def _update_status_display(self, status: LicenseStatus):
        """更新状态显示"""
        status_info = {
            LicenseStatus.VALID: ("✅", "正常", "#4ecdc4"),
            LicenseStatus.EXPIRED: ("⏰", "已过期", "#ff6b6b"),
            LicenseStatus.TRIAL_EXPIRED: ("⏰", "试用已过期", "#ff9f43"),
            LicenseStatus.INVALID: ("❌", "无效", "#ff6b6b"),
            LicenseStatus.NOT_ACTIVATED: ("❌", "未激活", "#ff6b6b")
        }
        
        icon, text, color = status_info.get(status, ("❓", "未知", "#666666"))
        
        self.status_icon.setText(icon)
        self.status_label.setText(text)
        self.status_label.setStyleSheet(f"color: {color};")
    
    def _update_expiry_info(self, license_info):
        """更新到期信息"""
        days_left = self.license_manager.get_days_until_expiry()
        
        if days_left is None:
            self.expiry_label.setText("永久有效")
            self.expiry_progress.setVisible(False)
        else:
            if days_left > 0:
                self.expiry_label.setText(f"剩余 {days_left} 天")
                
                # 显示进度条
                if license_info.license_type == LicenseType.TRIAL:
                    total_days = 30
                else:
                    total_days = 365
                
                progress = max(0, min(100, (days_left / total_days) * 100))
                self.expiry_progress.setValue(int(progress))
                self.expiry_progress.setVisible(True)
                
                # 设置颜色
                if days_left <= 7:
                    self.expiry_label.setStyleSheet("color: #ff6b6b; font-weight: bold;")
                elif days_left <= 30:
                    self.expiry_label.setStyleSheet("color: #ff9f43; font-weight: bold;")
                else:
                    self.expiry_label.setStyleSheet("color: #4ecdc4;")
            else:
                self.expiry_label.setText("已过期")
                self.expiry_label.setStyleSheet("color: #ff6b6b; font-weight: bold;")
                self.expiry_progress.setVisible(False)
    
    def _update_user_info(self, license_info):
        """更新用户信息"""
        user_info_parts = []
        
        if license_info.user_name:
            user_info_parts.append(f"用户: {license_info.user_name}")
        
        if license_info.company_name:
            user_info_parts.append(f"公司: {license_info.company_name}")
        
        if license_info.email:
            user_info_parts.append(f"邮箱: {license_info.email}")
        
        self.user_info_label.setText(" | ".join(user_info_parts))
    
    def _update_feature_status(self, license_info):
        """更新功能状态"""
        for feature_key, status_label in self.feature_labels.items():
            if self.license_manager.is_feature_enabled(feature_key):
                status_label.setText("✅ 已启用")
                status_label.setStyleSheet("color: #4ecdc4;")
            else:
                status_label.setText("❌ 未启用")
                status_label.setStyleSheet("color: #ff6b6b;")
    
    def _update_button_status(self, license_info, status: LicenseStatus):
        """更新按钮状态"""
        if status in [LicenseStatus.EXPIRED, LicenseStatus.TRIAL_EXPIRED, LicenseStatus.INVALID]:
            self.activate_button.setText("重新激活")
            self.activate_button.setEnabled(True)
            self.upgrade_button.setEnabled(False)
        elif license_info.license_type in [LicenseType.FREE, LicenseType.TRIAL]:
            self.activate_button.setText("激活正式版")
            self.activate_button.setEnabled(True)
            self.upgrade_button.setEnabled(True)
        else:
            self.activate_button.setText("更换激活码")
            self.activate_button.setEnabled(True)
            self.upgrade_button.setEnabled(True)
    
    def on_license_changed(self, license_info):
        """许可证变更处理"""
        self.update_status()
    
    def on_license_expired(self):
        """许可证过期处理"""
        self.update_status()
    
    def on_license_warning(self, days_left: int):
        """许可证警告处理"""
        self.update_status()
