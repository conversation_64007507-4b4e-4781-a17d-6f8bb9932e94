# 🔢 版本号管理说明

## 📋 版本号修改方法

### **方法1: 使用自动化工具 (推荐)**

我已经为您创建了一个自动化的版本号更新工具：

#### **使用步骤**
```bash
# 在项目根目录运行
python update_version.py
```

#### **工具功能**
1. **查看当前版本号** - 显示所有文件中的版本号分布
2. **统一更新版本号** - 一键更新所有相关文件
3. **自动验证格式** - 确保版本号格式正确
4. **更新发布日期** - 自动更新关于对话框的发布日期

#### **操作演示**
```
🚀 资管工具平台版本号更新工具
==================================================

📋 选择操作:
1. 查看当前版本号
2. 更新版本号  
3. 退出

请选择 (1-3): 2

当前版本: 1.0.0
请输入新版本号 (如 1.0.1): 1.0.1

确认更新版本号 1.0.0 → 1.0.1? (y/N): y

🔄 更新版本号: 1.0.0 → 1.0.1
==================================================
✅ 主程序版本: 资管工具平台/src/main.py
✅ 主窗口标题: 资管工具平台/src/ui/main_window.py
✅ 状态栏版本: 资管工具平台/src/ui/main_window.py
✅ 关于对话框版本标签: 资管工具平台/src/ui/about_dialog.py
✅ 关于对话框版本信息: 资管工具平台/src/ui/about_dialog.py

🎉 版本号更新完成!
📦 新版本: 1.0.1
📁 更新文件数: 5
```

### **方法2: 手动修改**

如果您需要手动修改，以下是所有版本号定义的位置：

#### **1. 主程序版本 (main.py)**
```python
# 文件: 资管工具平台/src/main.py
app.setApplicationVersion("1.0.1")  # 修改这里
```

#### **2. 主窗口标题 (main_window.py)**
```python
# 文件: 资管工具平台/src/ui/main_window.py
self.setWindowTitle("资管工具平台 v1.0.1")  # 修改这里
```

#### **3. 状态栏版本 (main_window.py)**
```python
# 文件: 资管工具平台/src/ui/main_window.py
version_label = QLabel("v1.0.1")  # 修改这里
```

#### **4. 关于对话框版本 (about_dialog.py)**
```python
# 文件: 资管工具平台/src/ui/about_dialog.py
version_label = QLabel("版本 1.0.1")  # 修改这里

# HTML内容中的版本信息
<b>版本：</b>1.0.1<br>  # 修改这里
<b>发布日期：</b>2025年8月5日  # 修改这里
```

#### **5. 配置文件版本 (default_config.json)**
```json
{
  "app": {
    "version": "1.0.1"
  }
}
```

## 📊 版本号格式规范

### **版本号格式**
使用语义化版本控制 (Semantic Versioning)：
```
主版本号.次版本号.修订号[-预发布标识]

例如：
1.0.0     - 正式版本
1.0.1     - 修复版本
1.1.0     - 功能更新
2.0.0     - 重大更新
1.0.1-beta - 测试版本
```

### **版本号含义**
- **主版本号** - 重大功能变更或不兼容的API修改
- **次版本号** - 新增功能，向下兼容
- **修订号** - 问题修复，向下兼容
- **预发布标识** - alpha, beta, rc等

## 🔄 版本更新流程

### **开发版本更新**
1. **修复版本** (1.0.0 → 1.0.1)
   - 修复bug
   - 小幅优化
   - 不影响功能

2. **功能版本** (1.0.1 → 1.1.0)
   - 新增功能
   - 功能改进
   - 保持兼容

3. **重大版本** (1.1.0 → 2.0.0)
   - 架构重构
   - 重大功能变更
   - 可能不兼容

### **发布流程**
1. **更新版本号** - 使用自动化工具
2. **更新变更日志** - 记录本次更新内容
3. **测试验证** - 确保所有功能正常
4. **构建发布** - 生成安装包
5. **更新服务端** - 同步服务端版本信息

## 🛠️ 相关配置

### **更新服务器配置**
如果您使用了更新服务器，还需要更新：

#### **服务端配置 (config.yaml)**
```yaml
update:
  current_version: "1.0.1"  # 修改这里
  minimum_version: "0.9.0"
  force_update_below: "0.8.0"
```

#### **版本信息文件 (version_info.json)**
```json
{
  "stable": {
    "version": "1.0.1",  // 修改这里
    "build_number": 1001,
    "release_date": "2025-08-05T14:30:00",
    "changelog": "• 修复了激活码验证问题\n• 优化了操作保护机制"
  }
}
```

### **客户端更新检查**
客户端会自动从以下位置获取版本信息：
- 配置文件中的 `app.version`
- 更新管理器的默认版本
- 主程序的应用程序版本

## ✅ 验证更新结果

### **检查项目**
更新版本号后，请验证：

1. **窗口标题** - 显示新版本号
2. **状态栏** - 显示新版本号
3. **关于对话框** - 显示新版本和日期
4. **更新检查** - 正确识别当前版本
5. **日志记录** - 版本信息正确

### **测试建议**
1. **重启应用** - 确保新版本号生效
2. **检查界面** - 所有版本显示一致
3. **功能测试** - 确保功能正常
4. **更新测试** - 测试版本检查功能

## 🎯 最佳实践

### **版本管理建议**
1. **使用自动化工具** - 避免手动错误
2. **保持一致性** - 所有位置版本号一致
3. **及时更新** - 每次发布前更新版本
4. **记录变更** - 维护详细的变更日志

### **发布前检查清单**
- [ ] 版本号格式正确
- [ ] 所有文件版本一致
- [ ] 发布日期已更新
- [ ] 变更日志已完善
- [ ] 功能测试通过
- [ ] 服务端配置同步

## 📝 示例：完整更新流程

```bash
# 1. 使用自动化工具更新版本号
python update_version.py

# 2. 选择更新版本号 (选项2)
# 3. 输入新版本号 (如 1.0.2)
# 4. 确认更新 (y)

# 5. 重启应用程序验证
python main.py

# 6. 检查版本显示是否正确
# 7. 测试主要功能
# 8. 更新服务端配置 (如需要)
```

现在您可以轻松管理版本号了！建议使用自动化工具，这样可以确保所有位置的版本号都保持一致。🎉
