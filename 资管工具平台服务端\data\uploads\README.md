# 更新包存储目录

这个目录用于存储软件更新包文件。

## 📁 目录结构

```
data/uploads/
├── README.md                    # 本说明文件
├── updates/                     # 更新包目录
│   ├── v1.0.0/                 # 版本1.0.0
│   │   ├── windows_x64.zip     # Windows 64位更新包
│   │   ├── windows_x86.zip     # Windows 32位更新包
│   │   ├── linux_x64.zip       # Linux 64位更新包
│   │   ├── macos_x64.zip       # macOS Intel更新包
│   │   └── macos_arm64.zip     # macOS Apple Silicon更新包
│   ├── v1.0.1/                 # 版本1.0.1
│   │   └── ...
│   └── v1.1.0/                 # 版本1.1.0
│       └── ...
├── patches/                     # 增量更新包
│   ├── 1.0.0_to_1.0.1/         # 从1.0.0到1.0.1的增量包
│   └── 1.0.1_to_1.1.0/         # 从1.0.1到1.1.0的增量包
└── temp/                        # 临时文件
    └── uploads/                 # 上传临时目录
```

## 📦 更新包命名规范

### 完整更新包
- 格式: `{platform}_{architecture}.zip`
- 示例: 
  - `windows_x64.zip` - Windows 64位完整包
  - `linux_x64.zip` - Linux 64位完整包
  - `macos_arm64.zip` - macOS Apple Silicon完整包

### 增量更新包
- 格式: `{from_version}_to_{to_version}_{platform}_{architecture}.zip`
- 示例:
  - `1.0.0_to_1.0.1_windows_x64.zip` - Windows 64位增量包
  - `1.0.1_to_1.1.0_linux_x64.zip` - Linux 64位增量包

## 📋 更新包内容结构

### 完整更新包内容
```
更新包.zip
├── 资管工具平台.exe              # 主程序文件
├── src/                         # 源代码目录
├── config/                      # 配置文件
├── data/                        # 数据文件
├── update_info.json             # 更新信息文件
└── install.bat                  # 安装脚本（Windows）
```

### 增量更新包内容
```
增量包.zip
├── files/                       # 需要更新的文件
│   ├── 资管工具平台.exe         # 更新的主程序
│   ├── src/modules/             # 更新的模块
│   └── config/                  # 更新的配置
├── patch_info.json              # 补丁信息文件
└── apply_patch.bat              # 补丁应用脚本
```

## 🔧 上传更新包

### 方式1: 通过API上传
```bash
curl -X POST http://localhost:5000/api/update/upload \
  -H "X-API-Key: your-api-key" \
  -F "file=@update_package.zip" \
  -F "version=1.0.1" \
  -F "platform=windows" \
  -F "architecture=x64" \
  -F "description=修复重要问题" \
  -F "changelog=- 修复上传历史记录显示问题\n- 优化性能"
```

### 方式2: 直接复制文件
1. 创建版本目录: `data/uploads/updates/v1.0.1/`
2. 复制更新包文件到对应目录
3. 通过管理后台或API注册版本信息

## 📊 文件大小建议

- **完整更新包**: 建议不超过 100MB
- **增量更新包**: 建议不超过 50MB
- **单个文件**: 建议不超过 200MB

## 🛡️ 安全注意事项

1. **文件验证**: 所有更新包都会计算SHA256哈希值进行完整性验证
2. **访问控制**: 只有通过API密钥认证的请求才能上传文件
3. **文件扫描**: 建议对上传的文件进行病毒扫描
4. **备份策略**: 定期备份重要的更新包文件

## 🔄 自动清理

系统会自动清理：
- 超过30天的临时文件
- 非活跃版本的更新包（可配置）
- 下载失败的临时文件

## 📞 技术支持

如有问题，请联系技术支持团队。
