#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
激活码局域网服务器配置脚本
"""

import os
import sys
import json
import socket
import subprocess
from pathlib import Path


def get_local_ip():
    """获取本机局域网IP地址"""
    try:
        # 连接到一个远程地址来获取本机IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except Exception:
        return "127.0.0.1"


def update_client_config(server_ip, port=5000):
    """更新客户端配置文件"""
    config_files = [
        Path("config") / "user_config.json",
        Path("config") / "default_config.json"
    ]
    
    updated_files = []
    
    for config_file in config_files:
        if not config_file.exists():
            continue
            
        try:
            # 读取配置文件
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 更新license_server配置
            if 'license_server' not in config:
                config['license_server'] = {}
            
            config['license_server']['enabled'] = True
            config['license_server']['server_url'] = f"http://{server_ip}:{port}"
            config['license_server']['api_key'] = "client-api-key-123"
            config['license_server']['timeout'] = 30
            config['license_server']['offline_grace_period'] = 86400
            
            # 写回配置文件
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4, ensure_ascii=False)
            
            updated_files.append(str(config_file))
            
        except Exception as e:
            print(f"❌ 更新配置文件失败 {config_file}: {e}")
    
    return updated_files


def update_server_config(server_ip, port=5000):
    """更新服务端配置文件"""
    server_config_file = Path("..") / "资管工具平台服务端" / "config" / "config.yaml"
    
    if not server_config_file.exists():
        print(f"❌ 服务端配置文件不存在: {server_config_file}")
        return False
    
    try:
        # 读取配置文件
        with open(server_config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 更新host配置为0.0.0.0以支持局域网访问
        import re
        content = re.sub(r'host:\s*"[^"]*"', f'host: "0.0.0.0"', content)
        content = re.sub(r'port:\s*\d+', f'port: {port}', content)
        
        # 写回配置文件
        with open(server_config_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 已更新服务端配置: {server_config_file}")
        return True
        
    except Exception as e:
        print(f"❌ 更新服务端配置失败: {e}")
        return False


def update_online_validator(server_ip, port=5000):
    """更新在线验证器的默认服务器地址"""
    validator_file = Path("src") / "license" / "online_validator.py"
    
    if not validator_file.exists():
        print(f"❌ 在线验证器文件不存在: {validator_file}")
        return False
    
    try:
        # 读取文件内容
        with open(validator_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换默认服务器URL
        import re
        pattern = r'def __init__\(self, server_url: str = "[^"]*"\)'
        replacement = f'def __init__(self, server_url: str = "http://{server_ip}:{port}/api")'
        content = re.sub(pattern, replacement, content)
        
        # 写回文件
        with open(validator_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 已更新在线验证器默认地址: http://{server_ip}:{port}/api")
        return True
        
    except Exception as e:
        print(f"❌ 更新在线验证器失败: {e}")
        return False


def main():
    """主函数"""
    print("🌐 激活码系统局域网配置")
    print("=" * 40)

    # 获取本机IP
    local_ip = get_local_ip()
    print(f"🔍 检测到本机IP: {local_ip}")

    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == "--auto":
            # 自动模式：使用局域网IP和默认端口
            server_ip = local_ip
            port = 5000
            print(f"🤖 自动配置模式: {server_ip}:{port}")
        elif sys.argv[1] == "--local":
            # 本地模式：使用127.0.0.1
            server_ip = "127.0.0.1"
            port = 5000
            print(f"🏠 本地配置模式: {server_ip}:{port}")
        else:
            # 自定义IP
            server_ip = sys.argv[1]
            port = int(sys.argv[2]) if len(sys.argv) > 2 and sys.argv[2].isdigit() else 5000
            print(f"🎯 自定义配置: {server_ip}:{port}")
    else:
        # 交互模式
        print(f"\n请选择服务器配置:")
        print(f"1. 局域网访问 - 使用本机IP: {local_ip}")
        print(f"2. 仅本机访问 - 使用回环地址: 127.0.0.1")
        print(f"3. 自定义IP地址")

        try:
            choice = input("\n请选择 (1-3): ").strip()

            if choice == "2":
                server_ip = "127.0.0.1"
            elif choice == "3":
                server_ip = input("请输入服务器IP地址: ").strip()
                if not server_ip:
                    server_ip = local_ip
            else:
                server_ip = local_ip

            # 端口配置
            port_input = input(f"\n请输入服务器端口 (默认5000): ").strip()
            port = int(port_input) if port_input.isdigit() else 5000
        except (EOFError, KeyboardInterrupt):
            # 如果无法交互，使用默认配置
            print("\n🤖 使用默认配置（局域网模式）")
            server_ip = local_ip
            port = 5000
    
    print(f"\n🚀 配置激活码服务器")
    print(f"服务器地址: http://{server_ip}:{port}")
    print(f"管理界面: http://{server_ip}:{port}/admin")
    print("-" * 40)
    
    success_count = 0
    total_tasks = 3
    
    # 1. 更新客户端配置
    print("1. 更新客户端配置...")
    updated_files = update_client_config(server_ip, port)
    if updated_files:
        print(f"   ✅ 已更新 {len(updated_files)} 个配置文件")
        for file in updated_files:
            print(f"      - {file}")
        success_count += 1
    else:
        print("   ❌ 客户端配置更新失败")
    
    # 2. 更新服务端配置
    print("2. 更新服务端配置...")
    if update_server_config(server_ip, port):
        success_count += 1
    
    # 3. 更新在线验证器
    print("3. 更新在线验证器...")
    if update_online_validator(server_ip, port):
        success_count += 1
    
    print(f"\n📊 配置完成情况: {success_count}/{total_tasks}")
    
    if success_count == total_tasks:
        print("✅ 所有配置已完成!")
        print(f"\n🎯 下一步操作:")
        print(f"1. 启动激活码服务器:")
        print(f"   cd ../资管工具平台服务端")
        print(f"   python src/app.py")
        print(f"2. 访问管理界面: http://{server_ip}:{port}/admin")
        print(f"3. 默认管理员账号: admin / admin123")
        
        if server_ip != "127.0.0.1":
            print(f"\n💡 局域网访问提示:")
            print(f"- 确保服务器和客户端在同一局域网")
            print(f"- 如果无法访问，请检查防火墙设置")
            print(f"- 客户端配置已自动更新为: http://{server_ip}:{port}")
    else:
        print("⚠️  部分配置可能未成功，请检查错误信息")


if __name__ == "__main__":
    main()
