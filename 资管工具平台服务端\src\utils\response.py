#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
响应工具模块
"""

from flask import jsonify
from datetime import datetime
from typing import Any, Dict, Optional


def success_response(data: Any = None, message: str = "Success", code: int = 200) -> tuple:
    """成功响应"""
    response = {
        'success': True,
        'code': code,
        'message': message,
        'timestamp': datetime.utcnow().isoformat(),
        'data': data
    }
    return jsonify(response), code


def error_response(message: str = "Error", code: int = 400, details: Optional[Dict] = None) -> tuple:
    """错误响应"""
    response = {
        'success': False,
        'code': code,
        'message': message,
        'timestamp': datetime.utcnow().isoformat(),
        'error': details
    }
    return jsonify(response), code


def paginated_response(items: list, page: int, per_page: int, total: int, **kwargs) -> tuple:
    """分页响应"""
    total_pages = (total + per_page - 1) // per_page
    
    data = {
        'items': items,
        'pagination': {
            'page': page,
            'per_page': per_page,
            'total': total,
            'pages': total_pages,
            'has_next': page < total_pages,
            'has_prev': page > 1
        }
    }
    
    # 添加额外数据
    data.update(kwargs)
    
    return success_response(data)


def validation_error_response(errors: Dict) -> tuple:
    """验证错误响应"""
    return error_response(
        message="Validation failed",
        code=422,
        details={'validation_errors': errors}
    )
