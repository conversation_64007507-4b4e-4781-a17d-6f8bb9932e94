#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一发布包生成工具
包含版本号管理、构建打包、发布说明生成
"""

import os
import sys
import json
import shutil
import zipfile
import subprocess
from pathlib import Path
from datetime import datetime
import re

class ReleaseBuilder:
    """发布包构建器"""
    
    def __init__(self, project_root=None):
        if project_root is None:
            project_root = Path(__file__).parent
        self.project_root = Path(project_root)
        
        # 项目路径
        self.client_dir = self.project_root / "资管工具平台"
        self.server_dir = self.project_root / "资管工具平台服务端"
        self.release_dir = self.project_root / "发布包"
        
        # 构建输出路径
        self.client_build_dir = self.client_dir / "dist"
        self.server_build_dir = self.server_dir / "dist"
        
        # 当前版本
        self.current_version = self.get_current_version()
        
    def get_current_version(self):
        """获取当前版本号"""
        main_py = self.client_dir / "src" / "main.py"
        if main_py.exists():
            content = main_py.read_text(encoding='utf-8')
            match = re.search(r'app\.setApplicationVersion\("([^"]+)"\)', content)
            if match:
                return match.group(1)
        return "1.0.0"
    
    def update_version(self, new_version):
        """更新版本号"""
        print(f"🔄 更新版本号: {self.current_version} → {new_version}")
        
        # 使用现有的版本更新工具
        update_script = self.project_root / "update_version.py"
        if update_script.exists():
            try:
                # 自动化版本更新
                result = subprocess.run([
                    sys.executable, str(update_script)
                ], input=f"2\n{new_version}\ny\n3\n", 
                text=True, capture_output=True, cwd=self.project_root)
                
                if result.returncode == 0:
                    print(f"✅ 版本号更新成功: {new_version}")
                    self.current_version = new_version
                    return True
                else:
                    print(f"❌ 版本号更新失败: {result.stderr}")
                    return False
            except Exception as e:
                print(f"❌ 版本号更新异常: {e}")
                return False
        else:
            print("❌ 找不到版本更新工具")
            return False
    
    def get_changelog(self):
        """获取版本更新说明"""
        print("\n📝 请输入本次更新的说明:")
        print("(每行一个更新点，空行结束输入)")
        
        changelog_items = []
        while True:
            line = input("• ").strip()
            if not line:
                break
            changelog_items.append(f"• {line}")
        
        if not changelog_items:
            changelog_items = [
                f"• 版本 {self.current_version} 更新",
                "• 修复已知问题",
                "• 性能优化"
            ]
        
        return "\n".join(changelog_items)
    
    def clean_build_dirs(self):
        """清理构建目录"""
        print("\n🧹 清理构建目录...")
        
        dirs_to_clean = [
            self.client_build_dir,
            self.server_build_dir,
            self.client_dir / "build"
        ]
        
        for dir_path in dirs_to_clean:
            if dir_path.exists():
                try:
                    shutil.rmtree(dir_path)
                    print(f"✅ 清理: {dir_path.relative_to(self.project_root)}")
                except Exception as e:
                    print(f"⚠️ 清理失败: {dir_path.relative_to(self.project_root)} - {e}")
    
    def build_client(self):
        """构建客户端"""
        print("\n🔨 构建客户端...")
        
        build_script = self.client_dir / "build.bat"
        if not build_script.exists():
            print("❌ 找不到客户端构建脚本")
            return False
        
        try:
            # 运行构建脚本
            result = subprocess.run([str(build_script)], 
                                  cwd=self.client_dir, 
                                  capture_output=True, 
                                  text=True,
                                  shell=True)
            
            if result.returncode == 0:
                print("✅ 客户端构建成功")
                return True
            else:
                print(f"❌ 客户端构建失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 客户端构建异常: {e}")
            return False
    
    def build_server(self):
        """构建服务端"""
        print("\n🔨 构建服务端...")
        
        # 服务端通常不需要编译，直接打包源码
        print("✅ 服务端准备完成 (Python源码)")
        return True
    
    def create_client_package(self):
        """创建客户端发布包"""
        print("\n📦 创建客户端发布包...")
        
        client_release_dir = self.release_dir / "客户端"
        client_release_dir.mkdir(parents=True, exist_ok=True)
        
        # 查找构建输出
        if self.client_build_dir.exists():
            # 打包构建输出
            package_name = f"ResourceManagementTool_v{self.current_version}_Windows_x64.zip"
            package_path = client_release_dir / package_name
            
            try:
                with zipfile.ZipFile(package_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    for root, dirs, files in os.walk(self.client_build_dir):
                        for file in files:
                            file_path = Path(root) / file
                            arc_path = file_path.relative_to(self.client_build_dir)
                            zipf.write(file_path, arc_path)
                
                print(f"✅ 客户端包: {package_name}")
                return package_path
                
            except Exception as e:
                print(f"❌ 客户端打包失败: {e}")
                return None
        else:
            print("❌ 找不到客户端构建输出")
            return None
    
    def create_server_package(self):
        """创建服务端发布包"""
        print("\n📦 创建服务端发布包...")
        
        server_release_dir = self.release_dir / "服务端"
        server_release_dir.mkdir(parents=True, exist_ok=True)
        
        package_name = f"ResourceManagementServer_v{self.current_version}.zip"
        package_path = server_release_dir / package_name
        
        try:
            with zipfile.ZipFile(package_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # 打包服务端源码和配置
                for root, dirs, files in os.walk(self.server_dir):
                    # 跳过不需要的目录
                    dirs[:] = [d for d in dirs if d not in ['__pycache__', '.git', 'dist', 'build']]
                    
                    for file in files:
                        if not file.endswith(('.pyc', '.pyo', '.log')):
                            file_path = Path(root) / file
                            arc_path = file_path.relative_to(self.server_dir)
                            zipf.write(file_path, f"ResourceManagementServer/{arc_path}")
            
            print(f"✅ 服务端包: {package_name}")
            return package_path
            
        except Exception as e:
            print(f"❌ 服务端打包失败: {e}")
            return None
    
    def create_update_package(self):
        """创建更新包"""
        print("\n📦 创建更新包...")
        
        update_release_dir = self.release_dir / "更新包"
        update_release_dir.mkdir(parents=True, exist_ok=True)
        
        package_name = "windows_x64.zip"
        package_path = update_release_dir / package_name
        
        # 更新包通常是客户端的精简版
        if self.client_build_dir.exists():
            try:
                with zipfile.ZipFile(package_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    for root, dirs, files in os.walk(self.client_build_dir):
                        for file in files:
                            file_path = Path(root) / file
                            arc_path = file_path.relative_to(self.client_build_dir)
                            zipf.write(file_path, arc_path)
                
                print(f"✅ 更新包: {package_name}")
                return package_path
                
            except Exception as e:
                print(f"❌ 更新包打包失败: {e}")
                return None
        else:
            print("❌ 找不到客户端构建输出")
            return None
    
    def generate_release_notes(self, changelog, packages):
        """生成发布说明"""
        print("\n📋 生成发布说明...")
        
        # 计算包大小
        def get_file_size(file_path):
            if file_path and file_path.exists():
                size = file_path.stat().st_size
                return f"{size / 1024 / 1024:.2f} MB"
            return "未知"
        
        release_notes = f"""# 🚀 资管工具平台 v{self.current_version} 发布说明

## 📦 发布信息

- **版本号**: {self.current_version}
- **发布日期**: {datetime.now().strftime('%Y年%m月%d日')}
- **构建时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 📋 更新内容

{changelog}

## 📁 发布包内容

### 🖥️ 客户端
- **文件名**: ResourceManagementTool_v{self.current_version}_Windows_x64.zip
- **大小**: {get_file_size(packages.get('client'))}
- **说明**: Windows x64 完整客户端，包含所有功能模块

### 🖥️ 服务端  
- **文件名**: ResourceManagementServer_v{self.current_version}.zip
- **大小**: {get_file_size(packages.get('server'))}
- **说明**: 完整服务端源码，支持授权验证和自动更新

### 🔄 更新包
- **文件名**: windows_x64.zip  
- **大小**: {get_file_size(packages.get('update'))}
- **说明**: 客户端自动更新包

## 🛠️ 安装说明

### 客户端安装
1. 下载 `ResourceManagementTool_v{self.current_version}_Windows_x64.zip`
2. 解压到任意目录
3. 运行 `启动资管工具平台.bat` 或 `ResourceManagementTool.exe`

### 服务端部署
1. 下载 `ResourceManagementServer_v{self.current_version}.zip`
2. 解压到服务器目录
3. 参考 `README.md` 进行配置和部署

## ⚙️ 系统要求

- **操作系统**: Windows 10/11 (x64)
- **内存**: 最低 4GB RAM
- **磁盘空间**: 最低 500MB 可用空间
- **网络**: 需要网络连接进行授权验证

## 🔗 相关链接

- 用户手册: 参见客户端 `docs/用户手册.md`
- 部署指南: 参见服务端 `README.md`
- 技术支持: 联系资源管理部

## 📝 注意事项

1. 首次使用需要有效的激活码
2. 建议在安装前关闭杀毒软件
3. 如遇问题请查看日志文件或联系技术支持

---
*本版本由资源管理部开发维护*
"""
        
        # 保存发布说明
        release_notes_file = self.release_dir / f"发布说明_v{self.current_version}.md"
        release_notes_file.write_text(release_notes, encoding='utf-8')
        
        # 更新主README
        readme_file = self.release_dir / "README.txt"
        readme_content = f"""资管工具平台 v{self.current_version} 发布包

发布日期: {datetime.now().strftime('%Y年%m月%d日')}

包含内容:
- 客户端/: Windows客户端程序
- 服务端/: 服务端程序和配置
- 更新包/: 自动更新文件

详细说明请查看: 发布说明_v{self.current_version}.md
"""
        readme_file.write_text(readme_content, encoding='utf-8')
        
        print(f"✅ 发布说明: 发布说明_v{self.current_version}.md")
        return release_notes_file

def main():
    """主函数"""
    print("🚀 资管工具平台统一发布构建工具")
    print("=" * 60)
    
    builder = ReleaseBuilder()
    
    print(f"📋 当前版本: {builder.current_version}")
    print(f"📁 项目根目录: {builder.project_root}")
    
    # 询问是否更新版本号
    update_version = input(f"\n是否更新版本号? 当前: {builder.current_version} (y/N): ").strip().lower()
    
    if update_version in ['y', 'yes']:
        new_version = input("请输入新版本号 (如 1.0.2): ").strip()
        if new_version and new_version != builder.current_version:
            if not builder.update_version(new_version):
                print("❌ 版本号更新失败，终止构建")
                return
        else:
            print("⚠️ 版本号无变化")
    
    # 获取更新说明
    changelog = builder.get_changelog()
    
    # 确认构建
    print(f"\n📊 构建信息:")
    print(f"- 版本号: {builder.current_version}")
    print(f"- 更新说明: {len(changelog.split(chr(10)))} 条")
    print(f"- 输出目录: {builder.release_dir}")
    
    confirm = input("\n确认开始构建? (y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("❌ 取消构建")
        return
    
    # 开始构建流程
    print(f"\n🔨 开始构建 v{builder.current_version}...")
    
    # 1. 清理构建目录
    builder.clean_build_dirs()
    
    # 2. 构建客户端
    if not builder.build_client():
        print("❌ 客户端构建失败，终止流程")
        return
    
    # 3. 构建服务端
    if not builder.build_server():
        print("❌ 服务端构建失败，终止流程")
        return
    
    # 4. 创建发布包
    packages = {}
    packages['client'] = builder.create_client_package()
    packages['server'] = builder.create_server_package()
    packages['update'] = builder.create_update_package()
    
    # 5. 生成发布说明
    release_notes = builder.generate_release_notes(changelog, packages)
    
    # 6. 构建完成
    success_count = sum(1 for p in packages.values() if p is not None)
    
    print(f"\n🎉 构建完成!")
    print(f"✅ 成功创建 {success_count}/3 个发布包")
    print(f"📋 发布说明: {release_notes.name}")
    print(f"📁 输出目录: {builder.release_dir}")
    
    if success_count == 3:
        print(f"\n🚀 v{builder.current_version} 发布包已准备就绪!")
    else:
        print(f"\n⚠️ 部分发布包创建失败，请检查错误信息")

if __name__ == '__main__':
    main()
