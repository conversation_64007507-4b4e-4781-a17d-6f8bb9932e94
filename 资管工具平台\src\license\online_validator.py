#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
在线许可证验证客户端
"""

import json
import requests
import hashlib
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Tuple
from PyQt6.QtCore import QObject, pyqtSignal, QThread

from .crypto_utils import generate_hardware_id


class OnlineValidationWorker(QThread):
    """在线验证工作线程"""
    
    validation_finished = pyqtSignal(bool, dict)  # 验证完成信号
    
    def __init__(self, server_url: str, activation_code: str, hardware_id: str):
        super().__init__()
        self.server_url = server_url
        self.activation_code = activation_code
        self.hardware_id = hardware_id
    
    def run(self):
        """执行在线验证"""
        try:
            validator = OnlineLicenseValidator(self.server_url)
            result = validator.validate_online(self.activation_code, self.hardware_id)
            self.validation_finished.emit(result['success'], result)
        except Exception as e:
            self.validation_finished.emit(False, {
                'success': False,
                'error': 'NETWORK_ERROR',
                'message': f'网络错误: {str(e)}'
            })


class OnlineLicenseValidator:
    """在线许可证验证器"""
    
    def __init__(self, server_url: str = "http://localhost:5000/api"):
        self.server_url = server_url.rstrip('/')
        self.session = requests.Session()
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'ResourceManagementTool-LicenseClient/1.0',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
        
        # 设置超时
        self.session.timeout = 30
    
    def validate_online(self, activation_code: str, hardware_id: str = None) -> Dict[str, Any]:
        """在线验证许可证"""
        if hardware_id is None:
            hardware_id = generate_hardware_id()
        
        try:
            # 构建请求数据
            request_data = {
                'activation_code': activation_code,
                'hardware_id': hardware_id,
                'client_info': self._get_client_info(),
                'timestamp': datetime.now().isoformat(),
                'signature': self._generate_request_signature(activation_code, hardware_id)
            }
            
            # 发送验证请求
            response = self.session.post(
                f"{self.server_url}/validate",
                json=request_data
            )
            
            # 检查HTTP状态
            if response.status_code != 200:
                return {
                    'success': False,
                    'error': 'HTTP_ERROR',
                    'message': f'HTTP错误: {response.status_code}'
                }
            
            # 解析响应
            result = response.json()
            
            # 验证响应签名
            if not self._verify_response_signature(result):
                return {
                    'success': False,
                    'error': 'SIGNATURE_ERROR',
                    'message': '响应签名验证失败'
                }
            
            return result
            
        except requests.RequestException as e:
            return {
                'success': False,
                'error': 'NETWORK_ERROR',
                'message': f'网络请求失败: {str(e)}'
            }
        except json.JSONDecodeError:
            return {
                'success': False,
                'error': 'PARSE_ERROR',
                'message': '响应数据解析失败'
            }
        except Exception as e:
            return {
                'success': False,
                'error': 'UNKNOWN_ERROR',
                'message': f'未知错误: {str(e)}'
            }
    
    def register_license_online(self, license_data: Dict[str, Any]) -> Dict[str, Any]:
        """在线注册许可证"""
        try:
            # 添加客户端信息
            license_data['client_info'] = self._get_client_info()
            license_data['timestamp'] = datetime.now().isoformat()
            
            # 发送注册请求
            response = self.session.post(
                f"{self.server_url}/register",
                json=license_data
            )
            
            if response.status_code != 200:
                return {
                    'success': False,
                    'error': 'HTTP_ERROR',
                    'message': f'HTTP错误: {response.status_code}'
                }
            
            return response.json()
            
        except Exception as e:
            return {
                'success': False,
                'error': 'NETWORK_ERROR',
                'message': f'注册失败: {str(e)}'
            }
    
    def get_license_info_online(self, activation_code: str) -> Dict[str, Any]:
        """在线获取许可证信息"""
        try:
            request_data = {
                'activation_code': activation_code,
                'timestamp': datetime.now().isoformat()
            }
            
            response = self.session.post(
                f"{self.server_url}/info",
                json=request_data
            )
            
            if response.status_code != 200:
                return {
                    'success': False,
                    'error': 'HTTP_ERROR',
                    'message': f'HTTP错误: {response.status_code}'
                }
            
            return response.json()
            
        except Exception as e:
            return {
                'success': False,
                'error': 'NETWORK_ERROR',
                'message': f'查询失败: {str(e)}'
            }
    
    def check_server_status(self) -> Dict[str, Any]:
        """检查服务器状态"""
        try:
            response = self.session.get(f"{self.server_url}/status")
            
            if response.status_code == 200:
                return {
                    'online': True,
                    'data': response.json()
                }
            else:
                return {
                    'online': False,
                    'error': f'HTTP {response.status_code}'
                }
                
        except Exception as e:
            return {
                'online': False,
                'error': str(e)
            }
    
    def _get_client_info(self) -> Dict[str, str]:
        """获取客户端信息"""
        import platform
        
        return {
            'platform': platform.platform(),
            'system': platform.system(),
            'release': platform.release(),
            'machine': platform.machine(),
            'processor': platform.processor(),
            'python_version': platform.python_version(),
            'app_version': '1.0.0'  # 应用程序版本
        }
    
    def _generate_request_signature(self, activation_code: str, hardware_id: str) -> str:
        """生成请求签名"""
        # 简单的签名算法（实际应用中应使用更安全的方法）
        data = f"{activation_code}{hardware_id}{datetime.now().date().isoformat()}"
        return hashlib.sha256(data.encode()).hexdigest()[:16]
    
    def _verify_response_signature(self, response_data: Dict[str, Any]) -> bool:
        """验证响应签名"""
        # 简单验证（实际应用中应实现真正的签名验证）
        return True  # 暂时总是返回True
    
    def test_connection(self) -> bool:
        """测试连接"""
        try:
            response = self.session.get(f"{self.server_url}/ping", timeout=10)
            return response.status_code == 200
        except:
            return False


class HybridLicenseValidator:
    """混合许可证验证器（离线+在线）"""
    
    def __init__(self, license_manager, online_validator: OnlineLicenseValidator = None):
        self.license_manager = license_manager
        self.online_validator = online_validator
        self.last_online_check = None
        self.online_check_interval = timedelta(days=7)  # 7天检查一次
    
    def validate_hybrid(self, activation_code: str, hardware_id: str = None) -> Dict[str, Any]:
        """混合验证（优先离线，定期在线验证）"""
        # 首先进行离线验证
        offline_result = self._validate_offline(activation_code, hardware_id)
        
        # 检查是否需要在线验证
        need_online_check = self._should_check_online()
        
        if need_online_check and self.online_validator:
            try:
                # 尝试在线验证
                online_result = self.online_validator.validate_online(activation_code, hardware_id)
                
                if online_result['success']:
                    # 在线验证成功，更新本地记录
                    self._update_local_from_online(online_result)
                    self.last_online_check = datetime.now()
                    
                    return {
                        'success': True,
                        'source': 'online',
                        'data': online_result['data']
                    }
                else:
                    # 在线验证失败，但离线验证成功，使用离线结果
                    if offline_result['success']:
                        return {
                            'success': True,
                            'source': 'offline',
                            'warning': '在线验证失败，使用离线验证结果',
                            'data': offline_result['data']
                        }
                    else:
                        return online_result
                        
            except Exception as e:
                # 在线验证出错，使用离线结果
                if offline_result['success']:
                    return {
                        'success': True,
                        'source': 'offline',
                        'warning': f'在线验证出错: {str(e)}',
                        'data': offline_result['data']
                    }
        
        # 返回离线验证结果
        return {
            'success': offline_result['success'],
            'source': 'offline',
            'data': offline_result.get('data'),
            'error': offline_result.get('error'),
            'message': offline_result.get('message')
        }
    
    def _validate_offline(self, activation_code: str, hardware_id: str = None) -> Dict[str, Any]:
        """离线验证"""
        try:
            from .activation_code import ActivationCodeValidator
            
            validator = ActivationCodeValidator()
            result = validator.validate_and_extract_license(activation_code, hardware_id)
            
            if result:
                return {
                    'success': True,
                    'data': {
                        'license_type': result.license_type.value,
                        'expire_date': result.expire_date.isoformat() if result.expire_date else None,
                        'user_name': result.user_name,
                        'email': result.email
                    }
                }
            else:
                return {
                    'success': False,
                    'error': 'INVALID_CODE',
                    'message': '激活码验证失败'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': 'VALIDATION_ERROR',
                'message': f'验证过程出错: {str(e)}'
            }
    
    def _should_check_online(self) -> bool:
        """检查是否应该进行在线验证"""
        if self.last_online_check is None:
            return True
        
        return datetime.now() - self.last_online_check > self.online_check_interval
    
    def _update_local_from_online(self, online_result: Dict[str, Any]):
        """根据在线验证结果更新本地记录"""
        # 这里可以实现更新本地许可证信息的逻辑
        pass
    
    def set_online_check_interval(self, days: int):
        """设置在线检查间隔"""
        self.online_check_interval = timedelta(days=days)


# 模拟服务器响应（用于测试）
class MockLicenseServer:
    """模拟许可证服务器"""
    
    def __init__(self):
        self.licenses = {
            '0003-01Z0-FBQU-FBQM-9PBE-X005-9A': {
                'license_type': 'professional',
                'user_name': '专业用户',
                'email': '<EMAIL>',
                'expire_date': (datetime.now() + timedelta(days=365)).isoformat(),
                'is_active': True
            }
        }
    
    def validate(self, activation_code: str, hardware_id: str) -> Dict[str, Any]:
        """模拟验证"""
        if activation_code in self.licenses:
            license_info = self.licenses[activation_code]
            
            if license_info['is_active']:
                return {
                    'success': True,
                    'data': {
                        'license_info': license_info
                    }
                }
            else:
                return {
                    'success': False,
                    'error': 'DEACTIVATED',
                    'message': '许可证已停用'
                }
        else:
            return {
                'success': False,
                'error': 'INVALID_CODE',
                'message': '激活码无效'
            }


def demo_online_validation():
    """演示在线验证功能"""
    print("🌐 在线许可证验证演示")
    print("=" * 50)
    
    # 创建模拟服务器
    mock_server = MockLicenseServer()
    
    # 测试激活码
    test_code = '0003-01Z0-FBQU-FBQM-9PBE-X005-9A'
    test_hardware_id = 'TEST-HARDWARE-12345'
    
    print(f"📝 测试激活码: {test_code}")
    print(f"🔧 硬件ID: {test_hardware_id}")
    
    # 模拟在线验证
    print(f"\n🌐 模拟在线验证:")
    result = mock_server.validate(test_code, test_hardware_id)
    
    if result['success']:
        print(f"   ✅ 验证成功")
        license_info = result['data']['license_info']
        print(f"   许可证类型: {license_info['license_type']}")
        print(f"   用户: {license_info['user_name']}")
        print(f"   邮箱: {license_info['email']}")
        print(f"   到期时间: {license_info['expire_date'][:10]}")
    else:
        print(f"   ❌ 验证失败: {result['message']}")
    
    print(f"\n📊 在线验证的优势:")
    print(f"   🔒 实时验证，防止盗版")
    print(f"   📈 使用统计和分析")
    print(f"   🔄 远程许可证管理")
    print(f"   ⚡ 即时停用和激活")


if __name__ == "__main__":
    demo_online_validation()
