#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
认证中间件
"""

import jwt
from functools import wraps
from flask import request, jsonify, current_app
from loguru import logger


def api_key_required(f):
    """API密钥认证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        api_key = request.headers.get('X-API-Key')
        
        if not api_key:
            logger.warning(f"API请求缺少密钥: {request.remote_addr} - {request.path}")
            return jsonify({'error': 'API密钥缺失'}), 401
        
        # 验证API密钥
        valid_keys = current_app.config.get('API_KEYS', {})
        if api_key not in valid_keys.values():
            logger.warning(f"无效的API密钥: {request.remote_addr} - {api_key[:8]}...")
            return jsonify({'error': 'API密钥无效'}), 401
        
        return f(*args, **kwargs)
    
    return decorated_function


def auth_required(f):
    """JWT认证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        token = request.headers.get('Authorization')
        
        if not token:
            return jsonify({'error': '认证令牌缺失'}), 401
        
        try:
            # 移除 "Bearer " 前缀
            if token.startswith('Bearer '):
                token = token[7:]
            
            # 验证JWT令牌
            payload = jwt.decode(
                token,
                current_app.config['JWT_SECRET_KEY'],
                algorithms=[current_app.config['JWT_ALGORITHM']]
            )
            
            request.user_id = payload.get('user_id')
            request.user_role = payload.get('role', 'user')
            
        except jwt.ExpiredSignatureError:
            return jsonify({'error': '认证令牌已过期'}), 401
        except jwt.InvalidTokenError:
            return jsonify({'error': '认证令牌无效'}), 401
        
        return f(*args, **kwargs)
    
    return decorated_function


def admin_required(f):
    """管理员权限装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not hasattr(request, 'user_role') or request.user_role != 'admin':
            return jsonify({'error': '需要管理员权限'}), 403
        
        return f(*args, **kwargs)
    
    return decorated_function


def generate_token(user_id: str, role: str = 'user') -> str:
    """生成JWT令牌"""
    import datetime
    
    payload = {
        'user_id': user_id,
        'role': role,
        'exp': datetime.datetime.utcnow() + datetime.timedelta(
            seconds=current_app.config['JWT_ACCESS_TOKEN_EXPIRES']
        ),
        'iat': datetime.datetime.utcnow()
    }
    
    return jwt.encode(
        payload,
        current_app.config['JWT_SECRET_KEY'],
        algorithm=current_app.config['JWT_ALGORITHM']
    )


def verify_token(token: str) -> dict:
    """验证JWT令牌"""
    try:
        payload = jwt.decode(
            token,
            current_app.config['JWT_SECRET_KEY'],
            algorithms=[current_app.config['JWT_ALGORITHM']]
        )
        return {'valid': True, 'payload': payload}
    except jwt.ExpiredSignatureError:
        return {'valid': False, 'error': 'Token expired'}
    except jwt.InvalidTokenError:
        return {'valid': False, 'error': 'Invalid token'}
