#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单IP服务器配置工具
"""

import socket
import re
from pathlib import Path

def get_local_ip():
    """获取本机IP地址"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except Exception:
        return "127.0.0.1"

def update_client_config(server_ip):
    """更新客户端配置"""
    config_file = Path(__file__).parent.parent / "src" / "updater" / "version_checker.py"

    if not config_file.exists():
        print(f"❌ 客户端配置文件不存在")
        return False

    try:
        # 读取文件内容
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # 替换服务器URL
        pattern = r'update_server_url: str = "http://[^"]+/api/version/check"'
        replacement = f'update_server_url: str = "http://{server_ip}:8080/api/version/check"'
        content = re.sub(pattern, replacement, content)

        # 写回文件
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(content)

        print(f"✅ 已更新客户端配置: {server_ip}:8080")
        return True
    except Exception as e:
        print(f"❌ 更新配置失败: {e}")
        return False

def main():
    """主函数"""
    print("🌐 简单IP服务器配置")
    print("=" * 30)

    # 获取本机IP
    local_ip = get_local_ip()
    print(f"🔍 检测到本机IP: {local_ip}")

    # 用户选择
    print(f"\n请选择:")
    print(f"1. 使用本机IP: {local_ip}")
    print(f"2. 仅本机访问: 127.0.0.1")

    choice = input("\n请选择 (1-2): ").strip()

    if choice == "2":
        server_ip = "127.0.0.1"
    else:
        server_ip = local_ip

    print(f"\n🚀 配置服务器IP: {server_ip}")

    # 更新客户端配置
    if update_client_config(server_ip):
        print(f"✅ 配置完成")
        print(f"� 服务器地址: http://{server_ip}:8080")

        if server_ip != "127.0.0.1":
            print(f"\n💡 提示:")
            print(f"如果其他设备无法访问，请运行: setup_firewall.bat")
    else:
        print(f"❌ 配置失败")

if __name__ == "__main__":
    main()
