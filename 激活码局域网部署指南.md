# 🌐 激活码系统局域网部署指南

## 📋 概述

本指南将帮助您将激活码系统配置为支持局域网访问，让局域网内的多台设备都能使用统一的激活码服务器进行许可证验证。

## 🚀 快速开始

### 方法一：使用自动配置脚本（推荐）

1. **运行配置脚本**
   ```bash
   cd 资管工具平台\scripts
   python setup_lan_license_server.py
   ```
   或者直接双击：`setup_lan_license.bat`

2. **选择配置选项**
   - 选项1：局域网访问（使用本机IP）
   - 选项2：仅本机访问（127.0.0.1）
   - 选项3：自定义IP地址

3. **启动服务器**
   ```bash
   cd 资管工具平台服务端
   python start_license_server.py
   ```

### 方法二：手动配置

#### 1. 配置服务端

编辑 `资管工具平台服务端/config/config.yaml`：

```yaml
app:
  host: "0.0.0.0"  # 允许局域网访问
  port: 5000       # 服务端口
```

#### 2. 配置客户端

编辑 `资管工具平台/config/user_config.json`：

```json
{
  "license_server": {
    "enabled": true,
    "server_url": "http://*************:5000",  # 替换为实际服务器IP
    "api_key": "client-api-key-123",
    "timeout": 30,
    "offline_grace_period": 86400
  }
}
```

## 🔧 详细配置说明

### 服务器端配置

1. **网络配置**
   - `host: "0.0.0.0"` - 监听所有网络接口
   - `port: 5000` - 服务端口（可自定义）

2. **安全配置**
   ```yaml
   security:
     api_keys:
       client: "client-api-key-123"  # 客户端API密钥
       admin: "admin-api-key-456"    # 管理员API密钥
   ```

3. **数据库配置**
   ```yaml
   database:
     url: "sqlite:///data/database/app.db"
   ```

### 客户端配置

1. **许可证服务器配置**
   - `enabled: true` - 启用在线验证
   - `server_url` - 服务器地址
   - `api_key` - 客户端API密钥
   - `timeout` - 连接超时时间（秒）
   - `offline_grace_period` - 离线宽限期（秒）

2. **自动配置更新**
   配置脚本会自动更新以下文件：
   - `config/user_config.json`
   - `config/default_config.json`

## 🌐 网络要求

### 端口开放

确保以下端口在防火墙中开放：

- **5000** - 激活码服务器端口
- **8080** - 更新服务器端口（可选）

### 防火墙配置

**Windows防火墙规则：**
```cmd
netsh advfirewall firewall add rule name="资管工具激活码服务" dir=in action=allow protocol=TCP localport=5000
```

**或者运行自动生成的脚本：**
```bash
scripts/setup_firewall.bat
```

## 🎯 部署步骤

### 1. 准备服务器

选择一台局域网内的计算机作为激活码服务器：

- **推荐配置**：
  - CPU: 双核以上
  - 内存: 4GB以上
  - 存储: 10GB可用空间
  - 网络: 稳定的局域网连接

### 2. 安装依赖

在服务器上安装Python依赖：

```bash
cd 资管工具平台服务端
pip install -r requirements.txt
```

### 3. 初始化数据库

```bash
python scripts/init_db.py
```

### 4. 启动服务

```bash
python src/app.py
```

或使用生成的启动脚本：

```bash
python start_license_server.py
```

### 5. 验证部署

1. **访问管理界面**
   - 地址：`http://服务器IP:5000/admin`
   - 账号：`admin`
   - 密码：`admin123`

2. **测试API接口**
   ```bash
   curl -X POST http://服务器IP:5000/api/license/validate \
        -H "Content-Type: application/json" \
        -H "X-API-Key: client-api-key-123" \
        -d '{"activation_code": "测试激活码", "machine_id": "测试机器ID"}'
   ```

## 🔍 故障排除

### 常见问题

1. **无法连接到服务器**
   - 检查服务器是否启动
   - 确认IP地址和端口正确
   - 检查防火墙设置
   - 验证网络连通性

2. **激活码验证失败**
   - 检查API密钥是否正确
   - 确认激活码格式有效
   - 查看服务器日志

3. **权限错误**
   - 确保数据库文件可写
   - 检查日志目录权限

### 日志查看

服务器日志位置：
- 应用日志：`data/logs/app.log`
- 访问日志：`data/logs/access.log`
- 错误日志：`data/logs/error.log`
- 许可证日志：`data/logs/license.log`

### 网络测试

```bash
# 测试端口连通性
telnet 服务器IP 5000

# 测试HTTP连接
curl http://服务器IP:5000/health
```

## 📊 监控和维护

### 健康检查

访问健康检查端点：
```
GET http://服务器IP:5000/health
```

### 性能监控

访问监控端点：
```
GET http://服务器IP:5000/metrics
```

### 数据备份

自动备份配置在 `config.yaml` 中：

```yaml
backup:
  enabled: true
  schedule: "0 2 * * *"  # 每天凌晨2点
  retention_days: 30
```

## 🔐 安全建议

1. **更改默认密钥**
   - 修改 `config.yaml` 中的 `secret_key`
   - 更新API密钥

2. **启用HTTPS**（生产环境）
   ```yaml
   production:
     ssl:
       enabled: true
       cert_file: "config/ssl/cert.pem"
       key_file: "config/ssl/key.pem"
   ```

3. **限制访问**
   - 配置防火墙规则
   - 使用VPN或内网隔离

## 📞 技术支持

如果遇到问题，请：

1. 查看日志文件
2. 检查网络配置
3. 验证防火墙设置
4. 确认服务状态

---

**配置完成后，您的激活码系统就可以在局域网内正常工作了！** 🎉
