#!/bin/bash
# 资管工具平台服务端部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
PROJECT_NAME="资管工具平台服务端"
PROJECT_DIR="/opt/resource-mgmt-server"
BACKUP_DIR="/opt/backups/resource-mgmt-server"
LOG_FILE="/var/log/resource-mgmt-deploy.log"
DOCKER_COMPOSE_FILE="docker-compose.yml"

# 日志函数
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO:${NC} $1" | tee -a "$LOG_FILE"
}

# 检查依赖
check_dependencies() {
    log "检查系统依赖..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    # 检查Git
    if ! command -v git &> /dev/null; then
        error "Git 未安装，请先安装 Git"
        exit 1
    fi
    
    log "系统依赖检查完成"
}

# 创建目录结构
create_directories() {
    log "创建目录结构..."
    
    sudo mkdir -p "$PROJECT_DIR"
    sudo mkdir -p "$BACKUP_DIR"
    sudo mkdir -p "$(dirname "$LOG_FILE")"
    
    # 创建数据目录
    sudo mkdir -p "$PROJECT_DIR/data/database"
    sudo mkdir -p "$PROJECT_DIR/data/uploads"
    sudo mkdir -p "$PROJECT_DIR/data/logs"
    sudo mkdir -p "$PROJECT_DIR/data/temp"
    sudo mkdir -p "$PROJECT_DIR/config/ssl"
    
    log "目录结构创建完成"
}

# 备份现有部署
backup_existing() {
    if [ -d "$PROJECT_DIR" ] && [ "$(ls -A $PROJECT_DIR)" ]; then
        log "备份现有部署..."
        
        BACKUP_NAME="backup_$(date +%Y%m%d_%H%M%S)"
        sudo cp -r "$PROJECT_DIR" "$BACKUP_DIR/$BACKUP_NAME"
        
        log "备份完成: $BACKUP_DIR/$BACKUP_NAME"
    else
        info "无需备份，目录为空或不存在"
    fi
}

# 部署代码
deploy_code() {
    log "部署应用代码..."
    
    # 如果是Git仓库，拉取最新代码
    if [ -d "$PROJECT_DIR/.git" ]; then
        cd "$PROJECT_DIR"
        sudo git pull origin main
    else
        # 否则从源目录复制
        if [ -n "$SOURCE_DIR" ] && [ -d "$SOURCE_DIR" ]; then
            sudo cp -r "$SOURCE_DIR"/* "$PROJECT_DIR/"
        else
            error "请指定源代码目录或Git仓库"
            exit 1
        fi
    fi
    
    log "代码部署完成"
}

# 配置环境
setup_environment() {
    log "配置环境..."
    
    # 创建环境变量文件
    if [ ! -f "$PROJECT_DIR/.env" ]; then
        cat > "$PROJECT_DIR/.env" << EOF
# 生产环境配置
FLASK_ENV=production
SECRET_KEY=$(openssl rand -hex 32)
JWT_SECRET=$(openssl rand -hex 32)
GRAFANA_PASSWORD=$(openssl rand -base64 12)

# 数据库配置
DATABASE_URL=sqlite:///data/database/app.db

# Redis配置
REDIS_URL=redis://redis:6379/0

# 日志级别
LOG_LEVEL=INFO
EOF
        log "环境配置文件已创建"
    else
        info "环境配置文件已存在，跳过创建"
    fi
    
    # 设置权限
    sudo chown -R 1000:1000 "$PROJECT_DIR/data"
    sudo chmod -R 755 "$PROJECT_DIR/data"
    
    log "环境配置完成"
}

# 构建镜像
build_images() {
    log "构建Docker镜像..."
    
    cd "$PROJECT_DIR"
    
    # 构建主应用镜像
    sudo docker build -t resource-mgmt-server:latest .
    
    # 构建备份服务镜像
    if [ -f "Dockerfile.backup" ]; then
        sudo docker build -f Dockerfile.backup -t resource-mgmt-backup:latest .
    fi
    
    log "Docker镜像构建完成"
}

# 启动服务
start_services() {
    log "启动服务..."
    
    cd "$PROJECT_DIR"
    
    # 停止现有服务
    sudo docker-compose down --remove-orphans
    
    # 启动新服务
    sudo docker-compose up -d
    
    # 等待服务启动
    sleep 10
    
    # 检查服务状态
    if sudo docker-compose ps | grep -q "Up"; then
        log "服务启动成功"
    else
        error "服务启动失败"
        sudo docker-compose logs
        exit 1
    fi
}

# 健康检查
health_check() {
    log "执行健康检查..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost/health > /dev/null 2>&1; then
            log "健康检查通过"
            return 0
        fi
        
        info "健康检查失败，重试 $attempt/$max_attempts"
        sleep 5
        ((attempt++))
    done
    
    error "健康检查失败，服务可能未正常启动"
    return 1
}

# 初始化数据库
init_database() {
    log "初始化数据库..."
    
    # 运行数据库初始化脚本
    sudo docker-compose exec app python scripts/init_db.py
    
    log "数据库初始化完成"
}

# 清理旧镜像
cleanup() {
    log "清理旧Docker镜像..."
    
    # 删除未使用的镜像
    sudo docker image prune -f
    
    # 删除未使用的容器
    sudo docker container prune -f
    
    # 删除未使用的网络
    sudo docker network prune -f
    
    log "清理完成"
}

# 显示部署信息
show_info() {
    log "部署完成！"
    echo
    echo "=========================================="
    echo "  $PROJECT_NAME 部署信息"
    echo "=========================================="
    echo "项目目录: $PROJECT_DIR"
    echo "备份目录: $BACKUP_DIR"
    echo "日志文件: $LOG_FILE"
    echo
    echo "服务地址:"
    echo "  主应用: http://localhost"
    echo "  API文档: http://localhost/api/info"
    echo "  健康检查: http://localhost/health"
    echo "  监控面板: http://localhost:3000 (Grafana)"
    echo "  指标收集: http://localhost:9090 (Prometheus)"
    echo
    echo "管理命令:"
    echo "  查看日志: sudo docker-compose logs -f"
    echo "  重启服务: sudo docker-compose restart"
    echo "  停止服务: sudo docker-compose down"
    echo "  更新服务: $0 --update"
    echo "=========================================="
}

# 主函数
main() {
    log "开始部署 $PROJECT_NAME"
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --source-dir)
                SOURCE_DIR="$2"
                shift 2
                ;;
            --update)
                UPDATE_MODE=true
                shift
                ;;
            --skip-backup)
                SKIP_BACKUP=true
                shift
                ;;
            --help)
                echo "用法: $0 [选项]"
                echo "选项:"
                echo "  --source-dir DIR    指定源代码目录"
                echo "  --update           更新模式（跳过某些步骤）"
                echo "  --skip-backup      跳过备份"
                echo "  --help             显示帮助信息"
                exit 0
                ;;
            *)
                error "未知参数: $1"
                exit 1
                ;;
        esac
    done
    
    # 执行部署步骤
    check_dependencies
    
    if [ "$UPDATE_MODE" != true ]; then
        create_directories
    fi
    
    if [ "$SKIP_BACKUP" != true ]; then
        backup_existing
    fi
    
    deploy_code
    setup_environment
    build_images
    start_services
    
    if [ "$UPDATE_MODE" != true ]; then
        init_database
    fi
    
    health_check
    cleanup
    show_info
    
    log "部署完成！"
}

# 错误处理
trap 'error "部署过程中发生错误，请检查日志: $LOG_FILE"' ERR

# 执行主函数
main "$@"
