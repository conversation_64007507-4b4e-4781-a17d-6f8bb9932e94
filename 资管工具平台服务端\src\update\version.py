#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
版本管理器
"""

import re
from typing import Dict, Any, Optional, List
from packaging import version
from loguru import logger

from .models import VersionInfo, ClientInfo, DownloadRecord
from src.database import db


class VersionManager:
    """版本管理器"""
    
    def __init__(self):
        self.current_version = "1.0.0"
        self.minimum_version = "0.9.0"
        self.force_update_below = "0.8.0"
    
    def check_for_updates(self, current_version: str, platform: str = 'windows', 
                         architecture: str = 'x64') -> Dict[str, Any]:
        """检查更新"""
        try:
            # 查找最新版本
            latest_version = VersionInfo.query.filter_by(
                platform=platform,
                architecture=architecture,
                is_active=True
            ).order_by(VersionInfo.release_date.desc()).first()
            
            if not latest_version:
                return {
                    'has_update': False,
                    'message': '暂无可用更新'
                }
            
            # 比较版本
            has_update = self._compare_versions(current_version, latest_version.version) < 0
            
            # 检查是否强制更新
            is_force_update = (
                latest_version.is_force_update or 
                self._compare_versions(current_version, self.force_update_below) < 0
            )
            
            # 检查最低版本要求
            is_supported = self._compare_versions(current_version, self.minimum_version) >= 0
            
            result = {
                'has_update': has_update,
                'current_version': current_version,
                'latest_version': latest_version.version,
                'is_force_update': is_force_update,
                'is_supported': is_supported,
                'platform': platform,
                'architecture': architecture
            }
            
            if has_update:
                result.update({
                    'download_url': f'/api/update/download?version={latest_version.version}&platform={platform}&arch={architecture}',
                    'file_size': latest_version.file_size,
                    'release_date': latest_version.release_date.isoformat(),
                    'description': latest_version.description,
                    'changelog': latest_version.changelog
                })
            
            # 更新客户端信息
            self._update_client_info(current_version, platform, architecture)
            
            return result
            
        except Exception as e:
            logger.error(f"检查更新异常: {e}")
            return {
                'has_update': False,
                'error': '检查更新时发生错误'
            }
    
    def get_version_list(self, platform: str = None, architecture: str = None, 
                        active_only: bool = True) -> List[Dict[str, Any]]:
        """获取版本列表"""
        try:
            query = VersionInfo.query
            
            if platform:
                query = query.filter_by(platform=platform)
            
            if architecture:
                query = query.filter_by(architecture=architecture)
            
            if active_only:
                query = query.filter_by(is_active=True)
            
            versions = query.order_by(VersionInfo.release_date.desc()).all()
            
            return [version.to_dict() for version in versions]
            
        except Exception as e:
            logger.error(f"获取版本列表异常: {e}")
            return []
    
    def get_changelog(self, version_filter: str = None, limit: int = 10) -> List[Dict[str, Any]]:
        """获取更新日志"""
        try:
            query = VersionInfo.query.filter_by(is_active=True)
            
            if version_filter:
                query = query.filter(VersionInfo.version.contains(version_filter))
            
            versions = query.order_by(VersionInfo.release_date.desc()).limit(limit).all()
            
            changelog = []
            for version_info in versions:
                changelog.append({
                    'version': version_info.version,
                    'release_date': version_info.release_date.isoformat(),
                    'description': version_info.description,
                    'changelog': version_info.changelog,
                    'platform': version_info.platform,
                    'architecture': version_info.architecture,
                    'file_size': version_info.file_size,
                    'download_count': version_info.download_count,
                    'is_force_update': version_info.is_force_update
                })
            
            return changelog
            
        except Exception as e:
            logger.error(f"获取更新日志异常: {e}")
            return []
    
    def record_download(self, version_id: int, client_ip: str, user_agent: str) -> bool:
        """记录下载"""
        try:
            # 创建下载记录
            download_record = DownloadRecord(
                version_id=version_id,
                client_ip=client_ip,
                user_agent=user_agent
            )
            
            db.session.add(download_record)
            
            # 更新下载计数
            version_info = VersionInfo.query.get(version_id)
            if version_info:
                version_info.download_count += 1
            
            db.session.commit()
            
            return True
            
        except Exception as e:
            logger.error(f"记录下载异常: {e}")
            db.session.rollback()
            return False
    
    def get_download_statistics(self) -> Dict[str, Any]:
        """获取下载统计"""
        try:
            # 总下载次数
            total_downloads = db.session.query(
                db.func.sum(VersionInfo.download_count)
            ).scalar() or 0
            
            # 按平台统计
            platform_stats = db.session.query(
                VersionInfo.platform,
                db.func.sum(VersionInfo.download_count)
            ).group_by(VersionInfo.platform).all()
            
            # 按版本统计
            version_stats = db.session.query(
                VersionInfo.version,
                VersionInfo.download_count
            ).order_by(VersionInfo.download_count.desc()).limit(10).all()
            
            # 最近下载
            from datetime import datetime, timedelta
            yesterday = datetime.utcnow() - timedelta(days=1)
            recent_downloads = DownloadRecord.query.filter(
                DownloadRecord.download_time >= yesterday
            ).count()
            
            return {
                'total_downloads': total_downloads,
                'recent_downloads': recent_downloads,
                'platform_stats': {platform: count for platform, count in platform_stats},
                'version_stats': [{'version': v, 'downloads': c} for v, c in version_stats]
            }
            
        except Exception as e:
            logger.error(f"获取下载统计异常: {e}")
            return {}
    
    def _compare_versions(self, version1: str, version2: str) -> int:
        """比较版本号"""
        try:
            v1 = version.parse(version1)
            v2 = version.parse(version2)
            
            if v1 < v2:
                return -1
            elif v1 > v2:
                return 1
            else:
                return 0
                
        except Exception as e:
            logger.error(f"版本比较异常: {e}")
            # 降级到字符串比较
            if version1 < version2:
                return -1
            elif version1 > version2:
                return 1
            else:
                return 0
    
    def _update_client_info(self, current_version: str, platform: str, architecture: str):
        """更新客户端信息"""
        try:
            # 生成客户端ID（基于IP和User-Agent）
            from flask import request
            import hashlib
            
            client_data = f"{request.remote_addr}_{request.headers.get('User-Agent', '')}"
            client_id = hashlib.md5(client_data.encode()).hexdigest()
            
            # 查找或创建客户端记录
            client_info = ClientInfo.query.filter_by(client_id=client_id).first()
            
            if not client_info:
                client_info = ClientInfo(
                    client_id=client_id,
                    current_version=current_version,
                    platform=platform,
                    architecture=architecture
                )
                db.session.add(client_info)
            else:
                client_info.current_version = current_version
                client_info.platform = platform
                client_info.architecture = architecture
                client_info.last_check_time = datetime.utcnow()
            
            db.session.commit()
            
        except Exception as e:
            logger.error(f"更新客户端信息异常: {e}")
    
    def validate_version_format(self, version_str: str) -> bool:
        """验证版本号格式"""
        try:
            version.parse(version_str)
            return True
        except Exception:
            return False
