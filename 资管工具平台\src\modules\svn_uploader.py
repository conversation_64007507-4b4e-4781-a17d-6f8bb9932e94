#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SVN载包和上传模块
基于原始的载包和上传工具，提供SVN检出和文件上传功能
"""

import os
import shutil
import stat
import subprocess
import json
import hashlib
import threading
from datetime import datetime
from typing import Optional, Dict, Any, List
from pathlib import Path
from PyQt6.QtCore import QObject, pyqtSignal, QThread

try:
    import py7zr
except ImportError:
    py7zr = None


class SvnManager:
    """SVN管理器"""
    
    @staticmethod
    def checkout(svn_url: str, checkout_dir: str) -> tuple[bool, str]:
        """
        执行SVN检出

        Args:
            svn_url: SVN地址
            checkout_dir: 检出目录

        Returns:
            tuple: (是否成功, 消息)
        """
        try:
            # 标准化SVN URL
            svn_url = svn_url.replace('\\', '/')

            # 执行SVN检出命令
            result = subprocess.run(
                ["svn", "checkout", svn_url, checkout_dir],
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='replace'
            )

            if result.returncode == 0:
                return True, "SVN检出成功"
            else:
                error_msg = f"SVN检出失败，返回码 {result.returncode}:\n{result.stderr}"
                return False, error_msg

        except FileNotFoundError:
            return False, "未找到SVN命令，请确保已安装SVN客户端"
        except Exception as e:
            return False, f"SVN检出时发生错误: {str(e)}"
            
    @staticmethod
    def is_svn_available() -> bool:
        """检查SVN是否可用"""
        try:
            result = subprocess.run(
                ["svn", "--version"], 
                capture_output=True, 
                text=True
            )
            return result.returncode == 0
        except FileNotFoundError:
            return False


class FileManager:
    """文件管理器"""
    
    @staticmethod
    def remove_readonly(path: str):
        """移除只读属性"""
        for root, dirs, files in os.walk(path):
            for dir_ in dirs:
                os.chmod(os.path.join(root, dir_), stat.S_IWRITE)
            for file_ in files:
                os.chmod(os.path.join(root, file_), stat.S_IWRITE)
        os.chmod(path, stat.S_IWRITE)
        
    @staticmethod
    def create_directory_structure(base_path: str, folder_name: str) -> str:
        """
        创建目录结构
        
        Args:
            base_path: 基础路径
            folder_name: 文件夹名称
            
        Returns:
            str: 创建的文件夹路径
        """
        folder_path = os.path.join(base_path, folder_name)
        
        # 创建主文件夹
        if not os.path.exists(folder_path):
            os.makedirs(folder_path)
            
        # 创建子文件夹
        client_update_folder = os.path.join(folder_path, "客户端更新")
        if not os.path.exists(client_update_folder):
            os.makedirs(client_update_folder)
            
        docs_folder = os.path.join(folder_path, "相关说明文档")
        if not os.path.exists(docs_folder):
            os.makedirs(docs_folder)
            
        return folder_path
        
    @staticmethod
    def move_script_folders(folder_path: str):
        """移动脚本文件夹"""
        client_update_folder = os.path.join(folder_path, "客户端更新")
        
        # 移动并重命名 +script 文件夹
        script_folder = os.path.join(folder_path, "+script")
        if os.path.exists(script_folder) and os.path.isdir(script_folder):
            shutil.move(script_folder, os.path.join(client_update_folder, "script"))
            
        # 移动并重命名 +ui 文件夹
        ui_folder = os.path.join(folder_path, "+ui")
        if os.path.exists(ui_folder) and os.path.isdir(ui_folder):
            shutil.move(ui_folder, os.path.join(client_update_folder, "ui"))
            
    @staticmethod
    def create_modification_note(folder_path: str, date_str: str):
        """创建修改说明文件"""
        docs_folder = os.path.join(folder_path, "相关说明文档")
        modification_note_path = os.path.join(docs_folder, "修改说明.txt")
        
        with open(modification_note_path, "w", encoding="utf-8") as f:
            f.write(f"{date_str}FZ\n\n\n在xsj_env_mini最新\n\n基础上\n\n整合")
            
    @staticmethod
    def cleanup_files(folder_path: str):
        """清理不需要的文件"""
        # 删除 .log 文件
        for item in os.listdir(folder_path):
            item_path = os.path.join(folder_path, item)
            if os.path.isfile(item_path) and item_path.endswith('.log'):
                os.remove(item_path)
                
        # 删除 .svn 文件夹
        svn_dir = os.path.join(folder_path, '.svn')
        if os.path.exists(svn_dir) and os.path.isdir(svn_dir):
            FileManager.remove_readonly(svn_dir)
            shutil.rmtree(svn_dir)


class CompressManager:
    """压缩管理器"""
    
    @staticmethod
    def compress_folder(folder_path: str, output_path: str) -> bool:
        """
        压缩文件夹为7z格式
        
        Args:
            folder_path: 要压缩的文件夹路径
            output_path: 输出的压缩包路径
            
        Returns:
            bool: 是否成功
        """
        if not py7zr:
            return False
            
        try:
            base_name = os.path.basename(folder_path)
            with py7zr.SevenZipFile(output_path, 'w') as archive:
                archive.writeall(folder_path, arcname=base_name)
            return True
        except Exception:
            return False
            
    @staticmethod
    def is_7z_available() -> bool:
        """检查7z压缩是否可用"""
        return py7zr is not None


class UploadHistoryManager:
    """上传历史管理器"""

    def __init__(self, history_file: str = "upload_history.json"):
        self.history_file = Path(history_file)
        self.history_data = self._load_history()

    def _load_history(self) -> List[Dict]:
        """加载上传历史"""
        if self.history_file.exists():
            try:
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception:
                return []
        return []

    def _save_history(self):
        """保存上传历史"""
        try:
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(self.history_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存历史记录失败: {e}")

    def add_upload_record(self, source_path: str, target_path: str,
                         file_size: int, status: str = "uploading") -> str:
        """添加上传记录"""
        record_id = hashlib.md5(f"{source_path}{datetime.now().isoformat()}".encode()).hexdigest()[:8]

        record = {
            "id": record_id,
            "source_path": source_path,
            "target_path": target_path,
            "file_size": file_size,
            "status": status,  # uploading, completed, failed, cancelled
            "start_time": datetime.now().isoformat(),
            "end_time": None,
            "error_message": None,
            "compressed_size": None
        }

        self.history_data.insert(0, record)  # 最新的在前面
        self._save_history()
        return record_id

    def update_record_status(self, record_id: str, status: str,
                           end_time: str = None, error_message: str = None,
                           compressed_size: int = None):
        """更新记录状态"""
        for record in self.history_data:
            if record["id"] == record_id:
                record["status"] = status
                if end_time:
                    record["end_time"] = end_time
                if error_message:
                    record["error_message"] = error_message
                if compressed_size:
                    record["compressed_size"] = compressed_size
                break
        self._save_history()

    def get_history(self, limit: int = 50) -> List[Dict]:
        """获取历史记录"""
        return self.history_data[:limit]

    def get_record_by_id(self, record_id: str) -> Optional[Dict]:
        """根据ID获取记录"""
        for record in self.history_data:
            if record["id"] == record_id:
                return record
        return None

    def delete_record(self, record_id: str) -> bool:
        """删除记录"""
        for i, record in enumerate(self.history_data):
            if record["id"] == record_id:
                del self.history_data[i]
                self._save_history()
                return True
        return False

    def clear_history(self):
        """清空历史记录"""
        self.history_data = []
        self._save_history()


class FastUploadManager:
    """大包快速上传管理器"""

    @staticmethod
    def get_folder_size(folder_path: str) -> int:
        """获取文件夹大小"""
        try:
            if not folder_path or not os.path.exists(folder_path):
                return 0

            total_size = 0
            for dirpath, _, filenames in os.walk(folder_path):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    try:
                        file_size = os.path.getsize(filepath)
                        if file_size is not None:
                            total_size += file_size
                    except (OSError, FileNotFoundError, TypeError):
                        continue
            return total_size
        except (OSError, TypeError, AttributeError):
            return 0

    @staticmethod
    def should_use_fast_upload(folder_path: str, threshold_mb: int = 100) -> bool:
        """判断是否应该使用快速上传"""
        try:
            folder_size = FastUploadManager.get_folder_size(folder_path)
            if folder_size is None:
                return False

            threshold_bytes = threshold_mb * 1024 * 1024
            return folder_size > threshold_bytes
        except (TypeError, ValueError):
            return False

    @staticmethod
    def compress_with_progress(source_path: str, target_path: str,
                             progress_callback=None) -> tuple[bool, str]:
        """带进度的压缩"""
        try:
            if not py7zr:
                return False, "py7zr库不可用"

            # 计算总文件数
            total_files = 0
            for root, _, files in os.walk(source_path):
                total_files += len(files)

            processed_files = 0

            with py7zr.SevenZipFile(target_path, 'w', password=None) as archive:
                for root, _, files in os.walk(source_path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, source_path)

                        try:
                            archive.write(file_path, arcname)
                            processed_files += 1

                            if progress_callback:
                                progress = int((processed_files / total_files) * 100)
                                progress_callback(progress)
                        except Exception as e:
                            print(f"压缩文件失败 {file_path}: {e}")
                            continue

            return True, f"压缩完成，共处理 {processed_files} 个文件"

        except Exception as e:
            return False, f"压缩失败: {str(e)}"

    @staticmethod
    def upload_with_retry(source_file: str, target_dirs: tuple[str, str],
                         max_retries: int = 3) -> tuple[bool, str]:
        """带重试的上传"""
        for attempt in range(max_retries):
            try:
                # 尝试上传到第一个目录
                target_file_1 = os.path.join(target_dirs[0], os.path.basename(source_file))
                shutil.copy2(source_file, target_file_1)

                # 尝试上传到第二个目录
                target_file_2 = os.path.join(target_dirs[1], os.path.basename(source_file))
                shutil.copy2(source_file, target_file_2)

                return True, f"上传成功 (尝试 {attempt + 1}/{max_retries})"

            except Exception as e:
                if attempt == max_retries - 1:
                    return False, f"上传失败，已重试 {max_retries} 次: {str(e)}"
                else:
                    print(f"上传尝试 {attempt + 1} 失败，正在重试: {e}")
                    continue

        return False, "上传失败"


class UndoUploadManager:
    """撤销上传管理器"""

    @staticmethod
    def find_uploaded_files(target_dirs: tuple[str, str], filename_pattern: str) -> List[str]:
        """查找已上传的文件"""
        found_files = []

        for target_dir in target_dirs:
            if not os.path.exists(target_dir):
                continue

            try:
                # 查找匹配的文件
                for file in os.listdir(target_dir):
                    if filename_pattern in file or file.startswith(filename_pattern):
                        file_path = os.path.join(target_dir, file)
                        if os.path.isfile(file_path):
                            found_files.append(file_path)
            except Exception as e:
                print(f"搜索文件失败 {target_dir}: {e}")
                continue

        return found_files

    @staticmethod
    def delete_uploaded_files(file_paths: List[str]) -> tuple[bool, str]:
        """删除已上传的文件"""
        deleted_files = []
        failed_files = []

        for file_path in file_paths:
            try:
                if os.path.exists(file_path):
                    # 尝试删除文件
                    os.remove(file_path)
                    deleted_files.append(file_path)
                    print(f"已删除: {file_path}")
                else:
                    print(f"文件不存在: {file_path}")
            except PermissionError:
                failed_files.append(f"{file_path} (权限不足)")
            except Exception as e:
                failed_files.append(f"{file_path} ({str(e)})")

        if failed_files:
            error_msg = f"部分文件删除失败:\n" + "\n".join(failed_files)
            if deleted_files:
                error_msg += f"\n\n已成功删除 {len(deleted_files)} 个文件"
            return False, error_msg
        elif deleted_files:
            return True, f"成功删除 {len(deleted_files)} 个文件"
        else:
            return False, "没有找到要删除的文件"

    @staticmethod
    def get_file_info(file_path: str) -> Dict[str, Any]:
        """获取文件信息"""
        try:
            stat = os.stat(file_path)
            return {
                "path": file_path,
                "name": os.path.basename(file_path),
                "size": stat.st_size,
                "modified_time": stat.st_mtime,
                "exists": True
            }
        except Exception:
            return {
                "path": file_path,
                "name": os.path.basename(file_path),
                "size": 0,
                "modified_time": 0,
                "exists": False
            }

    @staticmethod
    def confirm_undo_upload(source_folder_name: str, target_dirs: tuple[str, str]) -> tuple[List[str], str]:
        """确认撤销上传的文件列表"""
        # 生成可能的文件名模式
        patterns = [
            f"{source_folder_name}.7z",
            f"{source_folder_name}.zip",
            source_folder_name
        ]

        all_found_files = []
        search_info = []

        for pattern in patterns:
            found_files = UndoUploadManager.find_uploaded_files(target_dirs, pattern)
            all_found_files.extend(found_files)

            if found_files:
                search_info.append(f"模式 '{pattern}': 找到 {len(found_files)} 个文件")
            else:
                search_info.append(f"模式 '{pattern}': 未找到文件")

        # 去重
        unique_files = list(set(all_found_files))

        info_text = "\n".join(search_info)
        if unique_files:
            info_text += f"\n\n总共找到 {len(unique_files)} 个文件待删除"

        return unique_files, info_text


class UploadManager:
    """上传管理器"""
    
    @staticmethod
    def get_upload_directories() -> tuple[str, str]:
        """获取上传目录路径"""
        current_month = datetime.now().strftime('%Y年%m月')
        
        upload_directory_1 = f'w:\\资源管理部本部\\￥工作区￥\\￥待审核￥\\手游\\手机魔域\\{current_month}'
        upload_directory_2 = f'w:\\资源管理部本部\\工作区\\待审核\\手游\\手机魔域\\{current_month}/'
        
        return upload_directory_1, upload_directory_2
        
    @staticmethod
    def upload_file(source_path: str, target_directories: tuple[str, str]) -> tuple[bool, str]:
        """
        上传文件到指定目录
        
        Args:
            source_path: 源文件路径
            target_directories: 目标目录元组
            
        Returns:
            tuple: (是否成功, 消息)
        """
        upload_dir_1, upload_dir_2 = target_directories
        
        # 检查目录是否存在
        if not os.path.exists(upload_dir_1) or not os.path.exists(upload_dir_2):
            return False, "上传目录不存在"
            
        try:
            # 复制到第二个目录
            shutil.copy(source_path, upload_dir_2)
            return True, f"文件已上传到: {upload_dir_2}"
        except Exception as e:
            return False, f"上传失败: {str(e)}"


class SvnUploader(QObject):
    """SVN载包和上传器主类"""
    
    # 信号定义
    progress_updated = pyqtSignal(int)  # 进度更新
    log_message = pyqtSignal(str)       # 日志消息
    processing_finished = pyqtSignal(bool, str)  # 处理完成(成功, 消息)
    
    def __init__(self):
        super().__init__()
        self.svn_manager = SvnManager()
        self.file_manager = FileManager()
        self.compress_manager = CompressManager()
        self.upload_manager = UploadManager()
        self.history_manager = UploadHistoryManager()
        self.fast_upload_manager = FastUploadManager()
        self.undo_upload_manager = UndoUploadManager()
        self.current_upload_id = None
        self.is_cancelled = False

    def cancel_current_upload(self):
        """取消当前上传"""
        self.is_cancelled = True
        if self.current_upload_id:
            self.history_manager.update_record_status(
                self.current_upload_id,
                "cancelled",
                datetime.now().isoformat()
            )
            self.log_message.emit("上传已取消")

    def get_upload_history(self, limit: int = 50) -> List[Dict]:
        """获取上传历史"""
        return self.history_manager.get_history(limit)

    def delete_upload_record(self, record_id: str) -> bool:
        """删除上传记录"""
        return self.history_manager.delete_record(record_id)

    def clear_upload_history(self):
        """清空上传历史"""
        self.history_manager.clear_history()

    def retry_upload(self, record_id: str) -> bool:
        """重试上传"""
        record = self.history_manager.get_record_by_id(record_id)
        if not record:
            return False

        source_path = record["source_path"]
        if os.path.exists(source_path):
            # 重新开始上传
            return self.compress_and_upload(source_path)
        else:
            self.log_message.emit(f"源文件不存在: {source_path}")
            return False

    def undo_last_upload(self) -> tuple[bool, str]:
        """撤销最后一次上传"""
        try:
            # 获取最近的成功上传记录
            history = self.history_manager.get_history(10)
            last_completed = None

            for record in history:
                if record.get("status") == "completed":
                    last_completed = record
                    break

            if not last_completed:
                return False, "没有找到可撤销的上传记录"

            source_path = last_completed.get("source_path", "")
            if not source_path:
                return False, "上传记录中缺少源路径信息"

            # 获取源文件夹名称
            source_folder_name = os.path.basename(source_path)

            # 获取上传目录
            upload_dirs = self.upload_manager.get_upload_directories()

            # 查找要删除的文件
            files_to_delete, search_info = self.undo_upload_manager.confirm_undo_upload(
                source_folder_name, upload_dirs
            )

            if not files_to_delete:
                return False, f"没有找到要撤销的文件\n\n搜索信息:\n{search_info}"

            # 返回文件列表供用户确认
            return True, {
                "record_id": last_completed.get("id"),
                "source_path": source_path,
                "files_to_delete": files_to_delete,
                "search_info": search_info
            }

        except Exception as e:
            error_msg = f"查找撤销文件时发生错误: {str(e)}"
            self.log_message.emit(error_msg)
            return False, error_msg

    def confirm_undo_upload(self, files_to_delete: List[str], record_id: str) -> tuple[bool, str]:
        """确认并执行撤销上传"""
        try:
            self.log_message.emit(f"开始撤销上传，删除 {len(files_to_delete)} 个文件...")

            # 删除文件
            success, message = self.undo_upload_manager.delete_uploaded_files(files_to_delete)

            if success:
                # 更新历史记录状态
                self.history_manager.update_record_status(
                    record_id,
                    "undone",
                    datetime.now().isoformat(),
                    "已撤销上传"
                )
                self.log_message.emit("撤销上传完成")
                return True, message
            else:
                self.log_message.emit(f"撤销上传失败: {message}")
                return False, message

        except Exception as e:
            error_msg = f"撤销上传过程中发生错误: {str(e)}"
            self.log_message.emit(error_msg)
            return False, error_msg

    def get_undo_preview(self, source_folder_name: str) -> tuple[List[Dict], str]:
        """获取撤销预览信息"""
        try:
            upload_dirs = self.upload_manager.get_upload_directories()
            files_to_delete, search_info = self.undo_upload_manager.confirm_undo_upload(
                source_folder_name, upload_dirs
            )

            file_info_list = []
            for file_path in files_to_delete:
                file_info = self.undo_upload_manager.get_file_info(file_path)
                file_info_list.append(file_info)

            return file_info_list, search_info

        except Exception as e:
            return [], f"获取预览信息失败: {str(e)}"
        
    def checkout_and_process(self, svn_url: str, checkout_dir: str) -> bool:
        """
        检出并处理文件
        
        Args:
            svn_url: SVN地址
            checkout_dir: 检出目录
            
        Returns:
            bool: 是否成功
        """
        try:
            self.log_message.emit(f"开始SVN检出: {svn_url}")
            self.progress_updated.emit(10)
            
            # 检查SVN是否可用
            if not self.svn_manager.is_svn_available():
                self.log_message.emit("错误：SVN客户端不可用")
                self.processing_finished.emit(False, "SVN客户端不可用")
                return False
                
            # 记录检出前的目录内容
            existing_contents = set()
            if os.path.exists(checkout_dir):
                existing_contents = set(os.listdir(checkout_dir))
                
            self.progress_updated.emit(20)
            
            # 执行SVN检出
            success, message = self.svn_manager.checkout(svn_url, checkout_dir)
            if not success:
                self.log_message.emit(f"SVN检出失败: {message}")
                self.processing_finished.emit(False, message)
                return False

            self.log_message.emit("SVN检出成功")
            self.progress_updated.emit(50)

            # 后处理操作
            self._post_checkout_operations(checkout_dir, svn_url, existing_contents)
            
            self.progress_updated.emit(100)
            self.log_message.emit("检出和处理完成")
            self.processing_finished.emit(True, "检出和处理完成")
            return True
            
        except Exception as e:
            error_msg = f"处理过程中发生错误: {str(e)}"
            self.log_message.emit(error_msg)
            self.processing_finished.emit(False, error_msg)
            return False
            
    def _post_checkout_operations(self, checkout_dir: str, svn_url: str, existing_contents: set):
        """检出后的处理操作"""
        # 获取当前目录的所有内容
        all_contents = set(os.listdir(checkout_dir))
        # 计算出新检出的内容
        new_contents = all_contents - existing_contents
        
        if not new_contents:
            self.log_message.emit("没有新检出的内容")
            return
            
        # 解析SVN URL获取文件夹信息
        svn_url_parts = svn_url.strip('/').split('/')
        if len(svn_url_parts) < 4:
            self.log_message.emit("警告：SVN地址格式不符合预期")
            return
            
        base_folder_name = svn_url_parts[-2]
        fourth_last_folder_name = svn_url_parts[-4]
        
        # 获取当前日期
        current_date = datetime.now().strftime('%Y%m%d')
        
        # 根据倒数第四层目录名添加后缀
        if fourth_last_folder_name == "手机魔域":
            suffix = "【手机魔域】[任务包]"
        elif fourth_last_folder_name == "魔域手游":
            suffix = "【西山居手机魔域】[任务包]"
        else:
            suffix = ""
            
        # 删除文件夹名中的特定字眼
        base_folder_name = base_folder_name.replace("[手机魔域]", "").replace("[魔域手游]", "")
        
        # 生成新文件夹名
        if len(base_folder_name) >= 8:
            base_new_folder_name = current_date + suffix + base_folder_name[8:] + "v1.0(龚杰翔)"
        else:
            base_new_folder_name = current_date + suffix + base_folder_name + "v1.0(龚杰翔)"

        # 确保文件夹名唯一，如果已存在则添加序号
        new_folder_name = base_new_folder_name
        counter = 1
        while os.path.exists(os.path.join(checkout_dir, new_folder_name)):
            new_folder_name = f"{base_new_folder_name}_{counter}"
            counter += 1

        self.log_message.emit(f"创建新文件夹: {new_folder_name}")

        # 先创建主文件夹
        new_folder_path = os.path.join(checkout_dir, new_folder_name)
        if not os.path.exists(new_folder_path):
            os.makedirs(new_folder_path)

        self.progress_updated.emit(60)

        # 移动新检出的内容到新文件夹
        for item in new_contents:
            item_path = os.path.join(checkout_dir, item)
            shutil.move(item_path, new_folder_path)

        # 创建缺失的子目录结构
        client_update_folder = os.path.join(new_folder_path, "客户端更新")
        if not os.path.exists(client_update_folder):
            os.makedirs(client_update_folder)

        docs_folder = os.path.join(new_folder_path, "相关说明文档")
        if not os.path.exists(docs_folder):
            os.makedirs(docs_folder)
            
        self.progress_updated.emit(70)
        
        # 清理不需要的文件
        self.file_manager.cleanup_files(new_folder_path)
        
        # 移动脚本文件夹
        self.file_manager.move_script_folders(new_folder_path)
        
        # 创建修改说明文件
        self.file_manager.create_modification_note(new_folder_path, current_date)
        
        self.progress_updated.emit(80)
        self.log_message.emit("文件处理完成")
        
    def compress_and_upload(self, folder_path: str) -> tuple[bool, str]:
        """
        压缩并上传文件夹（增强版）

        Args:
            folder_path: 要压缩上传的文件夹路径

        Returns:
            tuple: (是否成功, 压缩包路径或错误消息)
        """
        try:
            self.is_cancelled = False
            self.log_message.emit(f"开始压缩文件夹: {folder_path}")

            # 检查7z是否可用
            if not self.compress_manager.is_7z_available():
                self.log_message.emit("错误：7z压缩库不可用")
                return False, "7z压缩库不可用，请安装py7zr"

            # 获取文件夹大小
            folder_size = self.fast_upload_manager.get_folder_size(folder_path)
            try:
                if folder_size is not None and isinstance(folder_size, (int, float)) and folder_size > 0:
                    size_mb = folder_size / (1024*1024)
                    self.log_message.emit(f"文件夹大小: {size_mb:.1f} MB")
                else:
                    self.log_message.emit("文件夹大小: 0 MB")
            except (TypeError, ValueError):
                self.log_message.emit("文件夹大小: 未知")

            # 获取上传目录
            upload_dirs = self.upload_manager.get_upload_directories()

            # 生成压缩包路径
            base_name = os.path.basename(folder_path)
            compressed_file_path = os.path.join(upload_dirs[0], f"{base_name}.7z")

            # 添加上传记录
            self.current_upload_id = self.history_manager.add_upload_record(
                folder_path, compressed_file_path, folder_size, "uploading"
            )

            # 判断是否使用快速上传
            use_fast_upload = self.fast_upload_manager.should_use_fast_upload(folder_path)

            if use_fast_upload:
                self.log_message.emit("检测到大文件包，使用快速上传模式")

                # 使用带进度的压缩
                def progress_callback(progress):
                    if self.is_cancelled:
                        return
                    self.progress_updated.emit(progress // 2)  # 压缩占50%进度
                    self.log_message.emit(f"压缩进度: {progress}%")

                success, message = self.fast_upload_manager.compress_with_progress(
                    folder_path, compressed_file_path, progress_callback
                )
            else:
                # 使用标准压缩
                success = self.compress_manager.compress_folder(folder_path, compressed_file_path)
                message = "压缩完成" if success else "压缩失败"

            if self.is_cancelled:
                self.log_message.emit("操作已取消")
                return False, "操作已取消"

            if not success:
                self.history_manager.update_record_status(
                    self.current_upload_id, "failed",
                    datetime.now().isoformat(), message
                )
                self.log_message.emit("压缩失败")
                return False, "压缩失败"

            # 获取压缩后文件大小
            compressed_size = os.path.getsize(compressed_file_path) if os.path.exists(compressed_file_path) else 0

            self.log_message.emit(f"压缩成功: {compressed_file_path}")
            try:
                if compressed_size is not None and isinstance(compressed_size, (int, float)) and compressed_size > 0:
                    size_mb = compressed_size / (1024*1024)
                    self.log_message.emit(f"压缩后大小: {size_mb:.1f} MB")
                else:
                    self.log_message.emit("压缩后大小: 0 MB")
            except (TypeError, ValueError):
                self.log_message.emit("压缩后大小: 未知")

            # 上传文件
            if use_fast_upload:
                upload_success, upload_message = self.fast_upload_manager.upload_with_retry(
                    compressed_file_path, upload_dirs, max_retries=3
                )
            else:
                upload_success, upload_message = self.upload_manager.upload_file(
                    compressed_file_path, upload_dirs
                )

            if self.is_cancelled:
                self.log_message.emit("操作已取消")
                return False, "操作已取消"

            # 更新历史记录
            if upload_success:
                self.history_manager.update_record_status(
                    self.current_upload_id, "completed",
                    datetime.now().isoformat(), None, compressed_size
                )
                self.log_message.emit(upload_message)

                # 添加调试信息
                print(f"[DEBUG] 上传成功，更新历史记录: {self.current_upload_id}")
                print(f"[DEBUG] 当前历史记录数量: {len(self.history_manager.history_data)}")

                return True, compressed_file_path
            else:
                self.history_manager.update_record_status(
                    self.current_upload_id, "failed",
                    datetime.now().isoformat(), upload_message
                )
                self.log_message.emit(f"上传失败: {upload_message}")
                return False, upload_message

        except Exception as e:
            error_msg = f"压缩上传过程中发生错误: {str(e)}"
            if self.current_upload_id:
                self.history_manager.update_record_status(
                    self.current_upload_id, "failed",
                    datetime.now().isoformat(), error_msg
                )
            self.log_message.emit(error_msg)
            return False, error_msg


class SvnUploaderWorker(QThread):
    """SVN上传工作线程"""
    
    progress_updated = pyqtSignal(int)
    log_message = pyqtSignal(str)
    processing_finished = pyqtSignal(bool, str)
    
    def __init__(self, operation: str, **kwargs):
        super().__init__()
        self.operation = operation
        self.kwargs = kwargs
        self.uploader = SvnUploader()
        self.is_cancelled = False

        # 连接信号
        self.uploader.progress_updated.connect(self.progress_updated)
        self.uploader.log_message.connect(self.log_message)
        self.uploader.processing_finished.connect(self.processing_finished)

    def cancel(self):
        """取消操作"""
        self.is_cancelled = True
        self.uploader.cancel_current_upload()
        
    def run(self):
        """运行任务"""
        try:
            if self.is_cancelled:
                return

            if self.operation == "checkout":
                self.uploader.checkout_and_process(
                    self.kwargs["svn_url"],
                    self.kwargs["checkout_dir"]
                )
            elif self.operation == "upload":
                success, result = self.uploader.compress_and_upload(
                    self.kwargs["folder_path"]
                )
                if not self.is_cancelled:
                    self.processing_finished.emit(success, result)
        except Exception as e:
            if not self.is_cancelled:
                self.processing_finished.emit(False, f"操作失败: {str(e)}")
