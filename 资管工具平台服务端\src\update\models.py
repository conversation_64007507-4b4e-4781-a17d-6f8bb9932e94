#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新服务数据模型
"""

from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, BigInteger, ForeignKey
from sqlalchemy.orm import relationship
from src.database import db


class VersionInfo(db.Model):
    """版本信息"""
    __tablename__ = 'version_info'
    
    id = Column(Integer, primary_key=True)
    version = Column(String(20), nullable=False, index=True)
    platform = Column(String(20), default='windows', nullable=False)
    architecture = Column(String(10), default='x64', nullable=False)
    
    release_date = Column(DateTime, default=datetime.utcnow, nullable=False)
    description = Column(Text, nullable=True)
    changelog = Column(Text, nullable=True)
    
    file_path = Column(String(500), nullable=False)
    file_size = Column(BigInteger, default=0)
    file_hash = Column(String(64), nullable=True)  # SHA256哈希
    
    download_count = Column(Integer, default=0)
    is_active = Column(Boolean, default=True)
    is_force_update = Column(Boolean, default=False)
    
    # 关联下载记录
    downloads = relationship("DownloadRecord", back_populates="version_info")
    
    def __repr__(self):
        return f'<VersionInfo {self.version} - {self.platform}>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'version': self.version,
            'platform': self.platform,
            'architecture': self.architecture,
            'release_date': self.release_date.isoformat() if self.release_date else None,
            'description': self.description,
            'changelog': self.changelog,
            'file_path': self.file_path,
            'file_size': self.file_size,
            'file_hash': self.file_hash,
            'download_count': self.download_count,
            'is_active': self.is_active,
            'is_force_update': self.is_force_update
        }


class DownloadRecord(db.Model):
    """下载记录"""
    __tablename__ = 'download_records'
    
    id = Column(Integer, primary_key=True)
    version_id = Column(Integer, ForeignKey('version_info.id'), nullable=False)
    
    client_ip = Column(String(45), nullable=True)  # 支持IPv6
    user_agent = Column(Text, nullable=True)
    download_time = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    is_completed = Column(Boolean, default=False)
    bytes_downloaded = Column(BigInteger, default=0)
    
    # 关联版本信息
    version_info = relationship("VersionInfo", back_populates="downloads")
    
    def __repr__(self):
        return f'<DownloadRecord {self.version_id} - {self.download_time}>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'version_id': self.version_id,
            'client_ip': self.client_ip,
            'user_agent': self.user_agent,
            'download_time': self.download_time.isoformat() if self.download_time else None,
            'is_completed': self.is_completed,
            'bytes_downloaded': self.bytes_downloaded
        }


class UpdateChannel(db.Model):
    """更新渠道"""
    __tablename__ = 'update_channels'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(50), unique=True, nullable=False)
    description = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True)
    
    # 渠道配置
    auto_update = Column(Boolean, default=True)
    beta_updates = Column(Boolean, default=False)
    force_update_threshold = Column(String(20), nullable=True)  # 强制更新的最低版本
    
    def __repr__(self):
        return f'<UpdateChannel {self.name}>'


class ClientInfo(db.Model):
    """客户端信息"""
    __tablename__ = 'client_info'
    
    id = Column(Integer, primary_key=True)
    client_id = Column(String(64), unique=True, nullable=False, index=True)
    
    current_version = Column(String(20), nullable=True)
    platform = Column(String(20), nullable=True)
    architecture = Column(String(10), nullable=True)
    
    last_check_time = Column(DateTime, default=datetime.utcnow)
    last_update_time = Column(DateTime, nullable=True)
    
    update_channel = Column(String(50), default='stable')
    auto_update_enabled = Column(Boolean, default=True)
    
    def __repr__(self):
        return f'<ClientInfo {self.client_id[:8]}... - {self.current_version}>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'client_id': self.client_id,
            'current_version': self.current_version,
            'platform': self.platform,
            'architecture': self.architecture,
            'last_check_time': self.last_check_time.isoformat() if self.last_check_time else None,
            'last_update_time': self.last_update_time.isoformat() if self.last_update_time else None,
            'update_channel': self.update_channel,
            'auto_update_enabled': self.auto_update_enabled
        }
