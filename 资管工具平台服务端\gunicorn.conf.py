#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gunicorn配置文件
"""

import os
import multiprocessing

# 服务器配置
bind = "0.0.0.0:5000"
backlog = 2048

# 工作进程配置
workers = int(os.getenv('WORKERS', multiprocessing.cpu_count() * 2 + 1))
worker_class = "gevent"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
preload_app = True

# 超时配置
timeout = 30
keepalive = 2
graceful_timeout = 30

# 日志配置
accesslog = "data/logs/access.log"
errorlog = "data/logs/error.log"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# 进程命名
proc_name = "resource-mgmt-server"

# 用户和组
user = "appuser"
group = "appuser"

# 临时目录
tmp_upload_dir = "data/temp"

# SSL配置（如果启用）
if os.getenv('SSL_ENABLED', 'false').lower() == 'true':
    keyfile = os.getenv('SSL_KEY_FILE', 'config/ssl/key.pem')
    certfile = os.getenv('SSL_CERT_FILE', 'config/ssl/cert.pem')
    ca_certs = os.getenv('SSL_CA_FILE', 'config/ssl/ca.pem')
    ssl_version = 2  # TLS

# 钩子函数
def on_starting(server):
    """服务器启动时调用"""
    server.log.info("资管工具平台服务端正在启动...")

def on_reload(server):
    """服务器重载时调用"""
    server.log.info("资管工具平台服务端正在重载...")

def when_ready(server):
    """服务器准备就绪时调用"""
    server.log.info("资管工具平台服务端已准备就绪")

def on_exit(server):
    """服务器退出时调用"""
    server.log.info("资管工具平台服务端正在关闭...")

def worker_int(worker):
    """工作进程收到SIGINT信号时调用"""
    worker.log.info("工作进程 %s 收到中断信号", worker.pid)

def pre_fork(server, worker):
    """工作进程fork之前调用"""
    server.log.info("工作进程 %s 即将启动", worker.pid)

def post_fork(server, worker):
    """工作进程fork之后调用"""
    server.log.info("工作进程 %s 已启动", worker.pid)

def post_worker_init(worker):
    """工作进程初始化完成后调用"""
    worker.log.info("工作进程 %s 初始化完成", worker.pid)

def worker_abort(worker):
    """工作进程异常退出时调用"""
    worker.log.error("工作进程 %s 异常退出", worker.pid)
