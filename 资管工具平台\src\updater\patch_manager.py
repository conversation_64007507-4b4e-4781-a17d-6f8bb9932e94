#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
补丁管理器
处理增量更新和补丁应用
"""

import os
import json
import shutil
import hashlib
import zipfile
from pathlib import Path
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum


class PatchType(Enum):
    """补丁类型"""
    ADD = "add"          # 添加文件
    MODIFY = "modify"    # 修改文件
    DELETE = "delete"    # 删除文件
    RENAME = "rename"    # 重命名文件


@dataclass
class PatchOperation:
    """补丁操作"""
    type: PatchType
    source_path: str
    target_path: str = ""
    file_hash: str = ""
    backup_path: str = ""


@dataclass
class PatchInfo:
    """补丁信息"""
    patch_id: str
    from_version: str
    to_version: str
    operations: List[PatchOperation]
    description: str
    created_date: str
    file_size: int


class PatchManager:
    """补丁管理器"""
    
    def __init__(self, app_root: str, backup_dir: str = None):
        self.app_root = Path(app_root)
        self.backup_dir = Path(backup_dir) if backup_dir else self.app_root / "backup"
        self.patch_dir = self.app_root / "patches"
        
        # 确保目录存在
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        self.patch_dir.mkdir(parents=True, exist_ok=True)
    
    def create_patch(self, old_version_dir: str, new_version_dir: str, 
                    from_version: str, to_version: str) -> PatchInfo:
        """创建补丁"""
        old_dir = Path(old_version_dir)
        new_dir = Path(new_version_dir)
        
        operations = []
        
        # 扫描文件变化
        old_files = self._scan_files(old_dir)
        new_files = self._scan_files(new_dir)
        
        # 找出新增文件
        for file_path, file_hash in new_files.items():
            if file_path not in old_files:
                operations.append(PatchOperation(
                    type=PatchType.ADD,
                    source_path=str(new_dir / file_path),
                    target_path=file_path,
                    file_hash=file_hash
                ))
        
        # 找出修改的文件
        for file_path, file_hash in new_files.items():
            if file_path in old_files and old_files[file_path] != file_hash:
                operations.append(PatchOperation(
                    type=PatchType.MODIFY,
                    source_path=str(new_dir / file_path),
                    target_path=file_path,
                    file_hash=file_hash
                ))
        
        # 找出删除的文件
        for file_path in old_files:
            if file_path not in new_files:
                operations.append(PatchOperation(
                    type=PatchType.DELETE,
                    source_path="",
                    target_path=file_path
                ))
        
        # 创建补丁信息
        patch_info = PatchInfo(
            patch_id=f"patch_{from_version}_to_{to_version}",
            from_version=from_version,
            to_version=to_version,
            operations=operations,
            description=f"从版本 {from_version} 升级到 {to_version}",
            created_date=self._get_current_time(),
            file_size=self._calculate_patch_size(operations)
        )
        
        return patch_info
    
    def apply_patch(self, patch_file: str) -> bool:
        """应用补丁"""
        try:
            # 解压补丁文件
            patch_info = self._extract_patch(patch_file)
            if not patch_info:
                return False
            
            # 创建备份
            backup_id = self._create_backup()
            
            try:
                # 应用所有操作
                for operation in patch_info.operations:
                    if not self._apply_operation(operation):
                        # 如果操作失败，回滚
                        self._rollback_backup(backup_id)
                        return False
                
                # 更新版本信息
                self._update_version_info(patch_info.to_version)
                
                # 清理临时文件
                self._cleanup_patch_temp()
                
                return True
                
            except Exception as e:
                # 发生错误时回滚
                self._rollback_backup(backup_id)
                raise e
                
        except Exception as e:
            print(f"应用补丁失败: {e}")
            return False
    
    def rollback_patch(self, backup_id: str) -> bool:
        """回滚补丁"""
        try:
            return self._rollback_backup(backup_id)
        except Exception as e:
            print(f"回滚补丁失败: {e}")
            return False
    
    def verify_patch(self, patch_file: str) -> bool:
        """验证补丁文件"""
        try:
            with zipfile.ZipFile(patch_file, 'r') as zip_file:
                # 检查必要文件
                required_files = ['patch_info.json']
                for required_file in required_files:
                    if required_file not in zip_file.namelist():
                        return False
                
                # 验证补丁信息
                patch_info_data = zip_file.read('patch_info.json')
                patch_info = json.loads(patch_info_data.decode('utf-8'))
                
                # 基本验证
                required_fields = ['patch_id', 'from_version', 'to_version', 'operations']
                for field in required_fields:
                    if field not in patch_info:
                        return False
                
                return True
                
        except Exception:
            return False
    
    def get_patch_info(self, patch_file: str) -> Optional[PatchInfo]:
        """获取补丁信息"""
        try:
            with zipfile.ZipFile(patch_file, 'r') as zip_file:
                patch_info_data = zip_file.read('patch_info.json')
                data = json.loads(patch_info_data.decode('utf-8'))
                
                operations = []
                for op_data in data['operations']:
                    operations.append(PatchOperation(
                        type=PatchType(op_data['type']),
                        source_path=op_data['source_path'],
                        target_path=op_data.get('target_path', ''),
                        file_hash=op_data.get('file_hash', ''),
                        backup_path=op_data.get('backup_path', '')
                    ))
                
                return PatchInfo(
                    patch_id=data['patch_id'],
                    from_version=data['from_version'],
                    to_version=data['to_version'],
                    operations=operations,
                    description=data.get('description', ''),
                    created_date=data.get('created_date', ''),
                    file_size=data.get('file_size', 0)
                )
                
        except Exception as e:
            print(f"获取补丁信息失败: {e}")
            return None
    
    def _scan_files(self, directory: Path) -> Dict[str, str]:
        """扫描目录中的所有文件并计算哈希"""
        files = {}
        
        for file_path in directory.rglob('*'):
            if file_path.is_file():
                relative_path = file_path.relative_to(directory)
                file_hash = self._calculate_file_hash(file_path)
                files[str(relative_path)] = file_hash
        
        return files
    
    def _calculate_file_hash(self, file_path: Path) -> str:
        """计算文件哈希"""
        sha256_hash = hashlib.sha256()
        
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                sha256_hash.update(chunk)
        
        return sha256_hash.hexdigest()
    
    def _calculate_patch_size(self, operations: List[PatchOperation]) -> int:
        """计算补丁大小"""
        total_size = 0

        try:
            for operation in operations:
                if operation.type in [PatchType.ADD, PatchType.MODIFY]:
                    if os.path.exists(operation.source_path):
                        try:
                            file_size = os.path.getsize(operation.source_path)
                            if file_size is not None and isinstance(file_size, (int, float)):
                                total_size += file_size
                        except (OSError, TypeError):
                            continue

            return total_size
        except (TypeError, AttributeError):
            return 0
    
    def _extract_patch(self, patch_file: str) -> Optional[PatchInfo]:
        """解压补丁文件"""
        try:
            temp_dir = self.patch_dir / "temp"
            temp_dir.mkdir(exist_ok=True)
            
            with zipfile.ZipFile(patch_file, 'r') as zip_file:
                zip_file.extractall(temp_dir)
            
            return self.get_patch_info(patch_file)
            
        except Exception as e:
            print(f"解压补丁失败: {e}")
            return None
    
    def _create_backup(self) -> str:
        """创建备份"""
        backup_id = f"backup_{self._get_current_time().replace(':', '-')}"
        backup_path = self.backup_dir / backup_id
        
        # 复制当前应用程序文件
        shutil.copytree(self.app_root, backup_path, 
                       ignore=shutil.ignore_patterns('backup', 'patches', '*.log'))
        
        return backup_id
    
    def _apply_operation(self, operation: PatchOperation) -> bool:
        """应用单个操作"""
        try:
            target_path = self.app_root / operation.target_path
            
            if operation.type == PatchType.ADD:
                # 添加文件
                target_path.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(operation.source_path, target_path)
                
            elif operation.type == PatchType.MODIFY:
                # 修改文件
                if target_path.exists():
                    target_path.unlink()
                target_path.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(operation.source_path, target_path)
                
            elif operation.type == PatchType.DELETE:
                # 删除文件
                if target_path.exists():
                    target_path.unlink()
                    
            elif operation.type == PatchType.RENAME:
                # 重命名文件
                source_path = self.app_root / operation.source_path
                if source_path.exists():
                    target_path.parent.mkdir(parents=True, exist_ok=True)
                    source_path.rename(target_path)
            
            return True
            
        except Exception as e:
            print(f"应用操作失败: {e}")
            return False
    
    def _rollback_backup(self, backup_id: str) -> bool:
        """回滚备份"""
        try:
            backup_path = self.backup_dir / backup_id
            
            if not backup_path.exists():
                return False
            
            # 删除当前文件
            for item in self.app_root.iterdir():
                if item.name not in ['backup', 'patches']:
                    if item.is_dir():
                        shutil.rmtree(item)
                    else:
                        item.unlink()
            
            # 恢复备份
            for item in backup_path.iterdir():
                if item.is_dir():
                    shutil.copytree(item, self.app_root / item.name)
                else:
                    shutil.copy2(item, self.app_root / item.name)
            
            return True
            
        except Exception as e:
            print(f"回滚失败: {e}")
            return False
    
    def _update_version_info(self, new_version: str):
        """更新版本信息"""
        version_file = self.app_root / "version.json"
        
        version_info = {
            "version": new_version,
            "updated_date": self._get_current_time()
        }
        
        with open(version_file, 'w', encoding='utf-8') as f:
            json.dump(version_info, f, indent=2, ensure_ascii=False)
    
    def _cleanup_patch_temp(self):
        """清理临时文件"""
        temp_dir = self.patch_dir / "temp"
        if temp_dir.exists():
            shutil.rmtree(temp_dir)
    
    def _get_current_time(self) -> str:
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().isoformat()
    
    def list_backups(self) -> List[str]:
        """列出所有备份"""
        backups = []
        
        if self.backup_dir.exists():
            for backup_path in self.backup_dir.iterdir():
                if backup_path.is_dir() and backup_path.name.startswith('backup_'):
                    backups.append(backup_path.name)
        
        return sorted(backups, reverse=True)
    
    def cleanup_old_backups(self, keep_count: int = 5):
        """清理旧备份"""
        backups = self.list_backups()
        
        if len(backups) > keep_count:
            for backup_name in backups[keep_count:]:
                backup_path = self.backup_dir / backup_name
                if backup_path.exists():
                    shutil.rmtree(backup_path)
