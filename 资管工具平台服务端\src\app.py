#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资管工具平台服务端主应用
"""

import os
import sys
from pathlib import Path
from flask import Flask, jsonify, request
from flask_cors import CORS
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.config import Config
from src.database import db, init_db
from src.auth.middleware import auth_required, api_key_required
from src.license.routes import license_bp
from src.update.routes import update_bp
from src.utils.response import success_response, error_response
from src.utils.logger import setup_logging


def create_app(config_name='production'):
    """创建Flask应用"""
    app = Flask(__name__)
    
    # 加载配置
    config = Config(config_name)
    app.config.from_object(config)
    
    # 设置日志
    setup_logging(config)
    
    # 初始化扩展
    init_extensions(app, config)
    
    # 注册蓝图
    register_blueprints(app)
    
    # 注册错误处理器
    register_error_handlers(app)
    
    # 注册中间件
    register_middleware(app)
    
    logger.info(f"应用启动: {config.APP_NAME} v{config.APP_VERSION}")
    
    return app


def init_extensions(app, config):
    """初始化Flask扩展"""
    # 数据库
    db.init_app(app)
    
    # CORS
    CORS(app, 
         origins=config.CORS_ORIGINS,
         methods=config.CORS_METHODS,
         allow_headers=config.CORS_HEADERS)
    
    # 请求限制
    limiter = Limiter(
        app,
        key_func=get_remote_address,
        default_limits=[config.DEFAULT_RATE_LIMIT]
    )
    app.limiter = limiter
    
    # 初始化数据库
    with app.app_context():
        init_db()


def register_blueprints(app):
    """注册蓝图"""
    # 激活码服务
    app.register_blueprint(license_bp, url_prefix='/api/license')
    
    # 更新服务
    app.register_blueprint(update_bp, url_prefix='/api/update')
    
    # 健康检查
    @app.route('/health')
    def health_check():
        """健康检查端点"""
        try:
            # 检查数据库连接
            db.session.execute('SELECT 1')
            
            # 检查磁盘空间
            disk_usage = os.statvfs('/')
            free_space = disk_usage.f_bavail * disk_usage.f_frsize
            free_space_gb = free_space / (1024**3)
            
            return success_response({
                'status': 'healthy',
                'database': 'connected',
                'disk_space_gb': round(free_space_gb, 2),
                'timestamp': datetime.utcnow().isoformat()
            })
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return error_response('Health check failed', 500)
    
    # API信息
    @app.route('/api/info')
    @api_key_required
    def api_info():
        """API信息端点"""
        return success_response({
            'name': app.config['APP_NAME'],
            'version': app.config['APP_VERSION'],
            'endpoints': {
                'license': '/api/license',
                'update': '/api/update',
                'health': '/health',
                'metrics': '/metrics'
            }
        })


def register_error_handlers(app):
    """注册错误处理器"""
    
    @app.errorhandler(400)
    def bad_request(error):
        return error_response('Bad Request', 400)
    
    @app.errorhandler(401)
    def unauthorized(error):
        return error_response('Unauthorized', 401)
    
    @app.errorhandler(403)
    def forbidden(error):
        return error_response('Forbidden', 403)
    
    @app.errorhandler(404)
    def not_found(error):
        return error_response('Not Found', 404)
    
    @app.errorhandler(429)
    def rate_limit_exceeded(error):
        return error_response('Rate limit exceeded', 429)
    
    @app.errorhandler(500)
    def internal_error(error):
        logger.error(f"内部服务器错误: {error}")
        return error_response('Internal Server Error', 500)


def register_middleware(app):
    """注册中间件"""
    
    @app.before_request
    def log_request():
        """记录请求日志"""
        logger.info(f"{request.method} {request.path} - {request.remote_addr}")
    
    @app.after_request
    def log_response(response):
        """记录响应日志"""
        logger.info(f"{request.method} {request.path} - {response.status_code}")
        return response


def main():
    """主函数"""
    import argparse
    from datetime import datetime
    
    parser = argparse.ArgumentParser(description='资管工具平台服务端')
    parser.add_argument('--config', default='production', 
                       choices=['development', 'production', 'testing'],
                       help='配置环境')
    parser.add_argument('--host', default='0.0.0.0', help='监听地址')
    parser.add_argument('--port', type=int, default=5000, help='监听端口')
    parser.add_argument('--debug', action='store_true', help='调试模式')
    
    args = parser.parse_args()
    
    # 创建应用
    app = create_app(args.config)
    
    # 启动应用
    if args.config == 'development' or args.debug:
        app.run(
            host=args.host,
            port=args.port,
            debug=True,
            threaded=True
        )
    else:
        # 生产环境使用gunicorn启动
        print(f"生产环境请使用: gunicorn -c gunicorn.conf.py src.app:app")


if __name__ == '__main__':
    main()
