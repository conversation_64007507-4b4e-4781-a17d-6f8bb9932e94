#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的激活码管理服务端
"""

import os
import sys
from pathlib import Path
from flask import Flask, jsonify, request, render_template, session, redirect, url_for
from flask_cors import CORS
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
import sqlite3
import json
from datetime import datetime, timedelta
import secrets
import hashlib
import platform

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

app = Flask(__name__)
app.secret_key = 'your-secret-key-change-in-production'

# 启用CORS
CORS(app)

# 配置限流
limiter = Limiter(
    key_func=get_remote_address,
    app=app,
    default_limits=["200 per day", "50 per hour"]
)

# 数据库路径
DB_PATH = os.path.join(os.path.dirname(__file__), 'data', 'database', 'license.db')

def get_db_connection():
    """获取数据库连接"""
    return sqlite3.connect(DB_PATH)

def require_api_key(f):
    """API密钥验证装饰器"""
    def decorated_function(*args, **kwargs):
        api_key = request.headers.get('X-API-Key')
        if not api_key or api_key not in ['client-api-key-123', 'admin-api-key-456']:
            return jsonify({'error': 'Invalid API key'}), 401
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

def require_admin_auth(f):
    """管理员认证装饰器"""
    def decorated_function(*args, **kwargs):
        if not session.get('admin_logged_in'):
            return redirect(url_for('admin_login'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

# ==================== API路由 ====================

@app.route('/api/license/validate', methods=['POST'])
@require_api_key
@limiter.limit("10 per minute")
def validate_activation_code():
    """验证激活码"""
    try:
        data = request.get_json()
        activation_code = data.get('activation_code', '').strip()
        machine_id = data.get('machine_id', '').strip()
        machine_info = data.get('machine_info', {})
        
        if not activation_code or not machine_id:
            return jsonify({
                'success': False,
                'error': 'missing_parameters',
                'message': '缺少必要参数'
            }), 400
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 查询激活码信息
        cursor.execute("""
            SELECT code, license_type, max_devices, expires_at, status
            FROM activation_codes 
            WHERE code = ?
        """, (activation_code,))
        
        code_info = cursor.fetchone()
        
        if not code_info:
            conn.close()
            return jsonify({
                'success': False,
                'error': 'invalid_code',
                'message': '激活码不存在'
            }), 404
        
        code, license_type, max_devices, expires_at, status = code_info
        
        # 检查激活码状态
        if status != 'active':
            conn.close()
            return jsonify({
                'success': False,
                'error': 'code_disabled',
                'message': f'激活码已{status}'
            }), 403
        
        # 检查过期时间
        expire_time = datetime.fromisoformat(expires_at)
        if datetime.now() > expire_time:
            cursor.execute("""
                UPDATE activation_codes 
                SET status = 'expired', updated_at = CURRENT_TIMESTAMP
                WHERE code = ?
            """, (activation_code,))
            conn.commit()
            conn.close()
            return jsonify({
                'success': False,
                'error': 'code_expired',
                'message': '激活码已过期'
            }), 403
        
        # 检查设备数量限制
        cursor.execute("""
            SELECT COUNT(*) FROM active_licenses 
            WHERE activation_code = ? AND status = 'active'
        """, (activation_code,))
        
        active_count = cursor.fetchone()[0]
        
        # 检查当前机器是否已经激活
        cursor.execute("""
            SELECT id FROM active_licenses 
            WHERE activation_code = ? AND machine_id = ? AND status = 'active'
        """, (activation_code, machine_id))
        
        existing_license = cursor.fetchone()
        
        if existing_license:
            # 更新最后检查时间
            cursor.execute("""
                UPDATE active_licenses 
                SET last_check = CURRENT_TIMESTAMP
                WHERE activation_code = ? AND machine_id = ?
            """, (activation_code, machine_id))
            conn.commit()
            conn.close()
            
            return jsonify({
                'success': True,
                'message': '许可证验证成功',
                'license_info': {
                    'activation_code': activation_code,
                    'license_type': license_type,
                    'expires_at': expires_at,
                    'max_devices': max_devices,
                    'current_devices': active_count
                }
            })
        
        elif active_count >= max_devices:
            conn.close()
            return jsonify({
                'success': False,
                'error': 'device_limit_exceeded',
                'message': f'设备数量已达上限 ({max_devices})'
            }), 403
        
        # 激活新设备
        cursor.execute("""
            INSERT INTO active_licenses 
            (activation_code, machine_id, machine_info, status)
            VALUES (?, ?, ?, 'active')
        """, (activation_code, machine_id, json.dumps(machine_info)))
        
        # 更新激活码状态为已使用
        cursor.execute("""
            UPDATE activation_codes 
            SET status = 'used', updated_at = CURRENT_TIMESTAMP
            WHERE code = ?
        """, (activation_code,))
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': '激活成功',
            'license_info': {
                'activation_code': activation_code,
                'license_type': license_type,
                'expires_at': expires_at,
                'max_devices': max_devices,
                'current_devices': active_count + 1
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'server_error',
            'message': f'服务器内部错误: {str(e)}'
        }), 500

@app.route('/api/license/status', methods=['POST'])
@require_api_key
@limiter.limit("30 per minute")
def check_license_status():
    """检查许可证状态"""
    try:
        data = request.get_json()
        activation_code = data.get('activation_code', '').strip()
        machine_id = data.get('machine_id', '').strip()
        
        if not activation_code or not machine_id:
            return jsonify({
                'success': False,
                'error': 'missing_parameters',
                'message': '缺少必要参数'
            }), 400
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 查询许可证信息
        cursor.execute("""
            SELECT al.status, ac.license_type, ac.expires_at, ac.max_devices, ac.status as code_status
            FROM active_licenses al
            JOIN activation_codes ac ON al.activation_code = ac.code
            WHERE al.activation_code = ? AND al.machine_id = ?
        """, (activation_code, machine_id))
        
        license_info = cursor.fetchone()
        
        if not license_info:
            conn.close()
            return jsonify({
                'success': False,
                'error': 'license_not_found',
                'message': '许可证不存在'
            }), 404
        
        license_status, license_type, expires_at, max_devices, code_status = license_info
        
        # 检查激活码状态
        if code_status not in ['used', 'active']:
            conn.close()
            return jsonify({
                'success': False,
                'error': 'code_disabled',
                'message': '激活码已被禁用'
            }), 403
        
        # 检查过期时间
        expire_time = datetime.fromisoformat(expires_at)
        if datetime.now() > expire_time:
            cursor.execute("""
                UPDATE active_licenses 
                SET status = 'expired'
                WHERE activation_code = ? AND machine_id = ?
            """, (activation_code, machine_id))
            conn.commit()
            conn.close()
            return jsonify({
                'success': False,
                'error': 'license_expired',
                'message': '许可证已过期'
            }), 403
        
        # 更新最后检查时间
        cursor.execute("""
            UPDATE active_licenses 
            SET last_check = CURRENT_TIMESTAMP
            WHERE activation_code = ? AND machine_id = ?
        """, (activation_code, machine_id))
        
        # 获取当前设备数量
        cursor.execute("""
            SELECT COUNT(*) FROM active_licenses 
            WHERE activation_code = ? AND status = 'active'
        """, (activation_code,))
        current_devices = cursor.fetchone()[0]
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': '许可证有效',
            'license_info': {
                'activation_code': activation_code,
                'license_type': license_type,
                'expires_at': expires_at,
                'max_devices': max_devices,
                'current_devices': current_devices,
                'status': license_status
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'server_error',
            'message': f'服务器内部错误: {str(e)}'
        }), 500

# ==================== 管理界面路由 ====================

@app.route('/admin/login', methods=['GET', 'POST'])
def admin_login():
    """管理员登录"""
    if request.method == 'GET':
        return '''
        <!DOCTYPE html>
        <html>
        <head>
            <title>激活码管理系统 - 登录</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; margin: 50px; }
                .login-form { max-width: 400px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; }
                input { width: 100%; padding: 10px; margin: 10px 0; }
                button { width: 100%; padding: 10px; background: #007bff; color: white; border: none; }
            </style>
        </head>
        <body>
            <div class="login-form">
                <h2>激活码管理系统</h2>
                <form method="post">
                    <input type="text" name="username" placeholder="用户名" required>
                    <input type="password" name="password" placeholder="密码" required>
                    <button type="submit">登录</button>
                </form>
                <p><small>默认用户名: admin, 密码: admin123</small></p>
            </div>
        </body>
        </html>
        '''
    
    username = request.form.get('username')
    password = request.form.get('password')
    
    if username == 'admin' and password == 'admin123':
        session['admin_logged_in'] = True
        return redirect(url_for('admin_dashboard'))
    else:
        return '用户名或密码错误', 401

@app.route('/admin')
@app.route('/admin/dashboard')
@require_admin_auth
def admin_dashboard():
    """管理面板首页"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取统计信息
        cursor.execute("""
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active,
                SUM(CASE WHEN status = 'used' THEN 1 ELSE 0 END) as used,
                SUM(CASE WHEN status = 'expired' THEN 1 ELSE 0 END) as expired
            FROM activation_codes
        """)
        stats = cursor.fetchone()
        
        # 获取最近的激活码
        cursor.execute("""
            SELECT code, license_type, created_at, expires_at, status
            FROM activation_codes
            ORDER BY created_at DESC
            LIMIT 10
        """)
        recent_codes = cursor.fetchall()
        
        conn.close()
        
        html = f'''
        <!DOCTYPE html>
        <html>
        <head>
            <title>激活码管理系统</title>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background: #f8f9fa; padding: 20px; margin-bottom: 20px; }}
                .stats {{ display: flex; gap: 20px; margin-bottom: 20px; }}
                .stat-card {{ background: white; padding: 20px; border: 1px solid #ddd; flex: 1; }}
                .stat-number {{ font-size: 2em; font-weight: bold; color: #007bff; }}
                table {{ width: 100%; border-collapse: collapse; }}
                th, td {{ padding: 10px; border: 1px solid #ddd; text-align: left; }}
                th {{ background: #f8f9fa; }}
                .btn {{ padding: 10px 20px; background: #007bff; color: white; text-decoration: none; margin: 5px; }}
                .status-active {{ color: green; }}
                .status-used {{ color: blue; }}
                .status-expired {{ color: red; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🔑 激活码管理系统</h1>
                <a href="/admin/codes" class="btn">管理激活码</a>
                <a href="/admin/licenses" class="btn">活跃许可证</a>
                <a href="/admin/logout" class="btn">退出登录</a>
            </div>
            
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number">{stats[0] or 0}</div>
                    <div>总激活码</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{stats[1] or 0}</div>
                    <div>活跃</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{stats[2] or 0}</div>
                    <div>已使用</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{stats[3] or 0}</div>
                    <div>已过期</div>
                </div>
            </div>
            
            <h3>最近的激活码</h3>
            <table>
                <tr>
                    <th>激活码</th>
                    <th>类型</th>
                    <th>创建时间</th>
                    <th>过期时间</th>
                    <th>状态</th>
                </tr>
        '''
        
        for code in recent_codes:
            status_class = f"status-{code[4]}"
            html += f'''
                <tr>
                    <td><code>{code[0]}</code></td>
                    <td>{code[1]}</td>
                    <td>{code[2]}</td>
                    <td>{code[3]}</td>
                    <td class="{status_class}">{code[4]}</td>
                </tr>
            '''
        
        html += '''
            </table>
        </body>
        </html>
        '''
        
        return html
        
    except Exception as e:
        return f'错误: {str(e)}', 500

@app.route('/admin/codes')
@require_admin_auth
def admin_codes():
    """激活码管理页面"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 获取所有激活码
        cursor.execute("""
            SELECT code, license_type, max_devices, created_at, expires_at, status, notes
            FROM activation_codes
            ORDER BY created_at DESC
        """)
        codes = cursor.fetchall()

        conn.close()

        html = '''
        <!DOCTYPE html>
        <html>
        <head>
            <title>激活码管理</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { background: #f8f9fa; padding: 20px; margin-bottom: 20px; }
                .btn { padding: 10px 20px; background: #007bff; color: white; text-decoration: none; margin: 5px; border: none; cursor: pointer; }
                .btn-success { background: #28a745; }
                .btn-warning { background: #ffc107; color: black; }
                .btn-danger { background: #dc3545; }
                .btn-secondary { background: #6c757d; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                th, td { padding: 10px; border: 1px solid #ddd; text-align: left; }
                th { background: #f8f9fa; }
                .status-active { color: green; font-weight: bold; }
                .status-used { color: blue; font-weight: bold; }
                .status-expired { color: red; font-weight: bold; }
                .status-disabled { color: gray; font-weight: bold; }
                .code-display { font-family: monospace; font-weight: bold; }
                .generate-form { background: #f8f9fa; padding: 20px; margin-bottom: 20px; border-radius: 5px; }
                .form-group { margin-bottom: 15px; }
                label { display: block; margin-bottom: 5px; font-weight: bold; }
                input, select, textarea { width: 200px; padding: 8px; border: 1px solid #ddd; }
                .form-row { display: flex; gap: 20px; align-items: end; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🔑 激活码管理</h1>
                <a href="/admin" class="btn btn-secondary">返回仪表板</a>
                <a href="/admin/logout" class="btn btn-danger">退出登录</a>
            </div>

            <div class="generate-form">
                <h3>生成新激活码</h3>
                <form method="post" action="/admin/generate">
                    <div class="form-row">
                        <div class="form-group">
                            <label>数量:</label>
                            <input type="number" name="count" value="1" min="1" max="10" required>
                        </div>
                        <div class="form-group">
                            <label>类型:</label>
                            <select name="license_type" required>
                                <option value="trial">试用版</option>
                                <option value="standard">标准版</option>
                                <option value="professional">专业版</option>
                                <option value="enterprise">企业版</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>最大设备数:</label>
                            <input type="number" name="max_devices" value="1" min="1" max="100" required>
                        </div>
                        <div class="form-group">
                            <label>有效期(天):</label>
                            <input type="number" name="validity_days" value="365" min="1" max="3650" required>
                        </div>
                        <div class="form-group">
                            <button type="submit" class="btn btn-success">生成</button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>备注:</label>
                        <textarea name="notes" rows="2" style="width: 400px;"></textarea>
                    </div>
                </form>
            </div>

            <h3>激活码列表</h3>
            <table>
                <tr>
                    <th>激活码</th>
                    <th>类型</th>
                    <th>最大设备</th>
                    <th>创建时间</th>
                    <th>过期时间</th>
                    <th>状态</th>
                    <th>备注</th>
                    <th>操作</th>
                </tr>
        '''

        for code in codes:
            status_class = f"status-{code[5]}"
            html += f'''
                <tr>
                    <td class="code-display">{code[0]}</td>
                    <td>{code[1]}</td>
                    <td>{code[2]}</td>
                    <td>{code[3]}</td>
                    <td>{code[4]}</td>
                    <td class="{status_class}">{code[5]}</td>
                    <td>{code[6] or ''}</td>
                    <td>
                        <form method="post" action="/admin/update_status" style="display: inline;">
                            <input type="hidden" name="code" value="{code[0]}">
                            <select name="status" onchange="this.form.submit()">
                                <option value="">修改状态</option>
                                <option value="active">启用</option>
                                <option value="disabled">禁用</option>
                                <option value="expired">过期</option>
                            </select>
                        </form>
                    </td>
                </tr>
            '''

        html += '''
            </table>
        </body>
        </html>
        '''

        return html

    except Exception as e:
        return f'错误: {str(e)}', 500

@app.route('/admin/generate', methods=['POST'])
@require_admin_auth
def admin_generate():
    """生成激活码"""
    try:
        count = int(request.form.get('count', 1))
        license_type = request.form.get('license_type', 'standard')
        max_devices = int(request.form.get('max_devices', 1))
        validity_days = int(request.form.get('validity_days', 365))
        notes = request.form.get('notes', '')

        if count > 10:
            return '单次最多生成10个激活码', 400

        conn = get_db_connection()
        cursor = conn.cursor()

        generated_codes = []
        expires_at = (datetime.now() + timedelta(days=validity_days)).strftime('%Y-%m-%d %H:%M:%S')

        for _ in range(count):
            # 生成激活码
            code = generate_activation_code()

            cursor.execute("""
                INSERT INTO activation_codes
                (code, license_type, max_devices, expires_at, status, notes)
                VALUES (?, ?, ?, ?, 'active', ?)
            """, (code, license_type, max_devices, expires_at, notes))

            generated_codes.append(code)

        conn.commit()
        conn.close()

        # 返回生成结果页面
        html = f'''
        <!DOCTYPE html>
        <html>
        <head>
            <title>激活码生成成功</title>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .success {{ background: #d4edda; color: #155724; padding: 20px; border-radius: 5px; margin-bottom: 20px; }}
                .code-list {{ background: #f8f9fa; padding: 20px; border-radius: 5px; }}
                .code {{ font-family: monospace; font-size: 1.2em; font-weight: bold; margin: 10px 0; }}
                .btn {{ padding: 10px 20px; background: #007bff; color: white; text-decoration: none; margin: 5px; }}
            </style>
        </head>
        <body>
            <div class="success">
                <h2>✅ 激活码生成成功!</h2>
                <p>成功生成 {count} 个激活码</p>
            </div>

            <div class="code-list">
                <h3>生成的激活码:</h3>
        '''

        for code in generated_codes:
            html += f'<div class="code">{code}</div>'

        html += f'''
            </div>

            <p>
                <strong>配置信息:</strong><br>
                类型: {license_type}<br>
                最大设备数: {max_devices}<br>
                有效期: {validity_days} 天<br>
                过期时间: {expires_at}<br>
                备注: {notes or '无'}
            </p>

            <a href="/admin/codes" class="btn">返回激活码管理</a>
            <a href="/admin" class="btn">返回仪表板</a>
        </body>
        </html>
        '''

        return html

    except Exception as e:
        return f'生成失败: {str(e)}', 500

@app.route('/admin/update_status', methods=['POST'])
@require_admin_auth
def admin_update_status():
    """更新激活码状态"""
    try:
        code = request.form.get('code')
        new_status = request.form.get('status')

        if not code or not new_status:
            return '参数错误', 400

        if new_status not in ['active', 'disabled', 'expired']:
            return '无效状态', 400

        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            UPDATE activation_codes
            SET status = ?, updated_at = CURRENT_TIMESTAMP
            WHERE code = ?
        """, (new_status, code))

        conn.commit()
        conn.close()

        return redirect(url_for('admin_codes'))

    except Exception as e:
        return f'更新失败: {str(e)}', 500

def generate_activation_code():
    """生成激活码"""
    # 生成16位随机字符串
    random_part = secrets.token_hex(8).upper()

    # 格式化为 XXXX-XXXX-XXXX-XXXX
    formatted_code = '-'.join([random_part[i:i+4] for i in range(0, 16, 4)])

    return formatted_code

@app.route('/admin/licenses')
@require_admin_auth
def admin_licenses():
    """活跃许可证管理页面"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 获取活跃许可证信息
        cursor.execute("""
            SELECT al.activation_code, al.machine_id, al.activated_at, al.last_check, al.status,
                   ac.license_type, ac.max_devices, ac.expires_at
            FROM active_licenses al
            JOIN activation_codes ac ON al.activation_code = ac.code
            ORDER BY al.activated_at DESC
        """)
        licenses = cursor.fetchall()

        conn.close()

        html = '''
        <!DOCTYPE html>
        <html>
        <head>
            <title>活跃许可证管理</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { background: #f8f9fa; padding: 20px; margin-bottom: 20px; }
                .btn { padding: 10px 20px; background: #007bff; color: white; text-decoration: none; margin: 5px; }
                .btn-secondary { background: #6c757d; }
                .btn-danger { background: #dc3545; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                th, td { padding: 10px; border: 1px solid #ddd; text-align: left; }
                th { background: #f8f9fa; }
                .code-display { font-family: monospace; font-weight: bold; }
                .machine-id { font-family: monospace; font-size: 0.9em; }
                .status-active { color: green; font-weight: bold; }
                .status-expired { color: red; font-weight: bold; }
                .status-deactivated { color: gray; font-weight: bold; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🛡️ 活跃许可证管理</h1>
                <a href="/admin" class="btn btn-secondary">返回仪表板</a>
                <a href="/admin/codes" class="btn btn-secondary">激活码管理</a>
                <a href="/admin/logout" class="btn btn-danger">退出登录</a>
            </div>

            <h3>活跃许可证列表</h3>
            <table>
                <tr>
                    <th>激活码</th>
                    <th>机器ID</th>
                    <th>许可证类型</th>
                    <th>最大设备数</th>
                    <th>激活时间</th>
                    <th>最后检查</th>
                    <th>过期时间</th>
                    <th>状态</th>
                </tr>
        '''

        for license_info in licenses:
            status_class = f"status-{license_info[4]}"
            html += f'''
                <tr>
                    <td class="code-display">{license_info[0]}</td>
                    <td class="machine-id">{license_info[1][:16]}...</td>
                    <td>{license_info[5]}</td>
                    <td>{license_info[6]}</td>
                    <td>{license_info[2]}</td>
                    <td>{license_info[3]}</td>
                    <td>{license_info[7]}</td>
                    <td class="{status_class}">{license_info[4]}</td>
                </tr>
            '''

        if not licenses:
            html += '''
                <tr>
                    <td colspan="8" style="text-align: center; color: #666;">
                        暂无活跃许可证
                    </td>
                </tr>
            '''

        html += '''
            </table>

            <div style="margin-top: 20px; padding: 15px; background: #e9ecef; border-radius: 5px;">
                <h4>说明:</h4>
                <ul>
                    <li><strong>激活码</strong>: 用于激活的唯一标识</li>
                    <li><strong>机器ID</strong>: 基于硬件信息生成的设备唯一标识</li>
                    <li><strong>最后检查</strong>: 客户端最后一次验证许可证的时间</li>
                    <li><strong>状态</strong>: active(活跃), expired(过期), deactivated(已停用)</li>
                </ul>
            </div>
        </body>
        </html>
        '''

        return html

    except Exception as e:
        return f'错误: {str(e)}', 500

@app.route('/admin/logout')
def admin_logout():
    """管理员登出"""
    session.pop('admin_logged_in', None)
    return redirect(url_for('admin_login'))

# ==================== 主程序 ====================

if __name__ == '__main__':
    # 确保数据库目录存在
    os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)
    
    print("🚀 激活码管理服务端启动中...")
    print(f"📊 管理界面: http://localhost:5000/admin")
    print(f"🔑 API接口: http://localhost:5000/api/license/")
    print(f"👤 默认登录: admin / admin123")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
