import os
import re
import shutil
import configparser
import json
import codecs
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from PIL import Image
import subprocess
from pywinauto.application import Application
import pywinauto.timings

# Windows API常量
SW_SHOWMINIMIZED = 6

def get_merge_rule(filename):
    """获取文件的合并规则"""
    config = configparser.ConfigParser()
    # 获取当前脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    config_path = os.path.join(current_dir, 'merge_rules.ini')
    config.read(config_path, encoding='utf-8')

    # 去掉文件名中的+号再进行匹配
    filename_no_plus = filename.replace('+', '')

    # 优先判断特定的XML文件
    if filename_no_plus.lower() == 'task.xml':
        return 9  # task.xml使用规则9
    elif filename_no_plus.lower() == 'replaceaction.xml':
        return 11  # replaceaction.xml使用规则11

    if not config.has_section('Rules'):
        return 1  # 默认使用规则1

    rule1_files = [f.strip() for f in config.get('Rules', 'rule1_files', fallback='').split(',')]
    rule2_files = [f.strip() for f in config.get('Rules', 'rule2_files', fallback='').split(',')]
    rule3_files = [f.strip() for f in config.get('Rules', 'rule3_files', fallback='').split(',')]
    rule4_files = [f.strip() for f in config.get('Rules', 'rule4_files', fallback='').split(',')]
    rule5_files = [f.strip() for f in config.get('Rules', 'rule5_files', fallback='').split(',')]
    rule6_files = [f.strip() for f in config.get('Rules', 'rule6_files', fallback='').split(',')]
    rule7_files = [f.strip() for f in config.get('Rules', 'rule7_files', fallback='').split(',')]
    rule8_files = [f.strip() for f in config.get('Rules', 'rule8_files', fallback='').split(',')]
    rule9_files = [f.strip() for f in config.get('Rules', 'rule9_files', fallback='').split(',')]
    rule10_files = [f.strip() for f in config.get('Rules', 'rule10_files', fallback='').split(',') if f.strip()]  # 添加strip()检查
    rule11_files = [f.strip() for f in config.get('Rules', 'rule11_files', fallback='').split(',')]
    rule12_files = [f.strip() for f in config.get('Rules', 'rule12_files', fallback='').split(',')]
    rule13_files = [f.strip() for f in config.get('Rules', 'rule13_files', fallback='').split(',')]
    rule14_files = [f.strip() for f in config.get('Rules', 'rule14_files', fallback='').split(',')]
    rule15_files = [f.strip() for f in config.get('Rules', 'rule15_files', fallback='').split(',')]
    rule16_files = [f.strip() for f in config.get('Rules', 'rule16_files', fallback='').split(',')]

    if filename_no_plus in rule1_files:
        return 1
    elif filename_no_plus in rule2_files:
        return 2
    elif filename_no_plus in rule3_files:
        return 3
    elif filename_no_plus in rule4_files:
        return 4
    elif filename_no_plus in rule5_files:
        return 5
    elif filename_no_plus in rule6_files:
        return 6
    elif filename_no_plus in rule7_files:
        return 7
    elif filename_no_plus in rule8_files:
        return 8
    elif filename_no_plus in rule9_files:
        return 9
    elif filename_no_plus in rule10_files:
        return 10
    elif filename_no_plus in rule11_files:
        return 11
    elif filename_no_plus in rule12_files:
        return 12
    elif filename_no_plus in rule13_files:
        return 13
    elif filename_no_plus in rule14_files:
        return 14
    elif filename_no_plus in rule15_files:
        return 15
    elif filename_no_plus in rule16_files:
        return 16
    elif filename_no_plus.endswith('.xml'):  # 其他XML文件使用规则12
        return 12
    return 1  # 默认使用规则1


def find_original_file(source_dir, original_file, ini_x=None):
    """在原始资源目录及其子目录中查找文件"""
    if ini_x:
        # 对于 ini_x，在指定的 ini_x 目录下查找
        ini_x_dir = os.path.join(source_dir, 'ini', ini_x)
        for root, _, files in os.walk(ini_x_dir):
            if original_file in files:
                return os.path.join(root, original_file)
    else:
        # 对于普通 ini 文件，在整个 ini 目录下查找
        for root, _, files in os.walk(source_dir):
            if original_file in files:
                return os.path.join(root, original_file)
    return None


def sort_key_rule2(items):
    """规则2的排序键：按第一列的ID排序"""
    key, content = items
    try:
        return int(key)
    except (ValueError, IndexError):
        return float('inf')


def sort_key_rule3(x):
    """规则3的排序键：先按数字长度排序，长度相同时按数字大小排序"""
    num = x[0]  # 获取键（数字字符串）
    try:
        return (len(num), int(num))  # 返回元组(长度, 数值)
    except ValueError:
        return (float('inf'), float('inf'))


def sort_key_rule4(items):
    """规则4的排序键：不排序"""
    return 0  # 保持原有顺序


def sort_key_rule5(x):
    """规则5的排序键：先按数字位数，再按数字大小排序"""
    key = x[0]  # 获取键
    try:
        # 提取数字部分
        num_match = re.search(r'\d+', key)
        if num_match:
            num = num_match.group()
            return (len(num), int(num))  # 返回元组(长度, 数值)
        return (0, 0)  # 如果没有数字，返回默认值
    except ValueError:
        return (0, 0)


def sort_key_rule6(items):
    """规则6的排序键：按段落标题排序，WDCommon优先"""
    key = items[0]  # 获取段落标题
    if key == 'WDCommon':
        return [(-1, '')]

    parts = re.findall(r'([A-Za-z]+|\d+)', key)
    result = []
    for part in parts:
        if part.isdigit():
            result.append((0, int(part)))
        else:
            result.append((1, part.lower()))
    return result or [(1, key)]


def sort_key_rule8(items):
    """规则8的排序键：按=号前的key排序"""
    key = items[0]  # 获取键
    try:
        # 提取=号前的部分作为排序键
        return key
    except (ValueError, IndexError):
        return ''


def sort_key_rule10(items):
    """规则10的排序键：按第一个数字排序"""
    key = items[0]  # 获取键
    try:
        # 如果是空格分隔的数字序列，取第一个数字
        if ' ' in key:
            first_num = key.split()[0]
            if first_num.isdigit():
                return int(first_num)

        # 提取第一个数字序列
        num_match = re.match(r'\d+', key)
        if num_match:
            return int(num_match.group())
        return float('inf')  # 如果没有找到数字，放到最后
    except (ValueError, IndexError):
        return float('inf')


def sort_key_rule16(items):
    """规则16的排序键：按第一列ID排序（类似规则2）"""
    key, content = items
    try:
        return int(key.split()[0])
    except (ValueError, IndexError):
        return float('inf')

def merge_ini_rule14(source_path, plus_path, output_path, is_minus=False):
    """规则14的合并逻辑（基于原整合str类.py）"""
    try:
        # 直接调用原始整合函数
        merge_ini_files(source_path, plus_path, output_path, is_minus)
        return True
    except Exception as e:
        print(f"规则14合并失败: {str(e)}")
        return False
def merge_ini_rule15(source_path, plus_path, output_path):
    """规则15的合并逻辑（基于原整合军团图鉴.py）"""
    try:
        # 直接调用原始整合函数
        merge_syntotempillar_files(source_path, plus_path, output_path)
        return True
    except Exception as e:
        print(f"规则15合并失败: {str(e)}")
        return False

def sort_content(content_dict, rule_type, filename):
    """根据不同规则对内容进行排序"""
    if rule_type == 1:
        # 规则1：确保key的唯一性
        unique_dict = {}
        for key, content in content_dict.items():
            if key not in unique_dict:
                unique_dict[key] = content
        return unique_dict

    elif rule_type == 2:
        # 规则2：按第一列的ID排序，确保ID唯一
        unique_dict = {}
        for key, content in sorted(content_dict.items(), key=sort_key_rule2):
            if key not in unique_dict:
                if isinstance(content, list):
                    content = [line if line.endswith('\n') else line + '\n' for line in content]
                unique_dict[key] = content
        return unique_dict

    elif rule_type == 3:
        # 规则3：先按数字长度排序，长度相同时按数字大小排序
        unique_dict = {}
        for key, content in sorted(content_dict.items(), key=sort_key_rule3):
            if key not in unique_dict:
                if isinstance(content, list):
                    if content and not content[-1].endswith('\n'):
                        content[-1] += '\n'  # 确保最后一行换行
                    unique_dict[key] = content
        return unique_dict

    elif rule_type == 4:
        # 规则4：不排序，但确保key唯一
        unique_dict = {}
        for key, content in sorted(content_dict.items(), key=sort_key_rule4):
            if key not in unique_dict:
                if isinstance(content, list):
                    if content and not content[-1].endswith('\n'):
                        content[-1] += '\n'  # 确保最后一行换行
                    unique_dict[key] = content
        return unique_dict

    elif rule_type == 5:
        # 规则5：先按数字位数，再按数字大小排序
        unique_dict = {}
        for key, content in sorted(content_dict.items(), key=sort_key_rule5):
            if key not in unique_dict:
                if isinstance(content, list):
                    if content and not content[-1].endswith('\n'):
                        content[-1] += '\n'  # 确保最后一行换行
                    unique_dict[key] = content
        return unique_dict

    elif rule_type == 6:
        # 规则6：与规则4类似，保留注释并按段落标题排序
        unique_dict = {}
        for key, content in sorted(content_dict.items(), key=sort_key_rule6):
            if key not in unique_dict:
                if isinstance(content, list):
                    if content and not content[-1].endswith('\n'):
                        content[-1] += '\n'  # 确保最后一行换行
                    unique_dict[key] = content
        return unique_dict

    elif rule_type == 8:
        # 规则8：每个段落独立处理title查重，保留最后出现的内容
        result_dict = {}
        for section, content in content_dict.items():
            if isinstance(content, list):
                # 用于记录当前段落中每个key的最新内容
                title_dict = {}
                filtered_content = []
                
                for line in content:
                    if '=' in line:
                        key = line.split('=')[0].strip()
                        # 更新为最新的内容
                        title_dict[key] = line
                    else:
                        # 保留不包含=号的行（如段落标题）
                        filtered_content.append(line)
                
                # 将所有最新的内容添加到结果中
                for line in title_dict.values():
                    filtered_content.append(line)
                
                # 确保最后一行换行
                if filtered_content and not filtered_content[-1].endswith('\n'):
                    filtered_content[-1] += '\n'
                    
                result_dict[section] = filtered_content
            else:
                result_dict[section] = content
        return result_dict

    elif rule_type == 10:
        # 规则10：按第一个数字排序，但保留注释的位置
        sorted_dict = {}
        number_keys = []
        comment_keys = []
        
        # 分离数字键和注释键
        for key in content_dict.keys():
            if key.startswith('comment_'):
                comment_keys.append(key)
            else:
                number_keys.append(key)
        
        # 对数字键进行排序
        for key in sorted(number_keys, key=lambda x: int(x)):
            sorted_dict[key] = content_dict[key]
            
        # 保持注释的原始顺序
        for key in comment_keys:
            sorted_dict[key] = content_dict[key]
        
        # 确保最后一行换行
        if sorted_dict and not list(sorted_dict.values())[-1][-1].endswith('\n'):
            list(sorted_dict.values())[-1][-1] += '\n'
        
        return sorted_dict

    elif rule_type == 16:
        # 规则16：closetitem.txt文件，按第一列ID排序，确保ID唯一
        unique_dict = {}
        for key, content in sorted(content_dict.items(), key=sort_key_rule16):
            if key not in unique_dict:
                if isinstance(content, list):
                    content = [line if line.endswith('\n') else line + '\n' for line in content]
                unique_dict[key] = content
        return unique_dict

    return content_dict


def process_rule4(line, current_key, current_content, current_comment, temp_dict):
    """处理规则4的逻辑：处理注释和段落"""
    if line.startswith('//'):
        # 保存当前注释
        current_comment = line + '\n'
        return current_comment, current_key, current_content, temp_dict
    
    match = re.match(r'\[(.*?)\]', line)
    if match:
        if current_key:
            if not current_content[-1].endswith('\n'):
                current_content[-1] += '\n'
            current_content.append('\n')  # 在段落末尾添加空行
            # 如果是重复的标题，保留最后的段落内容
            temp_dict[current_key] = current_content

        # 只使用标题作为键
        current_key = match.group(1)
        current_content = []
        if current_comment:  # 添加注释作为段落的一部分
            current_content.append(current_comment)
        current_content.append(line + '\n')
        current_comment = None  # 重置注释
    elif current_key:
        current_content.append(line + '\n')
    
    return current_comment, current_key, current_content, temp_dict


def process_files(file_path, content_dict, rule_type):
    """读取文件内容"""
    encodings = ['utf-8', 'gbk', 'gb2312', 'gb18030']

    for encoding in encodings:
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                current_key = None
                current_content = []
                header_lines = []  # 用于存储文件开头的行
                is_file_start = True  # 标记是否在文件开头
                current_comment = None  # 初始化段落注释变量
                line_count = 0  # 用于txt文件的行计数
                temp_dict = {}  # 临时字典，用于存储所有内容

                # 如果是原始文件，先读取所有内容
                is_plus_file = '+' in file_path or '-' in file_path
                
                for line in f:
                    original_line = line
                    line = line.strip()

                    # 对于非规则4的+/-文件，忽略所有注释
                    if is_plus_file and rule_type != 4 and (line.startswith('//') or line.startswith(';')):
                        continue

                    # 规则10：只处理数字开头的行
                    if rule_type == 10:
                        # 如果是原始文件，保留所有内容
                        if not is_plus_file:
                            if line:  # 跳过空行
                                if line[0].isdigit():
                                    # 提取第一个数字作为键
                                    first_num = line.split()[0]
                                    if first_num.isdigit():
                                        temp_dict[first_num] = [original_line]
                                else:
                                    # 非数字开头的行（如注释）保存为特殊键
                                    temp_dict[f'comment_{len(temp_dict)}'] = [original_line]
                        else:
                            # 如果是+文件，只处理数字开头的行
                            if line and line[0].isdigit():
                                first_num = line.split()[0]
                                if first_num.isdigit():
                                    temp_dict[first_num] = [original_line]
                        continue

                    # 基础规则：处理文件开头的内容
                    if is_file_start:
                        if file_path.lower().endswith(('.txt', '.csv')):
                            # 对于txt和csv文件，只有原始资源才保存前3行
                            if not is_plus_file and line_count < 3:
                                header_lines.append(original_line)
                                line_count += 1
                                continue
                            else:
                                is_file_start = False
                        else:
                            # 对于其他文件，只有原始资源才保存开头的注释行
                            if not is_plus_file and line.startswith('//'):
                                header_lines.append(original_line)
                                continue
                            else:
                                is_file_start = False

                    if not line:  # 跳过空行
                        continue

                    # 规则1和规则5：按[xxx]处理
                    if rule_type == 1 or rule_type == 5:
                        match = re.match(r'\[(.*?)\]', line)
                        if match:
                            if current_key:
                                if not current_content[-1].endswith('\n'):
                                    current_content[-1] += '\n'
                                current_content.append('\n')  # 在段落末尾添加空行
                                temp_dict[current_key] = current_content
                            current_key = match.group(1)
                            current_content = [line + '\n']
                        elif current_key:
                            current_content.append(line + '\n')
                        continue

                    # 规则2：按行处理，第一列作为排序依据
                    if rule_type == 2:
                        # 如果还没有保存头部，先保存
                        if header_lines and '__header__' not in temp_dict:
                            temp_dict['__header__'] = header_lines

                        # 根据文件类型选择分隔符和处理方式
                        is_csv = file_path.lower().endswith('.csv')
                        separator = ',' if is_csv else '\t'
                        parts = line.split(separator)
                        if parts and parts[0].strip():  # 确保第一列不为空
                            try:
                                key = parts[0].strip()
                                # 始终使用最新的内容覆盖旧内容
                                if is_csv:
                                    temp_dict[key] = [line + '\n']
                                else:
                                    temp_dict[key] = [original_line]
                            except (IndexError, ValueError):
                                continue
                        continue

                    # 规则16：closetitem.txt文件，按行处理，整行作为key
                    if rule_type == 16:
                        # 如果还没有保存头部，先保存
                        if header_lines and '__header__' not in temp_dict:
                            temp_dict['__header__'] = header_lines

                        # 整行作为key，用于后续的第二位ID处理
                        if line.strip():  # 确保不是空行
                            temp_dict[line] = [original_line]
                        continue

                    # 规则3：按等号分割，数字作为排序依据
                    if rule_type == 3 and '=' in line:
                        # 如果还没有保存头部，先保存
                        if header_lines and '__header__' not in temp_dict:
                            temp_dict['__header__'] = header_lines

                        match = re.match(r'(\d+)\s*=', line)
                        if match:
                            key = match.group(1)
                            temp_dict[key] = [line + '\n']
                        continue

                    # 规则4：处理注释和段落
                    if rule_type == 4:
                        current_comment, current_key, current_content, temp_dict = process_rule4(
                            line, current_key, current_content, current_comment, temp_dict)
                        continue

                    # 规则6：处理注释和段落，并进行排序
                    if rule_type == 6:
                        if line.startswith('//'):
                            current_comment = line + '\n'
                            continue

                        match = re.match(r'\[(.*?)\]', line)
                        if match:
                            if current_key:
                                if not current_content[-1].endswith('\n'):
                                    current_content[-1] += '\n'
                                current_content.append('\n')  # 在段落末尾添加空行
                                temp_dict[current_key] = current_content
                            current_key = match.group(1)
                            current_content = []
                            if current_comment:  # 添加注释
                                current_content.append(current_comment)
                            current_content.append(line + '\n')
                            current_comment = None  # 重置注释
                        elif current_key:
                            current_content.append(line + '\n')
                        continue

                    # 规则8：按[xxx]段落和=号前的key处理
                    if rule_type == 8:
                        if line.startswith('['):
                            if current_key and current_content:
                                temp_dict[current_key] = current_content
                            current_key = line.strip('[]')
                            current_content = [line + '\n']
                        elif current_key:
                            if '=' in line:
                                key_part = line.split('=')[0].strip()
                                current_content.append(line + '\n')
                            else:
                                current_content.append(line + '\n')
                        continue

                # 处理最后一个段落
                if current_key and current_content:
                    if rule_type == 8:
                        temp_dict[current_key] = current_content
                    else:
                        if isinstance(current_content, list) and current_content:
                            if not current_content[-1].endswith('\n'):
                                current_content[-1] += '\n'
                            current_content.append('\n')  # 在最后一个段落末尾也添加空行
                        temp_dict[current_key] = current_content

                # 如果有头部内容但还没保存，保存它
                if header_lines and '__header__' not in temp_dict:
                    temp_dict['__header__'] = header_lines

                # 将临时字典中的内容转移到最终字典中
                content_dict.clear()
                content_dict.update(temp_dict)

                break  # 如果成功读取，跳出编码尝试循环

        except UnicodeDecodeError:
            if encoding == encodings[-1]:
                print(f'错误：无法读取文件 {file_path}，请检查文件编码')
                raise
            continue


def merge_and_save(source_path, plus_path, output_path):
    content_dict = {}
    filename = os.path.basename(source_path or plus_path)
    rule_type = get_merge_rule(filename)

    # 如果是规则7，检查文件名是否含有+号，如果有则不进行复制（防止错误文件被更新到客户端）
    if rule_type == 7 and os.path.exists(plus_path):
        plus_filename = os.path.basename(plus_path)
        # 规则7应该是整份替换，文件名中不应该包含+号（包括+开头的文件如+shop.ini）
        # 如果包含+号，说明文件命名错误，不进行复制以防止错误更新到客户端
        if '+' in plus_filename:
            print(f"[错误] 规则7文件 {plus_filename} 包含+号，配置未整合，请手动整合")
            return
        # 如果文件名正确（不含+号），则进行整份替换
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        shutil.copy2(plus_path, output_path)
        return

    # 如果是规则12（XML文件），使用XML合并逻辑
    if rule_type == 12:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        if not os.path.exists(source_path):
            # 如果原始文件不存在，直接复制+文件
            shutil.copy2(plus_path, output_path)
        else:
            # 合并XML文件
            success = merge_xml_files(source_path, plus_path, output_path)
            if not success:
                print(f"警告：XML文件合并失败，将使用+文件内容")
                shutil.copy2(plus_path, output_path)
        return

    # 如果是规则11（replaceaction.xml），使用特殊合并逻辑
    if rule_type == 11:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        if not os.path.exists(source_path):
            # 如果原始文件不存在，直接复制+文件
            shutil.copy2(plus_path, output_path)
        else:
            # 使用replace整合.py的合并逻辑
            success = update_action_with_ids(source_path, plus_path, output_path)
            if not success:
                print(f"警告：replaceaction.xml合并失败，将使用+文件内容")
                shutil.copy2(plus_path, output_path)
        return

    # 如果是规则9（task.xml），使用特殊合并逻辑
    if rule_type == 9:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        if not os.path.exists(source_path):
            shutil.copy2(plus_path, output_path)
        else:
            success = merge_task_xml(source_path, plus_path, output_path)
            if not success:
                print(f"警告：task.xml合并失败，将使用+文件内容")
                shutil.copy2(plus_path, output_path)
        return

    # 如果是规则13（itemcategory.ini格式），使用特殊合并逻辑
    if rule_type == 13:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        if not os.path.exists(source_path):
            # 如果原始文件不存在，直接复制+文件
            shutil.copy2(plus_path, output_path)
        else:
            # 使用 itemcategory 的合并逻辑
            success = merge_itemcategory_files(source_path, plus_path, output_path)
            if not success:
                print(f"警告：itemcategory格式文件合并失败，将使用+文件内容")
                shutil.copy2(plus_path, output_path)
        return
    # 新增规则14处理分支
    if rule_type == 14:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        if not os.path.exists(source_path):
            shutil.copy2(plus_path, output_path)
        else:
            success = merge_ini_rule14(source_path, plus_path, output_path)
            if not success:
                print(f"警告：规则14合并失败，将使用+文件内容")
                shutil.copy2(plus_path, output_path)
        return
    # 新增规则15处理分支
    if rule_type == 15:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        if not os.path.exists(source_path):
            shutil.copy2(plus_path, output_path)
        else:
            success = merge_syntotempillar_files(source_path, plus_path, output_path)
            if not success:
                print(f"警告：规则15合并失败，将使用+文件内容")
                shutil.copy2(plus_path, output_path)
        return


    # 检测原始文件的编码
    source_encoding = None
    if os.path.exists(source_path):
        for encoding in ['utf-8', 'gbk', 'gb2312', 'gb18030', 'ansi', 'cp936']:
            try:
                with open(source_path, 'r', encoding=encoding) as f:
                    f.read()
                source_encoding = encoding
                break
            except UnicodeDecodeError:
                continue

    # 如果没有原始文件，检测+ani/+ini文件的编码
    if not source_encoding and os.path.exists(plus_path):
        for encoding in ['utf-8', 'gbk', 'gb2312', 'gb18030', 'ansi', 'cp936']:
            try:
                with open(plus_path, 'r', encoding=encoding) as f:
                    f.read()
                source_encoding = encoding
                break
            except UnicodeDecodeError:
                continue

    # 如果都检测失败，默认使用gbk
    if not source_encoding:
        source_encoding = 'gbk'

    # 处理原始文件
    if os.path.exists(source_path):
        process_files(source_path, content_dict, rule_type)

    # 处理+ani/+ini中的文件
    if os.path.exists(plus_path):
        plus_content = {}
        process_files(plus_path, plus_content, rule_type)

        # 对于规则8，需要特殊处理合并逻辑
        if rule_type == 8:
            # 遍历+文件中的每个段落
            for section, plus_lines in plus_content.items():
                if section in content_dict:
                    # 如果段落已存在，将新内容（除了标题行）追加到原有内容后
                    for line in plus_lines:
                        # 跳过段落标题行
                        if not line.strip().startswith('['):
                            content_dict[section].append(line)
                else:
                    # 如果是新段落，直接添加
                    content_dict[section] = plus_lines
        elif rule_type == 16:
            # 规则16：closetitem.txt文件，基于第二位物品ID进行合并
            # 建立第二位ID到原始内容的映射
            source_by_second_id = {}
            max_first_id = 0

            for key, content in list(content_dict.items()):
                if key == '__header__':
                    continue
                parts = key.split()
                if len(parts) >= 2:
                    try:
                        first_id = int(parts[0])
                        second_id = int(parts[1])
                        max_first_id = max(max_first_id, first_id)
                        source_by_second_id[second_id] = key
                    except ValueError:
                        continue

            # 处理+文件中的内容
            for plus_key, plus_content_item in plus_content.items():
                if plus_key == '__header__':
                    continue
                parts = plus_key.split()
                if len(parts) >= 2:
                    try:
                        second_id = int(parts[1])

                        if second_id in source_by_second_id:
                            # 第二位ID已存在，替换原有内容，但保持原第一列ID
                            original_key = source_by_second_id[second_id]
                            original_parts = original_key.split()
                            original_first_id = original_parts[0]

                            # 更新内容中的第一列ID，保持原始分隔符格式
                            updated_content = []
                            for line in plus_content_item:
                                line_stripped = line.strip()
                                if line_stripped:
                                    # 找到第一个分隔符的位置
                                    first_sep_pos = -1
                                    for i, char in enumerate(line_stripped):
                                        if char in [' ', '\t']:
                                            first_sep_pos = i
                                            break

                                    if first_sep_pos > 0:
                                        # 替换第一列ID，保持原始分隔符
                                        rest_of_line = line_stripped[first_sep_pos:]
                                        updated_line = original_first_id + rest_of_line + '\n'
                                        updated_content.append(updated_line)
                                    else:
                                        updated_content.append(line)
                                else:
                                    updated_content.append(line)

                            # 直接替换原有内容
                            content_dict[original_key] = updated_content
                        else:
                            # 第二位ID不存在，新增，重新分配第一列ID
                            max_first_id += 1

                            # 更新内容中的第一列ID，保持原始分隔符格式
                            updated_content = []
                            for line in plus_content_item:
                                line_stripped = line.strip()
                                if line_stripped:
                                    # 找到第一个分隔符的位置
                                    first_sep_pos = -1
                                    for i, char in enumerate(line_stripped):
                                        if char in [' ', '\t']:
                                            first_sep_pos = i
                                            break

                                    if first_sep_pos > 0:
                                        # 替换第一列ID，保持原始分隔符
                                        rest_of_line = line_stripped[first_sep_pos:]
                                        updated_line = str(max_first_id) + rest_of_line + '\n'
                                        updated_content.append(updated_line)
                                    else:
                                        updated_content.append(line)
                                else:
                                    updated_content.append(line)

                            # 构建新的key，保持原始分隔符格式
                            # 找到plus_key中第一个分隔符的位置
                            first_sep_pos = -1
                            for i, char in enumerate(plus_key):
                                if char in [' ', '\t']:
                                    first_sep_pos = i
                                    break

                            if first_sep_pos > 0:
                                rest_of_key = plus_key[first_sep_pos:]
                                new_key = str(max_first_id) + rest_of_key
                            else:
                                new_key = str(max_first_id) + ' ' + ' '.join(parts[1:])

                            content_dict[new_key] = updated_content
                    except ValueError:
                        # 解析失败，直接添加
                        content_dict[plus_key] = plus_content_item
        else:
            # 其他规则保持原有的合并逻辑
            content_dict.update(plus_content)

    # 确保输出文件的目录存在
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    # 使用检测到的编码写入文件
    with open(output_path, 'w', encoding=source_encoding) as f:
        # 首先写入头部（如果有）
        if '__header__' in content_dict:
            f.writelines(content_dict['__header__'])
            del content_dict['__header__']  # 删除头部，不参与排序

        # 对剩余内容进行排序并写入
        sorted_content = sort_content(content_dict, rule_type, filename)
        for key, content in sorted_content.items():
            if isinstance(content, list):
                for line in content:
                    f.write(line)
            else:
                f.write(content)

    try:
        # 规则14处理
        if rule_type == 14:
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            if not os.path.exists(source_path):
                shutil.copy2(plus_path, output_path)
            else:
                success = merge_ini_rule14(source_path, plus_path, output_path)
                if not success:
                    print(f"警告：规则14合并失败，将使用+文件内容")
                    shutil.copy2(plus_path, output_path)
            return
    except Exception as e:
        print(f"处理规则{rule_type}时发生错误: {str(e)}")
        if os.path.exists(plus_path):
            shutil.copy2(plus_path, output_path)


def merge_files(source_dir, plus_dir, output_dir, file_type, minus_dir=None, ini_x=None):
    """
    合并原始资源和+文件夹中的文件

    Args:
        source_dir: 原始资源目录
        plus_dir: +ani或+ini文件夹目录（可以为None，表示只处理-文件夹）
        output_dir: 输出目录
        file_type: 文件类型('ani'或'ini')
        minus_dir: -ani或-ini文件夹目录（可选）
        ini_x: ini_x的名称（如'ini_1'），用于特殊处理ini_x类型的文件
    """
    # 清理输出目录中的对应文件夹，只在非ini_x的情况下清理
    if ini_x:
        output_type_dir = os.path.join(output_dir, file_type, ini_x)
    else:
        output_type_dir = os.path.join(output_dir, file_type)
        if os.path.exists(output_type_dir):
            print(f'清理输出目录: {output_type_dir}')
            shutil.rmtree(output_type_dir)

    # 创建临时目录
    temp_dir = None
    if minus_dir:
        temp_dir = os.path.join(output_dir, f'temp_{file_type}')
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
        os.makedirs(temp_dir)

    try:
        # 如果有-文件夹，先处理删除操作
        if minus_dir:
            process_minus_files(source_dir, minus_dir, temp_dir, output_dir, file_type, ini_x)
            source_dir = temp_dir

        # 如果没有+文件夹但有-文件夹，需要将处理后的结果复制到输出目录
        if not plus_dir and minus_dir:
            # 将临时目录的内容复制到输出目录
            if ini_x:
                temp_type_dir = os.path.join(temp_dir, file_type, ini_x)
                output_type_dir = os.path.join(output_dir, file_type, ini_x)
            else:
                temp_type_dir = os.path.join(temp_dir, file_type)
                output_type_dir = os.path.join(output_dir, file_type)

            if os.path.exists(temp_type_dir):
                os.makedirs(os.path.dirname(output_type_dir), exist_ok=True)
                if os.path.exists(output_type_dir):
                    shutil.rmtree(output_type_dir)
                shutil.copytree(temp_type_dir, output_type_dir)
                print(f'已将-{file_type}处理结果复制到输出目录')
            return

        # 遍历+文件夹（如果存在）
        if plus_dir:
            for root, _, files in os.walk(plus_dir):
                # 跳过mapdestination目录
                if file_type == 'ini' and os.path.basename(root).lower() in ['mapdestination', '+mapdestination']:
                    continue

                for file in files:
                    # 根据文件类型判断是否处理该文件
                    if (file_type == 'ani' and file.endswith(('.ani', '.size'))) or \
                            (file_type == 'ini' and file.endswith(('.ini', '.txt', '.csv', '.xml'))):

                        # 获取相对路径
                        rel_path = os.path.relpath(root, plus_dir)

                        # 去掉文件名中的"+"号来匹配原始资源文件
                        original_file = file.replace('+', '')

                        # 初始化source_file和output_file
                        source_file = None
                        output_file = None

                    # 对于ini类型的文件，在原始资源目录中递归查找
                    if file_type == 'ini':
                        if ini_x:
                            # 对于ini_x，使用特殊的路径结构
                            source_file = find_original_file(source_dir, original_file, ini_x)
                            if source_file:
                                # 获取相对于 ini_x 目录的路径
                                rel_output_path = os.path.relpath(os.path.dirname(source_file),
                                                            os.path.join(source_dir, 'ini', ini_x))
                                output_file = os.path.join(output_dir, 'ini', ini_x, rel_output_path, original_file)
                            else:
                                # 如果没找到原始文件，使用+ini_x中的目录结构
                                output_file = os.path.join(output_dir, 'ini', ini_x, rel_path, original_file)
                        else:
                            source_file = find_original_file(source_dir, original_file)
                            if source_file:
                                # 获取相对于原始资源ini目录的路径
                                rel_output_path = os.path.relpath(os.path.dirname(source_file),
                                                            os.path.join(source_dir, 'ini'))
                                output_file = os.path.join(output_dir, 'ini', rel_output_path, original_file)
                            else:
                                # 如果没找到原始文件，使用+ini中的目录结构
                                output_file = os.path.join(output_dir, 'ini', rel_path, original_file)
                    else:
                        source_file = os.path.join(source_dir, file_type, rel_path, original_file)
                        output_file = os.path.join(output_dir, file_type, rel_path, original_file)

                    plus_file = os.path.join(root, file)

                    print(f'处理文件：')
                    print(f'源文件：{source_file if source_file else "不存在"}')
                    print(f'+{file_type}文件：{plus_file}')
                    print(f'输出文件：{output_file}')

                    # 如果原始资源文件不存在，直接复制+ani/+ini文件
                    if not source_file or not os.path.exists(source_file):
                        os.makedirs(os.path.dirname(output_file), exist_ok=True)
                        shutil.copy2(plus_file, output_file)
                        print(f'[错误] 文件 {os.path.basename(plus_file)} 的原始资源不存在，直接复制+{file_type}文件')
                    else:
                        # 合并文件
                        merge_and_save(source_file, plus_file, output_file)
                        print(f'合并完成')
                    print('-------------------')

    finally:
        # 清理临时目录
        if temp_dir and os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)

def load_xml_config():
    """加载XML配置文件"""
    config_path = os.path.join(os.path.dirname(__file__), 'xml_config.json')
    try:
        if not os.path.exists(config_path):
            print(f"配置文件不存在: {config_path}")
            return {}
            
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
            
        # 处理配置，合并排序和不排序的配置
        flat_config = {}
        
        # 处理不排序的配置
        for tag, structures in config.get('no_sort', {}).items():
            flat_config[tag] = {
                'structures': [tuple(v) for v in structures],
                'sort_by_id': False
            }
            
        # 处理需要排序的配置
        for tag, structures in config.get('sort', {}).items():
            flat_config[tag] = {
                'structures': [tuple(v) for v in structures],
                'sort_by_id': True
            }
            
        return flat_config
            
    except json.JSONDecodeError as e:
        print(f"JSON格式错误: {str(e)}")
        return {}
    except Exception as e:
        print(f"加载配置文件失败: {str(e)}")
        return {}

def merge_xml_files(source_path, plus_path, output_path, is_minus=False):
    """合并两个XML文件的内容"""
    try:
        # 读取源文件内容
        source_content = ""
        if os.path.exists(source_path):
            with open(source_path, 'r', encoding='cp936') as f:
                source_content = f.read()
        
        # 读取+/-文件内容
        with open(plus_path, 'r', encoding='cp936') as f:
            plus_content = f.read()
            
        # 创建输出目录
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # 加载XML配置
        known_structures = load_xml_config()
        if not known_structures:
            raise ValueError("无法加载XML配置文件，请检查 xml_config.json 是否存在且格式正确")
        
        # 自动检测根标签
        for tag in known_structures.keys():
            if f'<{tag}>' in source_content:
                root_tag = tag
                break
        else:
            raise ValueError("不支持的XML文件类型")
            
        # 找到根标签的位置
        root_start = source_content.find(f"<{root_tag}>")
        root_end = source_content.rfind(f"</{root_tag}>")
        
        if root_start == -1 or root_end == -1:
            raise ValueError(f"在源文件中未找到完整的{root_tag}标签")
            
        # 获取模块标签和ID标签
        config = known_structures[root_tag]
        structures = config['structures']
        sort_by_id = config['sort_by_id']
        
        module_tag = None
        id_tag = None
        
        # 检查文件中包含哪种结构
        for mod_tag, id_tag_candidate in structures:
            if f'<{mod_tag}' in source_content or f'<{mod_tag}' in plus_content:
                module_tag = mod_tag
                id_tag = id_tag_candidate
                break
                
        if not module_tag:
            raise ValueError(f"在文件中未找到支持的模块标签")
        
        def get_indent(content, pos):
            """获取指定位置的缩进"""
            line_start = content.rfind('\n', 0, pos) + 1
            return content[line_start:pos]
        
        # 提取模块内容
        def extract_modules(content, tag_name, id_tag, source):
            modules = []
            current_pos = 0
            result = source
            
            while True:
                # 查找模块开始位置（考虑可能的 "-" 前缀）
                module_start = -1
                for pattern in [f"<{tag_name}", f"-<{tag_name}"]:
                    pos = content.find(pattern, current_pos)
                    if pos != -1:
                        module_start = pos
                        break
                
                if module_start == -1:
                    break
                    
                # 找到完整的模块结束位置
                module_end = content.find(f"</{tag_name}>", module_start)
                if module_end == -1:
                    break
                module_end += len(f"</{tag_name}>")
                
                # 获取原始内容和缩进
                module_content = content[module_start:module_end]
                indent = get_indent(content, module_start)
                
                # 提取ID
                if f'{id_tag}="' in module_content:  # WndID="xxx" 格式
                    id_start = module_content.find(f'{id_tag}="') + len(id_tag) + 2
                    id_end = module_content.find('"', id_start)
                    if id_start != -1 and id_end != -1:
                        module_id = module_content[id_start:id_end].strip()
                else:  # <TypeID>xxx</TypeID> 格式
                    id_start = module_content.find(f'<{id_tag}>') + len(id_tag) + 2
                    id_end = module_content.find(f'</{id_tag}>')
                    if id_start > len(id_tag) + 1 and id_end != -1:
                        module_id = module_content[id_start:id_end].strip()
                
                if 'module_id' in locals():
                    # 检查是否存在相同的ID
                    existing_pos = -1
                    if f'{id_tag}="' in module_content:
                        existing_pos = result.find(f'{id_tag}="{module_id}"')
                    else:
                        existing_pos = result.find(f'<{id_tag}>{module_id}<')
                        
                    if existing_pos != -1:
                        # 获取原有模块的开始位置
                        existing_start = result.rfind(f"<{tag_name}", 0, existing_pos)
                        if existing_start == -1:
                            existing_start = result.rfind(f"-<{tag_name}", 0, existing_pos)
                            
                        # 获取原有模块的结束位置
                        existing_end = result.find(f"</{tag_name}>", existing_pos) + len(f"</{tag_name}>")
                        next_line = result.find('\n', existing_end)
                        if next_line != -1:
                            existing_end = next_line
                            
                        if is_minus:
                            # 如果是-文件，删除匹配的模块
                            result = result[:existing_start] + result[existing_end:]
                        else:
                            # 如果是+文件，替换现有模块
                            adjusted_content = module_content.replace(indent, get_indent(result, existing_start))
                            result = result[:existing_start] + adjusted_content + result[existing_end:]
                    elif not is_minus:
                        # 如果不存在且不是-文件，添加到modules列表
                        modules.append(module_content)
                
                current_pos = module_end
            return result, modules
            
        # 提取并处理模块
        source_content, plus_modules = extract_modules(plus_content, module_tag, id_tag, source_content)
        
        # 在根标签结束前插入新模块（仅对+文件）
        if plus_modules and not is_minus:
            if sort_by_id:
                # 提取所有模块（包括现有的和新的）
                all_modules = []
                current_pos = 0
                while True:
                    module_start = source_content.find(f"<{module_tag}", current_pos)
                    if module_start == -1:
                        break
                    module_end = source_content.find(f"</{module_tag}>", module_start) + len(f"</{module_tag}>")
                    all_modules.append(source_content[module_start:module_end])
                    current_pos = module_end
                
                all_modules.extend(plus_modules)
                
                # 按ID排序
                def get_module_id(module):
                    if f'{id_tag}="' in module:
                        start = module.find(f'{id_tag}="') + len(id_tag) + 2
                        end = module.find('"', start)
                        return module[start:end]  # 保持为字符串
                    else:
                        start = module.find(f'<{id_tag}>') + len(id_tag) + 2
                        end = module.find(f'</{id_tag}>')
                        return module[start:end]
                
                all_modules.sort(key=get_module_id)
                
                # 重新组装内容
                content_before_modules = source_content[:source_content.find(f"<{module_tag}")]
                content_after_modules = source_content[source_content.rfind(f"</{module_tag}>") + len(f"</{module_tag}>"):]
                
                merged_content = (
                    content_before_modules +
                    '\n'.join(all_modules) +
                    content_after_modules
                )
            else:
                merged_content = (
                    source_content[:root_end] + '\n\t' +
                    '\n\t'.join(plus_modules) + '\n' +
                    source_content[root_end:]
                )
        else:
            merged_content = source_content
        
        # 保存合并后的内容
        with open(output_path, 'w', encoding='cp936') as f:
            f.write(merged_content)
            
        print(f"成功处理并保存XML文件: {os.path.abspath(output_path)}")
        return True
        
    except Exception as e:
        print(f"处理XML文件时出错: {str(e)}")
        return False

def merge_replace_ids(content):
    """合并相同Action值的Replace ID，并移除重复的ID"""
    updated_content = content
    sorts = list(re.finditer(r'<Sort Type="(\d+)">(.*?)</Sort>', content, re.DOTALL))
    
    for sort in reversed(sorts):  # 逆序处理防止位置偏移
        sort_type = sort.group(1)
        sort_content = sort.group(2)
        
        action_map = {}
        patterns = [
            (r'(<NoReplaceAction Replace="([^"]*)">(\d+)</NoReplaceAction>)', 'NoReplaceAction'),
            (r'(<Action Replace="([^"]*)">(\d+)</Action>)', 'Action')
        ]
        
        # 第一次遍历收集所有ID
        for pattern, action_type in patterns:
            for match in re.finditer(pattern, sort_content):
                full_tag, replace_ids, action_value = match.groups()
                ids = [id.strip() for id in replace_ids.split(',') if id.strip()]
                
                if action_value not in action_map:
                    action_map[action_value] = {
                        'type': action_type,
                        'ids': [],
                        'full_tags': []
                    }
                
                # 合并ID并保持顺序
                for id in ids:
                    if id not in action_map[action_value]['ids']:
                        action_map[action_value]['ids'].append(id)
                action_map[action_value]['full_tags'].append(full_tag)
        
        # 第二次遍历替换内容
        for action_value, data in action_map.items():
            if len(data['full_tags']) <= 1:
                continue
                
            # 构建新标签
            new_tag = f'<{data["type"]} Replace="{",".join(data["ids"])}">{action_value}</{data["type"]}>'
            
            # 替换第一个出现的标签
            first_tag = data['full_tags'][0]
            sort_content = sort_content.replace(first_tag, new_tag, 1)
            
            # 删除其他重复标签
            for dup_tag in data['full_tags'][1:]:
                sort_content = sort_content.replace(dup_tag, '', 1)
        
        # 清理空行
        sort_content = re.sub(r'\n\s*\n', '\n', sort_content)
        
        # 更新内容
        original_sort = sort.group(0)
        updated_sort = f'<Sort Type="{sort_type}">{sort_content}</Sort>'
        updated_content = updated_content.replace(original_sort, updated_sort)
    
    return updated_content

def update_action_with_ids(original_file, plus_file, output_file, is_minus=False):
    """更新原文件中的Action和NoReplaceAction元素，添加来自+文件的ID"""
    try:
        # 读取原文件内容
        with codecs.open(original_file, 'r', encoding='gb2312') as f:
            source_content = f.read()
            if source_content.startswith('\ufeff'):
                source_content = source_content[1:]

        # 读取+/-文件内容
        with codecs.open(plus_file, 'r', encoding='gb2312') as f:
            plus_content = f.read()
            if plus_content.startswith('\ufeff'):
                plus_content = plus_content[1:]

        # 移除注释和处理未闭合的标签
        plus_content = re.sub(r'//.*?\n', '\n', plus_content)  # 移除以//开头的注释行
        plus_content = re.sub(r'<Sort Type="(\d+)">(.*?)(?=<Sort Type="|$)', 
                            r'<Sort Type="\1">\2</Sort>', 
                            plus_content, 
                            flags=re.DOTALL)  # 处理未闭合的Sort标签

        # 预处理：合并重复的Action和ID
        source_content = merge_replace_ids(source_content)
        plus_content = merge_replace_ids(plus_content)

        # 从+/-文件中提取所有Sort和Action/NoReplaceAction信息
        plus_sorts = list(re.finditer(r'<Sort Type="(\d+)">(.*?)</Sort>', plus_content, re.DOTALL))
        if not plus_sorts:
            raise ValueError("在+/-文件中未找到Sort标签")
            
        # 移除注释块
        source_content_no_comments = re.sub(r'<!--.*?-->', '', source_content, flags=re.DOTALL)
        updated_content = source_content_no_comments

        # 用于存储每个Sort中已使用的Replace值
        sort_replace_values = {}

        for plus_sort in plus_sorts:
            plus_sort_type = plus_sort.group(1)
            plus_sort_content = plus_sort.group(2)
            
            # 初始化当前Sort的Replace值集合
            if plus_sort_type not in sort_replace_values:
                sort_replace_values[plus_sort_type] = set()
            
            # 查找该Sort下的所有Action和NoReplaceAction
            action_patterns = [
                (r'<Action Replace="([^"]*)">(.*?)</Action>', 'Action'),
                (r'<NoReplaceAction Replace="([^"]*)">(.*?)</NoReplaceAction>', 'NoReplaceAction')
            ]
            
            for pattern, action_type in action_patterns:
                plus_actions = list(re.finditer(pattern, plus_sort_content))
                
                for plus_action in plus_actions:
                    plus_ids = plus_action.group(1).strip(',').split(',')
                    plus_ids = [id.strip() for id in plus_ids if id.strip()]
                    plus_action_value = plus_action.group(2).strip()

                    # 检查Replace值是否已存在于当前Sort中
                    if plus_action_value in sort_replace_values[plus_sort_type]:
                        print(f"\n警告: Sort Type={plus_sort_type}中已存在Replace值={plus_action_value}，跳过处理")
                        continue
                    
                    sort_replace_values[plus_sort_type].add(plus_action_value)

                    # 在原文件中查找对应的Sort
                    sort_pattern = rf'<Sort Type="{plus_sort_type}">(.*?)</Sort>'
                    sort_match = re.search(sort_pattern, updated_content, re.DOTALL)
                    
                    if sort_match:
                        sort_content = sort_match.group(0)
                        
                        # 尝试精确匹配Action或NoReplaceAction
                        action_pattern = rf'<{action_type} Replace="[^"]*">{plus_action_value}</{action_type}>'
                        action_match = re.search(action_pattern, sort_content)
                        
                        if action_match:
                            if is_minus:
                                # 如果是-文件，删除匹配的ID
                                full_match = action_match.group(0)
                                existing_replace_match = re.search(r'Replace="([^"]*)"', full_match)
                                if existing_replace_match:
                                    existing_ids = [id.strip() for id in existing_replace_match.group(1).split(',') if id.strip()]
                                    
                                    # 移除需要删除的ID
                                    remaining_ids = [id for id in existing_ids if id not in plus_ids]
                                    
                                    if remaining_ids:
                                        # 更新Replace属性
                                        new_replace = ','.join(remaining_ids)
                                        updated_action = re.sub(
                                            r'Replace="[^"]*"',
                                            f'Replace="{new_replace}"',
                                            full_match
                                        )
                                        updated_content = updated_content.replace(full_match, updated_action)
                                    else:
                                        # 没有剩余ID时删除整个标签
                                        updated_content = updated_content.replace(full_match, '')
                                        print(f"删除 Sort Type={plus_sort_type} 的 {action_type} 值={plus_action_value}")
                            else:
                                # 如果是+文件，更新已存在的Action/NoReplaceAction
                                full_match = action_match.group(0)
                                existing_replace_match = re.search(r'Replace="([^"]*)"', full_match)
                                existing_ids = []
                                if existing_replace_match:
                                    existing_ids = existing_replace_match.group(1).strip(',').split(',')
                                    existing_ids = [id.strip() for id in existing_ids if id.strip()]

                                # 合并并去重ID，保持顺序
                                all_ids = existing_ids + plus_ids
                                unique_ids = []
                                seen = set()
                                for id in all_ids:
                                    if id not in seen and id:
                                        unique_ids.append(id)
                                        seen.add(id)

                                new_replace = ','.join(unique_ids)
                                updated_action = re.sub(
                                    r'Replace="[^"]*"',
                                    f'Replace="{new_replace}"',
                                    full_match
                                )
                                updated_content = updated_content.replace(full_match, updated_action)
                        elif not is_minus:
                            # 如果是+文件且Action/NoReplaceAction不存在，添加新的
                            indent = re.search(r'(\s*)<Sort', sort_content).group(1)
                            action_indent = indent + '    '  # 增加一级缩进
                            
                            new_action = f'{action_indent}<{action_type} Replace="{",".join(plus_ids)}">{plus_action_value}</{action_type}>\n{indent}'
                            
                            # 在Sort结束标签前插入新Action/NoReplaceAction
                            sort_end_pos = sort_content.rfind('</Sort>')
                            updated_sort = sort_content[:sort_end_pos] + new_action + sort_content[sort_end_pos:]
                            updated_content = updated_content.replace(sort_content, updated_sort)
                    else:
                        raise ValueError(f"在原文件中未找到Sort Type={plus_sort_type}")

        # 修复可能的多行问题
        updated_content = re.sub(r'</Sort>\s*<Sort', '</Sort>\n  <Sort', updated_content)
        updated_content = re.sub(r'</Sort>\s*$', '</Sort>\n', updated_content)

        # 保存更新后的文件
        with codecs.open(output_file, 'w', encoding='gb2312') as f:
            f.write(updated_content)

        print(f"\n成功更新并保存文件: {os.path.abspath(output_file)}")
        return True

    except Exception as e:
        print(f"更新文件失败: {str(e)}")
        return False

def get_task_info(task_content):
    """获取TaskList的Profession属性和TaskID"""
    profession = None
    task_id = None
    
    # 获取Profession属性
    prof_match = re.search(r'<TaskList[^>]*Profession="(\d+)"', task_content)
    if prof_match:
        profession = prof_match.group(1)
    
    # 获取TaskID
    task_id_match = re.search(r'<TaskID>(\d+)</TaskID>', task_content)
    if task_id_match:
        task_id = task_id_match.group(1)
    
    return profession, task_id

def merge_task_xml(source_path, plus_path, output_path, is_minus=False):
    """合并task.xml文件（规则9）"""
    try:
        # 读取源文件内容
        source_content = ""
        if os.path.exists(source_path):
            with open(source_path, 'r', encoding='cp936') as f:
                source_content = f.read()
        else:
            print("警告：源文件不存在")

        # 读取+/-文件内容
        with open(plus_path, 'r', encoding='cp936') as f:
            plus_content = f.read()

        updated_content = source_content

        # 定义所有支持的标签
        supported_tags = ['主线', '活动', '支线', '剧情', '奇遇', '其他']
        
        # 处理每个plus文件的Section
        for tag in supported_tags:
            tag_match = re.search(f'<{tag}>(.*?)</{tag}>', plus_content, re.DOTALL)
            if tag_match:
                tag_content = tag_match.group(1)
                section_matches = list(re.finditer(r'<Section>.*?</Section>', tag_content, re.DOTALL))
                
                for section in section_matches:
                    section_content = section.group(0)
                    section_id_match = re.search(r'<SectionId>(\d+)</SectionId>', section_content)
                    if section_id_match:
                        section_id = section_id_match.group(1)
                        
                        # 在所有标签中查找对应的SectionId
                        section_found = False
                        for source_tag in supported_tags:
                            if section_found:
                                break
                                
                            tag_match_in_source = re.search(f'(<{source_tag}>)(.*?)(</{source_tag}>)', updated_content, re.DOTALL)
                            if tag_match_in_source:
                                # 检查是否已存在该SectionId
                                old_section_pattern = f'<Section>.*?<SectionId>{section_id}</SectionId>.*?</Section>'
                                tag_content_in_source = tag_match_in_source.group(2)
                                existing_section = re.search(old_section_pattern, tag_content_in_source, re.DOTALL)
                                
                                if existing_section:
                                    section_found = True
                                    if is_minus:
                                        # 如果是-文件，删除匹配的Section和周围的空白字符
                                        new_content = re.sub(r'\s*' + old_section_pattern + r'\s*', '\n', tag_content_in_source, flags=re.DOTALL)
                                        # 如果删除后标签内容为空，保留一个换行
                                        if not new_content.strip():
                                            new_content = '\n'
                                        updated_content = updated_content.replace(tag_content_in_source, new_content)
                                    else:
                                        new_content = re.sub(old_section_pattern, section_content, tag_content_in_source, flags=re.DOTALL)
                                        updated_content = updated_content.replace(tag_content_in_source, new_content)

                        if not section_found and not is_minus:
                            # 如果是+文件且Section不存在，添加到对应标签
                            tag_match_in_source = re.search(f'(<{tag}>)(.*?)(</{tag}>)', updated_content, re.DOTALL)
                            if tag_match_in_source:
                                insert_pos = tag_match_in_source.end(2)
                                if tag_content_in_source.strip():
                                    updated_content = updated_content[:insert_pos] + '\n      ' + section_content + '\n' + updated_content[insert_pos:]
                                else:
                                    updated_content = updated_content[:insert_pos] + '\n      ' + section_content + '\n    ' + updated_content[insert_pos:]

        # 处理TaskList标签
        task_list_matches = list(re.finditer(r'<TaskList[^>]*>.*?</TaskList>', plus_content, re.DOTALL))
        
        for task in task_list_matches:
            task_content = task.group(0)
            profession, task_id = get_task_info(task_content)
            
            if profession and task_id:
                # 在原文件中查找相同Profession和TaskID的TaskList
                old_task_pattern = f'<TaskList[^>]*Profession="{profession}"[^>]*>.*?<TaskID>{task_id}</TaskID>.*?</TaskList>'
                existing_task = re.search(old_task_pattern, updated_content, re.DOTALL)
                
                if existing_task:
                    if is_minus:
                        # 删除TaskList和周围的空白字符
                        updated_content = re.sub(r'\s*' + old_task_pattern + r'\s*', '\n', updated_content, flags=re.DOTALL)
                    else:
                        updated_content = re.sub(old_task_pattern, task_content, updated_content, flags=re.DOTALL)
                elif not is_minus:
                    # 找到相同Profession的最后一个TaskList
                    prof_pattern = f'<TaskList[^>]*Profession="{profession}"[^>]*>.*?</TaskList>'
                    last_prof_task = None
                    for match in re.finditer(prof_pattern, updated_content, re.DOTALL):
                        last_prof_task = match
                    
                    if last_prof_task:
                        pos = last_prof_task.end()
                        updated_content = updated_content[:pos] + '\n      ' + task_content + updated_content[pos:]
                    else:
                        last_task = re.search(r'</TaskList>[^<]*(?=</?[^>]+>)', updated_content)
                        if last_task:
                            pos = last_task.end()
                            updated_content = updated_content[:pos] + '\n      ' + task_content + updated_content[pos:]

        # 清理多余的空行
        updated_content = re.sub(r'\n\s*\n\s*\n', '\n\n', updated_content)

        # 保存更新后的文件
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        with open(output_path, 'w', encoding='cp936') as f:
            f.write(updated_content)
            
        return True

    except Exception as e:
        print(f"处理task.xml文件失败: {str(e)}")
        import traceback
        print(f"错误详情:\n{traceback.format_exc()}")
        return False

def merge_itemcategory_files(original_file, plus_file, output_file, is_minus=False):
    """合并 itemcategory 格式的文件（规则13）"""
    try:
        def parse_file(file_content):
            items = {}
            sections_order = []  # 保存section顺序
            current_key = None
            current_comment = []  # 用于存储注释
            
            for line in file_content.splitlines():
                line = line.strip()  # 处理可能的空白字符
                if not line:  # 跳过空行
                    continue
                if line.startswith('//'):  # 处理注释
                    current_comment.append(line)
                elif line.startswith('[') and line.endswith(']'):
                    current_key = line
                    if current_key not in items:  # 只在首次出现时添加到顺序列表
                        sections_order.append(current_key)
                    items[current_key] = {
                        'ids': [],  # 使用列表存储ID
                        'comments': current_comment,  # 存储该分类的注释
                        'order': len(sections_order),  # 记录顺序
                        'attributes': []  # 存储name=等属性
                    }
                    current_comment = []  # 重置注释列表
                elif line.startswith('IDRange='):
                    try:
                        ids = line[len('IDRange='):].split(',')
                        ids = [id.strip() for id in ids if id.strip()]
                        if current_key:  # 确保有当前分类
                            items[current_key]['ids'].extend(ids)
                    except Exception as e:
                        print(f"解析IDRange时出错: {line}, 错误: {str(e)}")
                elif line.startswith('name='):  # 处理name=属性
                    if current_key:
                        items[current_key]['attributes'].append(line)
                
            if current_comment and current_key:
                items[current_key]['comments'].extend(current_comment)
                
            return items, sections_order

        def merge_items(original_items, plus_items, original_order, plus_order, is_minus=False):
            merged_order = original_order.copy()
            if not is_minus:
                # 如果是+文件，添加新的section
                for section in plus_order:
                    if section not in merged_order:
                        merged_order.append(section)
            
            merged_items = original_items.copy()
            for key, plus_data in plus_items.items():
                if key in merged_items:
                    if is_minus:
                        # 如果是-文件，从原始ID列表中删除指定的ID
                        original_ids = []
                        minus_ids = set(plus_data['ids'])
                        for id in merged_items[key]['ids']:
                            if id not in minus_ids:
                                original_ids.append(id)
                        merged_items[key]['ids'] = original_ids
                        
                        # 如果删除后没有ID了，删除整个section
                        if not original_ids:
                            del merged_items[key]
                            merged_order.remove(key)
                    else:
                        # 如果是+文件，合并ID列表
                        original_ids = []
                        seen = set(plus_data['ids'])
                        for id in merged_items[key]['ids']:
                            if id not in seen:
                                original_ids.append(id)
                        
                        merged_items[key]['ids'] = original_ids + plus_data['ids']
                        
                        # 合并注释，去重但保持顺序
                        seen_comments = set()
                        merged_comments = []
                        for comment in merged_items[key]['comments'] + plus_data['comments']:
                            if comment not in seen_comments:
                                seen_comments.add(comment)
                                merged_comments.append(comment)
                        merged_items[key]['comments'] = merged_comments
                elif not is_minus:
                    # 如果是+文件且section不存在，添加新的section
                    merged_items[key] = plus_data
                    
            return merged_items, merged_order

        def format_items(items, sections_order):
            formatted_lines = []
            for key in sections_order:
                if key in items:  # 确保section存在
                    data = items[key]
                    # 首先添加注释
                    formatted_lines.extend(data['comments'])
                    # 添加分类名
                    formatted_lines.append(key)
                    # 添加ID范围
                    if data['ids']:  # 只有当有ID时才添加IDRange行
                        formatted_lines.append('IDRange=' + ','.join(data['ids']))
                    # 添加属性（如name=）
                    if 'attributes' in data and data['attributes']:
                        formatted_lines.extend(data['attributes'])
                    # 添加空行以提高可读性
                    formatted_lines.append('')
            return '\n'.join(formatted_lines)

        # 尝试多种编码方式读取文件
        def read_file_with_encoding(file_path):
            encodings = ['utf-8', 'gbk', 'gb2312', 'ansi']
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        return f.read()
                except UnicodeDecodeError:
                    continue
            raise UnicodeDecodeError(f"无法使用已知编码格式读取文件: {file_path}")

        original_content = read_file_with_encoding(original_file)
        plus_content = read_file_with_encoding(plus_file)

        # 解析文件，获取内容和顺序
        original_items, original_order = parse_file(original_content)
        plus_items, plus_order = parse_file(plus_content)
        
        # 合并内容和顺序
        merged_items, merged_order = merge_items(original_items, plus_items, original_order, plus_order, is_minus)
        
        # 按照合并后的顺序格式化内容
        merged_content = format_items(merged_items, merged_order)

        # 使用与原始文件相同的编码保存
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(merged_content)
        except UnicodeEncodeError:
            with open(output_file, 'w', encoding='gbk') as f:
                f.write(merged_content)
        
        return True
        
    except Exception as e:
        print(f"处理文件时发生错误: {str(e)}")
        return False

def merge_ini_files(original_file, additional_file, output_file, is_minus=False):
    """规则14的合并逻辑（基于原整合str类.py）"""
    def read_file_with_encoding(file_path):
        # 尝试多种编码方式读取文件
        encodings = ['utf-8', 'gbk', 'gb2312', 'ansi']
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    return f.readlines()
            except UnicodeDecodeError:
                continue
        raise UnicodeDecodeError(f"无法使用已知编码格式读取文件: {file_path}")

    def extract_id(line):
        """从行中提取ID"""
        line = line.strip()
        if '=' in line:
            return line.split('=')[0].strip()
        elif ',' in line:
            return line.split(',')[0].strip()
        return None

    def find_insert_position(id_num, lines, header_length=0):
        """找到应该插入的位置，考虑头部注释的长度"""
        current_pos = header_length  # 从头部注释之后开始查找
        for i, line in enumerate(lines[header_length:], header_length):
            if not is_comment_line(line):
                current_id = extract_id(line)
                if current_id:
                    try:
                        current_id_num = int(current_id)
                        if current_id_num > id_num:
                            return i
                        current_pos = i + 1
                    except ValueError:
                        continue
        return current_pos

    def is_comment_line(line):
        """检查是否是注释行"""
        stripped = line.strip()
        return stripped.startswith('//') or stripped.startswith(';') or not stripped

    def get_end_comments(lines):
        """获取文件末尾的注释"""
        end_comments = []
        i = len(lines) - 1
        
        # 跳过末尾空行
        while i >= 0 and not lines[i].strip():
            i -= 1
            
        # 收集注释行
        while i >= 0 and is_comment_line(lines[i]):
            end_comments.insert(0, lines[i])
            i -= 1
            
        return end_comments, i + 1

    def should_append_to_end(line):
        """检查是否应该追加到末尾（用逗号分隔的格式）"""
        return ',' in line and '=' not in line

    # 读取文件内容
    original_lines = read_file_with_encoding(original_file)
    additional_lines = read_file_with_encoding(additional_file)
    
    # 处理行尾换行符
    original_lines = [line.rstrip('\n') for line in original_lines]
    additional_lines = [line.rstrip('\n') for line in additional_lines]
    
    # 保存文件头部注释
    header_comments = []
    i = 0
    while i < len(original_lines) and is_comment_line(original_lines[i]):
        header_comments.append(original_lines[i])
        i += 1
    
    # 保存原始文件的末尾注释
    original_end_comments, content_end_pos = get_end_comments(original_lines)
    
    # 创建ID到行的映射，用于检测重复ID
    seen_ids = set()
    id_map = {}
    
    # 首先处理附加文件中的内容
    additional_id_map = {}
    current_comments = []
    for line in additional_lines:
        if is_comment_line(line):
            current_comments.append(line)
        else:
            key = extract_id(line)
            if key:
                additional_id_map[key] = {
                    'line': line,
                    'comments': current_comments.copy()
                }
            current_comments = []

    # 重建文件内容
    final_lines = []
    current_comments = []
    
    # 添加头部注释
    final_lines.extend(header_comments)
    
    # 首先收集所有ID的最终值
    final_id_map = {}
    
    # 处理原始文件内容
    for line in original_lines[len(header_comments):content_end_pos + 1]:
        if not is_comment_line(line):
            key = extract_id(line)
            if key:
                final_id_map[key] = {
                    'line': line,
                    'comments': current_comments.copy()
                }
            current_comments = []
        else:
            current_comments.append(line)
    
    # 处理附加文件内容
    for key, data in additional_id_map.items():
        if is_minus:
            # 如果是-文件，从final_id_map中删除对应的ID
            if key in final_id_map:
                del final_id_map[key]
        else:
            # 如果是+文件，更新或添加ID
            final_id_map[key] = data
    
    # 按照原始文件的顺序重建内容，但使用最终的值
    current_comments = []
    processed_ids = set()
    
    for line in original_lines[len(header_comments):content_end_pos + 1]:
        if is_comment_line(line):
            current_comments.append(line)
        else:
            key = extract_id(line)
            if key:
                if key not in processed_ids:  # 只处理未处理过的ID
                    processed_ids.add(key)
                    if key in final_id_map:
                        final_lines.extend(final_id_map[key]['comments'])
                        final_lines.append(final_id_map[key]['line'])
            else:
                final_lines.extend(current_comments)
                final_lines.append(line)
            current_comments = []
    
    # 添加新的ID（不在原始文件中的ID）
    append_lines = []
    insert_lines = []
    
    if not is_minus:  # 只在非-文件时添加新ID
        for key in sorted(final_id_map.keys(), key=lambda x: int(x) if x.isdigit() else float('inf')):
            if key not in processed_ids:
                data = final_id_map[key]
                if should_append_to_end(data['line']):
                    append_lines.extend(data['comments'])
                    append_lines.append(data['line'])
                else:
                    insert_pos = find_insert_position(int(key), final_lines, len(header_comments))
                    for comment in data['comments']:
                        final_lines.insert(insert_pos, comment)
                        insert_pos += 1
                    final_lines.insert(insert_pos, data['line'])
    
    # 添加需要追加到末尾的内容
    final_lines.extend(append_lines)
    
    # 添加末尾注释
    final_lines.extend(original_end_comments)
    
    # 保存文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(final_lines) + '\n')
    except UnicodeEncodeError:
        with open(output_file, 'w', encoding='gbk') as f:
            f.write('\n'.join(final_lines) + '\n')

    return True

def merge_ini_rule14(source_path, plus_path, output_path, is_minus=False):
    """规则14的合并逻辑（基于原整合str类.py）"""
    try:
        # 直接调用原始整合函数
        merge_ini_files(source_path, plus_path, output_path, is_minus)
        return True
    except Exception as e:
        print(f"规则14合并失败: {str(e)}")
        return False

def merge_syntotempillar_files(original_file, plus_file, output_file, is_minus=False):
    """合并军团图鉴文件（规则15）"""
    try:
        def read_file_with_encoding(file_path):
            encodings = ['utf-8', 'gbk', 'gb2312', 'ansi']
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        return f.readlines()
                except UnicodeDecodeError:
                    continue
            raise UnicodeDecodeError(f"无法使用已知编码格式读取文件: {file_path}")

        def is_comment_line(line):
            stripped = line.strip()
            return stripped.startswith('//') or stripped.startswith(';') or not stripped

        def get_section_lines(lines, start_index, end_index=None):
            if end_index is None:
                return lines[start_index:]
            return lines[start_index:end_index]

        def update_amount_and_ids(section_lines, additional_data, is_minus=False):
            # 创建现有ID的映射
            existing_data = {}
            lines_to_remove = []
            
            # 保留section标题行
            section_header = section_lines[0]
            section_lines.pop(0)
            
            # 收集现有的Eudemon数据和需要移除的行
            for i, line in enumerate(section_lines):
                if '=' in line and line.strip().startswith('Eudemon'):
                    try:
                        eid = int(line.split('=')[0].replace('Eudemon', '').strip())
                        data = line.split('=', 1)[1].strip()
                        existing_data[eid] = data
                        lines_to_remove.append(i)
                    except ValueError:
                        continue

            # 处理新数据，检查ID是否已存在
            if is_minus:
                # 如果是-文件，删除指定的ID
                for new_line in additional_data:
                    new_item_id = new_line.split()[0]
                    # 查找并删除匹配的ID
                    ids_to_remove = []
                    for eid, data in existing_data.items():
                        if new_item_id == data.split()[0]:
                            ids_to_remove.append(eid)
                    for eid in ids_to_remove:
                        del existing_data[eid]
            else:
                # 如果是+文件，添加或更新ID
                for new_line in additional_data:
                    new_item_id = new_line.split()[0]
                    
                    actual_id = None
                    for existing_id, existing_data_value in existing_data.items():
                        if new_item_id == existing_data_value.split()[0]:
                            actual_id = existing_id
                            break
                    
                    if actual_id is None:
                        actual_id = max(list(existing_data.keys()) + [-1]) + 1
                    
                    existing_data[actual_id] = new_line

            # 移除原有的Eudemon行
            for i in reversed(lines_to_remove):
                section_lines.pop(i)

            # 重建section内容
            new_section_lines = [section_header]  # 从section标题开始
            
            # 添加Amount行（如果存在）
            for line in section_lines:
                if line.strip().startswith('Amount='):
                    new_section_lines.append(f'Amount={len(existing_data)}\n')
                    break
            
            # 按ID排序添加更新后的数据
            for eid in sorted(existing_data.keys()):
                new_section_lines.append(f'Eudemon{eid}={existing_data[eid]}\n')

            # 确保末尾有一个空行
            if new_section_lines and new_section_lines[-1].strip():
                new_section_lines.append('\n')

            # 更新section_lines
            section_lines.clear()
            section_lines.extend(new_section_lines)

        original_lines = read_file_with_encoding(original_file)
        additional_lines = read_file_with_encoding(plus_file)

        # 解析additional文件中的数据，按section分组
        additional_data = {}
        current_section = None
        
        for line in additional_lines:
            if line.strip().startswith('['):
                current_section = line.strip()[1:-1]  # 移除[]获取section名称
                additional_data[current_section] = []
            elif current_section and not is_comment_line(line) and line.strip().startswith('Eudemon'):
                value = line.split('=', 1)[1].strip()
                additional_data[current_section].append(value)

        # 处理每个section
        result_lines = []
        current_section = None
        section_start = 0
        
        for i, line in enumerate(original_lines):
            if line.strip().startswith('['):
                # 处理上一个section
                if current_section and current_section in additional_data:
                    section_lines = get_section_lines(original_lines, section_start, i)
                    update_amount_and_ids(section_lines, additional_data[current_section], is_minus)
                    result_lines.extend(section_lines)
                else:
                    result_lines.extend(original_lines[section_start:i])
                
                # 开始新的section
                current_section = line.strip()[1:-1]
                section_start = i

        # 处理最后一个section
        if current_section and current_section in additional_data:
            section_lines = get_section_lines(original_lines, section_start)
            update_amount_and_ids(section_lines, additional_data[current_section], is_minus)
            result_lines.extend(section_lines)
        else:
            result_lines.extend(original_lines[section_start:])

        # 写入文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.writelines(result_lines)
            
        print(f"\n成功{'删除' if is_minus else '合并'}并保存文件: {os.path.abspath(output_file)}")
        return True

    except Exception as e:
        print(f"处理军团图鉴文件失败: {str(e)}")
        return False



def process_minus_files(source_dir, minus_dir, temp_dir, output_dir, file_type, ini_x=None):
    """
    处理-ani/-ini文件夹中的文件，从原始资源中删除对应内容

    Args:
        source_dir: 原始资源目录
        minus_dir: -ani或-ini文件夹目录
        temp_dir: 临时输出目录
        output_dir: 输出目录
        file_type: 文件类型('ani'或'ini')
        ini_x: ini_x的名称（如'ini_1'），用于特殊处理ini_x类型的文件
    """
    # 确保临时输出目录存在
    if os.path.exists(temp_dir):
        shutil.rmtree(temp_dir)  # 先删除已存在的临时目录
    
    # 定义ini类型的文件扩展名
    ini_extensions = ('.ini', '.txt', '.csv', '.xml')

    # 首先复制原始资源到临时目录
    source_type_dir = os.path.join(source_dir, file_type)
    temp_type_dir = os.path.join(temp_dir, file_type)
    if os.path.exists(source_type_dir):
        shutil.copytree(source_type_dir, temp_type_dir)  # 现在可以安全创建

    # 遍历-ani/-ini目录
    for root, _, files in os.walk(minus_dir):
        for file in files:
            # 根据文件类型判断是否处理该文件
            if (file_type == 'ani' and file.endswith(('.ani', '.size'))) or \
                    (file_type == 'ini' and file.endswith(ini_extensions)):
                
                # 获取相对路径
                rel_path = os.path.relpath(root, minus_dir)
                
                # 去掉文件名中的"-"号来匹配原始资源文件
                original_file = file.replace('-', '')
                
                # 构建对应的源文件和临时文件路径
                source_file = None
                temp_file = None
                
                # 对于ini类型的文件，在原始资源目录中递归查找
                if file_type == 'ini':
                    if ini_x:
                        # 对于ini_x，使用特殊的路径结构
                        source_file = os.path.join(source_dir, 'ini', ini_x, rel_path, original_file)
                        temp_file = os.path.join(temp_dir, 'ini', ini_x, rel_path, original_file)
                    else:
                        source_file = find_original_file(os.path.join(source_dir, 'ini'), original_file)
                        if source_file:
                            # 获取相对于原始资源ini目录的路径
                            rel_output_path = os.path.relpath(os.path.dirname(source_file),
                                                            os.path.join(source_dir, 'ini'))
                            temp_file = os.path.join(temp_dir, 'ini', rel_output_path, original_file)
                        else:
                            # 如果没找到原始文件，使用-ini中的目录结构
                            temp_file = os.path.join(temp_dir, 'ini', rel_path, original_file)
                else:
                    source_file = os.path.join(source_dir, file_type, rel_path, original_file)
                    temp_file = os.path.join(temp_dir, file_type, rel_path, original_file)

                minus_file = os.path.join(root, file)

                print(f'处理-文件：')
                print(f'源文件：{source_file}')
                print(f'-{file_type}文件：{minus_file}')
                print(f'临时文件：{temp_file}')

                # 如果原始资源文件不存在，跳过处理
                if not source_file or not os.path.exists(source_file):
                    print(f'[错误] 文件 {os.path.basename(minus_file)} 的原始资源不存在，跳过处理')
                    continue

                # 处理文件内容删除
                process_minus_content(source_file, minus_file, temp_file)
                print(f'处理完成')
                print('-------------------')

        # 复制临时文件到客户端更新
        if os.path.exists(temp_dir):
            for root, _, files in os.walk(temp_dir):
                for file in files:
                    # 获取相对路径
                    rel_path = os.path.relpath(root, temp_dir)
                    # 构建目标路
                    target_path = os.path.join(output_dir, rel_path, file)
                    os.makedirs(os.path.dirname(target_path), exist_ok=True)
                    shutil.copy2(os.path.join(root, file), target_path)
                    print(f'已复制临时文件到客户端更新: {target_path}')

def process_minus_content(source_path, minus_path, output_path):
    """处理-文件的内容删除操作"""
    content_dict = {}
    filename = os.path.basename(source_path)
    rule_type = get_merge_rule(filename)

    # 如果是规则7，检查文件名格式
    if rule_type == 7:
        minus_filename = os.path.basename(minus_path)
        # 如果文件名以-开头（如-shop.ini），则不处理
        if minus_filename.startswith('-'):
            print(f"警告：规则7文件 {minus_filename} 以-开头，跳过处理")
            return
        # 如果文件名包含+号，则不处理
        if '+' in minus_filename:
            print(f"警告：规则7文件 {minus_filename} 包含+号，跳过处理")
            return
        # 如果文件名正确（不以-开头且不含+号），则删除原始文件
        if os.path.exists(source_path):
            os.remove(source_path)
        return

    # 如果是规则9（task.xml），使用特殊处理
    if rule_type == 9:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        if os.path.exists(source_path):
            success = merge_task_xml(source_path, minus_path, output_path, is_minus=True)  # 注意这里设置 is_minus=True
            if not success:
                print(f"警告：task.xml处理失败")
                shutil.copy2(source_path, output_path)
        return

    # 如果是规则11（replaceaction.xml），使用特殊处理
    if rule_type == 11:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        if os.path.exists(source_path):
            success = update_action_with_ids(source_path, minus_path, output_path, is_minus=True)
            if not success:
                print(f"警告：replaceaction.xml处理失败")
                shutil.copy2(source_path, output_path)
        return

    # 如果是规则12（XML文件），使用特殊处理
    if rule_type == 12:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        if os.path.exists(source_path):
            success = merge_xml_files(source_path, minus_path, output_path, is_minus=True)
            if not success:
                print(f"警告：XML文件处理失败")
                shutil.copy2(source_path, output_path)
        return

    # 如果是规则13（itemcategory.ini格式），使用特殊处理
    if rule_type == 13:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        if os.path.exists(source_path):
            success = merge_itemcategory_files(source_path, minus_path, output_path, is_minus=True)
            if not success:
                print(f"警告：itemcategory格式文件处理失败")
                shutil.copy2(source_path, output_path)
        return

    # 如果是规则14（str类文件），使用特殊处理
    if rule_type == 14:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        if os.path.exists(source_path):
            success = merge_ini_rule14(source_path, minus_path, output_path, is_minus=True)
            if not success:
                print(f"警告：规则14处理失败")
                shutil.copy2(source_path, output_path)
        return

    # 如果是规则15（军团图鉴文件），使用特殊处理
    if rule_type == 15:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        if os.path.exists(source_path):
            success = merge_syntotempillar_files(source_path, minus_path, output_path, is_minus=True)
            if not success:
                print(f"警告：规则15处理失败")
                shutil.copy2(source_path, output_path)
        return



    # 读取源文件内容
    process_files(source_path, content_dict, rule_type)

    # 读取-文件内容
    minus_content = {}
    process_files(minus_path, minus_content, rule_type)

    # 根据不同规则处理内容删除
    if rule_type == 8:
        # 对于规则8，需要从原内容中删除-文件中指定的内容
        for section, minus_lines in minus_content.items():
            if section in content_dict:
                # 获取要删除的内容（除了段落标题）
                minus_content_set = set()
                for line in minus_lines:
                    if not line.strip().startswith('['):
                        minus_content_set.add(line.strip())
                
                # 保留不在minus_content_set中的行
                content_dict[section] = [
                    line for line in content_dict[section]
                    if not line.strip() in minus_content_set
                ]
    elif rule_type in [1, 5]:
        # 规则1和5：按[xxx]标识处理
        for key in minus_content.keys():
            if key in content_dict:
                del content_dict[key]
    elif rule_type in [2, 3, 10]:
        # 规则2、3、10：按ID排序处理
        for key in minus_content.keys():
            try:
                # 尝试将key转换为数字进行比较
                minus_id = int(key.split()[0].strip())
                keys_to_remove = []
                for content_key in content_dict.keys():
                    try:
                        content_id = int(content_key.split()[0].strip())
                        if content_id == minus_id:
                            keys_to_remove.append(content_key)
                    except (ValueError, IndexError):
                        continue
                for k in keys_to_remove:
                    del content_dict[k]
            except (ValueError, IndexError):
                continue
    elif rule_type == 16:
        # 规则16：closetitem.txt文件，根据第二位物品ID删除
        for key in minus_content.keys():
            if key == '__header__':
                continue
            try:
                # 获取-文件中的第二位物品ID
                parts = key.split()
                if len(parts) >= 2:
                    minus_second_id = int(parts[1].strip())
                    keys_to_remove = []
                    for content_key in content_dict.keys():
                        if content_key == '__header__':
                            continue
                        try:
                            content_parts = content_key.split()
                            if len(content_parts) >= 2:
                                content_second_id = int(content_parts[1].strip())
                                if content_second_id == minus_second_id:
                                    keys_to_remove.append(content_key)
                        except (ValueError, IndexError):
                            continue
                    for k in keys_to_remove:
                        del content_dict[k]
            except (ValueError, IndexError):
                continue
    elif rule_type == 4:
        # 规则4：每个段落前有注释的文件
        for key, minus_lines in minus_content.items():
            if key in content_dict:
                # 获取要删除的内容
                minus_content_set = set(line.strip() for line in minus_lines)
                # 保留不在minus_content_set中的行
                content_dict[key] = [
                    line for line in content_dict[key]
                    if line.strip() not in minus_content_set
                ]
    elif rule_type == 6:
        # 规则6：每个段落前有注释的文件（如npcface.ani）
        for key, minus_lines in minus_content.items():
            if key in content_dict:
                # 获取要删除的内容，包括注释
                minus_content_set = set(line.strip() for line in minus_lines)
                # 保留不在minus_content_set中的行
                content_dict[key] = [
                    line for line in content_dict[key]
                    if line.strip() not in minus_content_set
                ]

    # 确保输出文件的目录存在
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    # 检测原始文件的编码
    source_encoding = None
    for encoding in ['utf-8', 'gbk', 'gb2312', 'gb18030', 'ansi', 'cp936']:
        try:
            with open(source_path, 'r', encoding=encoding) as f:
                f.read()
            source_encoding = encoding
            break
        except UnicodeDecodeError:
            continue

    if not source_encoding:
        source_encoding = 'gbk'

    # 使用检测到的编码写入文件
    with open(output_path, 'w', encoding=source_encoding) as f:
        # 首先写入头部（如果有）
        if '__header__' in content_dict:
            f.writelines(content_dict['__header__'])
            del content_dict['__header__']

        # 对剩余内容进行排序并写入
        sorted_content = sort_content(content_dict, rule_type, filename)
        for key, content in sorted_content.items():
            if isinstance(content, list):
                for line in content:
                    f.write(line)
            else:
                f.write(content)

def copy_original_files(complete_dir, source_dir, plus_dir, minus_dir, file_type, ini_x=None):
    """
    根据+/-文件从完整目录复制原始文件到原始资源目录
    
    Args:
        complete_dir: 完整资源目录
        source_dir: 原始资源目录
        plus_dir: +ani或+ini文件夹目录
        minus_dir: -ani或-ini文件夹目录（可选）
        file_type: 文件类型('ani'或'ini')
        ini_x: ini_x的名称（如'ini_1'），用于特殊处理ini_x类型的文件
    """
    # 只在完整目录存在时才清理原始资源目录
    if os.path.exists(complete_dir):
        # 首先清理原始资源目录中的对应文件夹
        target_dir = os.path.join(source_dir, file_type)
        if os.path.exists(target_dir) and not ini_x:  # 只在非ini_x的情况下清理
            print(f'清理原始资源目录: {target_dir}')
            shutil.rmtree(target_dir)
    else:
        print(f'完整目录不存在，保留原始资源目录内容')
        return
    
    # 定义ini类型的文件扩展名
    ini_extensions = ('.ini', '.txt', '.csv', '.xml')
    needed_files = set()
    
    def scan_directory(directory, is_plus=True):
        """递归扫描目录并收集文件"""
        if not os.path.exists(directory):
            return
            
        for root, _, files in os.walk(directory):
            for file in files:
                if (file_type == 'ani' and file.endswith(('.ani', '.size'))) or \
                   (file_type == 'ini' and file.endswith(ini_extensions)):
                    # 去掉文件名中的"+"或"-"号
                    original_file = file.replace('+' if is_plus else '-', '')
                    # 获取相对路径，包括子文件夹
                    rel_path = os.path.relpath(root, directory)
                    needed_files.add((rel_path, original_file))
                    
                    # 在完整目录中查找文件
                    if ini_x:
                        # 对于ini_x，在ini/ini_x目录下查找
                        complete_file = os.path.join(complete_dir, 'ini', ini_x, rel_path, original_file)
                    else:
                        complete_file = os.path.join(complete_dir, file_type, rel_path, original_file)
                        
                    if not os.path.exists(complete_file):
                        # 尝试在完整目录的子文件夹中查找
                        found = False
                        search_root = os.path.join(complete_dir, 'ini', ini_x) if ini_x else os.path.join(complete_dir, file_type)
                        for complete_root, _, complete_files in os.walk(search_root):
                            if original_file in complete_files:
                                new_rel_path = os.path.relpath(complete_root, search_root)
                                # 更新为实际找到的路径
                                needed_files.remove((rel_path, original_file))
                                needed_files.add((new_rel_path, original_file))
                                found = True
                                print(f'在其他位置找到文件: {os.path.join(complete_root, original_file)}')
                                break
                        if not found:
                            print(f'[错误] 资源不存在：在完整目录中未找到文件: {original_file}')

    # 扫描+文件夹
    if os.path.exists(plus_dir):
        scan_directory(plus_dir, True)

    # 扫描-文件夹
    if minus_dir and os.path.exists(minus_dir):
        scan_directory(minus_dir, False)

    # 复制需要的文件到原始资源目录
    for rel_path, file in needed_files:
        # 构建源文件和目标文件的完整路径
        if ini_x:
            # 对于ini_x，使用特殊的路径结构
            source_file = os.path.join(complete_dir, 'ini', ini_x, rel_path, file)
            target_file = os.path.join(source_dir, 'ini', ini_x, rel_path, file)
        else:
            source_file = os.path.join(complete_dir, file_type, rel_path, file)
            target_file = os.path.join(source_dir, file_type, rel_path, file)
        
        # 如果源文件存在，复制到目标位置
        if os.path.exists(source_file):
            os.makedirs(os.path.dirname(target_file), exist_ok=True)
            shutil.copy2(source_file, target_file)
            print(f'已复制文件: {source_file} -> {target_file}')
        else:
            print(f'[错误] 资源不存在：在完整目录中未找到文件: {file}')

def process_mapdestination(plus_ini_dir, output_dir, ini_x=None):
    """
    处理mapdestination目录下的文件
    
    Args:
        plus_ini_dir: +ini目录路径
        output_dir: 输出目录路径
        ini_x: ini_x的名称（如'ini_1'），用于特殊处理ini_x类型的文件
    """
    # 可能的mapdestination目录路径
    map_paths = [
        os.path.join(plus_ini_dir, 'mapdestination'),
        os.path.join(plus_ini_dir, '+mapdestination')
    ]
    
    # 目标输出目录
    if ini_x:
        target_dir = os.path.join(output_dir, 'ini', ini_x, 'client', 'debug', 'mapdestination')
    else:
        target_dir = os.path.join(output_dir, 'ini', 'client', 'debug', 'mapdestination')
    
    for map_path in map_paths:
        if os.path.exists(map_path):
            print(f'处理mapdestination目录: {map_path}')
            
            # 确保输出目录存在
            os.makedirs(target_dir, exist_ok=True)
            
            # 遍历目录下的所有文件
            for root, _, files in os.walk(map_path):
                for file in files:
                    # 去掉文件名中的+号（如果有的话）
                    new_filename = file.replace('+', '')
                    source_file = os.path.join(root, file)
                    target_file = os.path.join(target_dir, new_filename)
                    
                    # 复制文件
                    shutil.copy2(source_file, target_file)
                    print(f'已复制文件: {source_file} -> {target_file}')

def process_script_and_ui(game_root, output_dir):
    """
    处理script、ui及其对应的_x目录下的文件
    目录结构：
    - script/
        - script_1/
        - script_2/
    - ui/
        - ui_1/
        - ui_2/

    对于-script和-ui文件夹，会复制到!客户端删除文件夹下，并生成deletelist.txt

    Args:
        game_root: 游戏资源根目录
        output_dir: 输出目录路径
    """
    # 创建!客户端删除目录
    delete_dir = os.path.join(game_root, '!客户端删除')
    delete_list = []  # 存储需要删除的文件列表

    # 需要处理的目录配置
    dir_configs = [
        {
            'names': ['script', '+script'],
            'minus_names': ['-script'],
            'target': 'script',
            'x_pattern': re.compile(r'\+?script_\d+'),
            'minus_x_pattern': re.compile(r'\-script_\d+')
        },
        {
            'names': ['ui', '+ui'],
            'minus_names': ['-ui'],
            'target': 'ui',
            'x_pattern': re.compile(r'\+?ui_\d+'),
            'minus_x_pattern': re.compile(r'\-ui_\d+')
        }
    ]

    for config in dir_configs:
        # 创建基础目标目录
        target_dir = os.path.join(output_dir, config['target'])
        os.makedirs(target_dir, exist_ok=True)

        # 处理正常目录（script, +script, ui, +ui）
        for source_name in config['names']:
            source_path = os.path.join(game_root, source_name)
            if os.path.exists(source_path):
                print(f'处理{source_name}目录: {source_path}')

                # 遍历目录下的所有文件和子目录
                for root, _, files in os.walk(source_path):
                    for file in files:
                        # 去掉文件名中的+号（如果有的话）
                        new_filename = file.replace('+', '')

                        # 保持相对路径结构
                        rel_path = os.path.relpath(root, source_path)
                        source_file = os.path.join(root, file)
                        target_path = os.path.join(target_dir, rel_path)
                        target_file = os.path.join(target_path, new_filename)

                        # 确保目标目录存在
                        os.makedirs(target_path, exist_ok=True)

                        # 复制文件
                        shutil.copy2(source_file, target_file)
                        print(f'已复制文件: {source_file} -> {target_file}')

        # 处理减号目录（-script, -ui）
        for source_name in config['minus_names']:
            source_path = os.path.join(game_root, source_name)
            if os.path.exists(source_path):
                print(f'处理{source_name}目录到!客户端删除: {source_path}')

                # 创建!客户端删除目录
                os.makedirs(delete_dir, exist_ok=True)
                delete_target_dir = os.path.join(delete_dir, config['target'])
                os.makedirs(delete_target_dir, exist_ok=True)

                # 遍历目录下的所有文件和子目录
                for root, _, files in os.walk(source_path):
                    for file in files:
                        # 去掉文件名中的-号
                        new_filename = file.replace('-', '')

                        # 保持相对路径结构
                        rel_path = os.path.relpath(root, source_path)
                        source_file = os.path.join(root, file)
                        delete_target_path = os.path.join(delete_target_dir, rel_path)
                        delete_target_file = os.path.join(delete_target_path, new_filename)

                        # 确保目标目录存在
                        os.makedirs(delete_target_path, exist_ok=True)

                        # 复制文件到!客户端删除目录
                        shutil.copy2(source_file, delete_target_file)
                        print(f'已复制文件到删除目录: {source_file} -> {delete_target_file}')

                        # 添加到删除列表（相对于客户端的路径，使用反斜杠）
                        if rel_path == '.':
                            client_path = f"{config['target']}\\{new_filename}"
                        else:
                            client_path = f"{config['target']}\\{rel_path}\\{new_filename}"
                        # 确保路径分隔符统一为反斜杠
                        client_path = client_path.replace('/', '\\')
                        delete_list.append(client_path)
                        print(f'添加到删除列表: {client_path}')

                # 处理完成后删除原始的-script或-ui文件夹
                try:
                    shutil.rmtree(source_path)
                    print(f'已删除原始文件夹: {source_path}')
                except Exception as e:
                    print(f'删除原始文件夹时发生错误: {source_path}, 错误: {str(e)}')

        # 处理_x目录（正常的+script_x, script_x, +ui_x, ui_x）
        for item in os.listdir(game_root):
            if config['x_pattern'].match(item):
                x_suffix = item.replace('+', '').split('_')[1]  # 获取数字部分
                source_path = os.path.join(game_root, item)
                if os.path.exists(source_path):
                    print(f'处理{item}目录: {source_path}')

                    # 目标输出目录（在基础目录下创建_x目录）
                    target_x_dir = os.path.join(target_dir, f"{config['target']}_{x_suffix}")

                    # 遍历目录下的所有文件和子目录
                    for root, _, files in os.walk(source_path):
                        for file in files:
                            # 去掉文件名中的+号（如果有的话）
                            new_filename = file.replace('+', '')

                            # 保持相对路径结构
                            rel_path = os.path.relpath(root, source_path)
                            source_file = os.path.join(root, file)
                            target_path = os.path.join(target_x_dir, rel_path)
                            target_file = os.path.join(target_path, new_filename)

                            # 确保目标目录存在
                            os.makedirs(target_path, exist_ok=True)

                            # 复制文件
                            shutil.copy2(source_file, target_file)
                            print(f'已复制文件: {source_file} -> {target_file}')

            # 处理减号_x目录（-script_x, -ui_x）
            elif config['minus_x_pattern'].match(item):
                x_suffix = item.replace('-', '').split('_')[1]  # 获取数字部分
                source_path = os.path.join(game_root, item)
                if os.path.exists(source_path):
                    print(f'处理{item}目录到!客户端删除: {source_path}')

                    # 创建!客户端删除目录
                    os.makedirs(delete_dir, exist_ok=True)
                    delete_target_x_dir = os.path.join(delete_dir, config['target'], f"{config['target']}_{x_suffix}")

                    # 遍历目录下的所有文件和子目录
                    for root, _, files in os.walk(source_path):
                        for file in files:
                            # 去掉文件名中的-号
                            new_filename = file.replace('-', '')

                            # 保持相对路径结构
                            rel_path = os.path.relpath(root, source_path)
                            source_file = os.path.join(root, file)
                            delete_target_path = os.path.join(delete_target_x_dir, rel_path)
                            delete_target_file = os.path.join(delete_target_path, new_filename)

                            # 确保目标目录存在
                            os.makedirs(delete_target_path, exist_ok=True)

                            # 复制文件到!客户端删除目录
                            shutil.copy2(source_file, delete_target_file)
                            print(f'已复制文件到删除目录: {source_file} -> {delete_target_file}')

                            # 添加到删除列表（相对于客户端的路径，使用反斜杠）
                            if rel_path == '.':
                                client_path = f"{config['target']}\\{config['target']}_{x_suffix}\\{new_filename}"
                            else:
                                client_path = f"{config['target']}\\{config['target']}_{x_suffix}\\{rel_path}\\{new_filename}"
                            # 确保路径分隔符统一为反斜杠
                            client_path = client_path.replace('/', '\\')
                            delete_list.append(client_path)
                            print(f'添加到删除列表: {client_path}')

                    # 处理完成后删除原始的-script_x或-ui_x文件夹
                    try:
                        shutil.rmtree(source_path)
                        print(f'已删除原始文件夹: {source_path}')
                    except Exception as e:
                        print(f'删除原始文件夹时发生错误: {source_path}, 错误: {str(e)}')

    # 生成deletelist.txt文件
    if delete_list:
        delete_list_file = os.path.join(delete_dir, 'deletelist.txt')
        try:
            with open(delete_list_file, 'w', encoding='utf-8') as f:
                for file_path in delete_list:
                    f.write(file_path + '\n')
            print(f'\n已生成删除列表文件: {delete_list_file}')
            print(f'共包含 {len(delete_list)} 个需要删除的文件')
        except Exception as e:
            print(f'生成删除列表文件时发生错误: {str(e)}')

def generate_size_files(ani_dir, primary_dir, backup_dirs=None):
    """生成.size文件
    
    Args:
        ani_dir: ANI文件目录
        primary_dir: 首选资源目录（客户端更新目录）
        backup_dirs: 备用资源目录列表（完整目录）
    """
    print("\n开始生成.size文件...")
    
    if not backup_dirs:
        backup_dirs = []
    
    def get_image_size(image_path):
        """获取图片尺寸"""
        try:
            with Image.open(image_path) as img:
                return img.size
        except Exception:
            return (0, 0)

    def find_resource_file(frame_path, primary_dir, backup_dirs):
        """查找资源文件，优先在客户端更新目录查找"""
        # 首先在首选目录（客户端更新目录）中查找
        full_frame_path = os.path.join(primary_dir, frame_path.replace('/', os.sep))
        if os.path.exists(full_frame_path):
            return full_frame_path
            
        # 在备用目录（完整目录）中查找
        for backup_dir in backup_dirs:
            # 直接在完整目录中查找
            temp_path = os.path.join(backup_dir, frame_path.replace('/', os.sep))
            if os.path.exists(temp_path):
                return temp_path
                
        return None

    def process_single_file(filename, ani_dir, primary_dir, backup_dirs):
        """处理单个文件"""
        ani_file_path = os.path.join(ani_dir, filename)
        size_file_path = ani_file_path + '.size'
        current_missing_files = []
        
        try:
            # 直接尝试以GBK读取，大多数情况下都是GBK编码
            with open(ani_file_path, 'r', encoding='gbk') as ani_file:
                content = ani_file.read()
        except UnicodeDecodeError:
            try:
                # 如果GBK失败，尝试cp1252
                with open(ani_file_path, 'r', encoding='cp1252') as ani_file:
                    content = ani_file.read()
            except Exception as e:
                print(f"错误：读取文件 {filename} 时发生错误：{str(e)}")
                return None, []
        except Exception as e:
            print(f"错误：读取文件 {filename} 时发生错误：{str(e)}")
            return None, []
            
        try:
            bracket_name = filename[:-4]
            content = content.replace('FrameAmount=1', bracket_name + '\nFrameAmount=1')
            lines = content.splitlines()
            
            size_lines = []
            last_was_frame = False
            frame_paths = []
            
            # 第一遍扫描收集所有需要处理的帧
            for line in lines:
                if line.startswith('Frame') and not line.startswith('FrameAmount'):
                    frame_path = line.split('=')[1].strip()
                    frame_paths.append((line, frame_path))
            
            # 批量处理所有帧
            frame_sizes = {}
            with ThreadPoolExecutor(max_workers=16) as executor:
                future_to_path = {}
                for original_line, frame_path in frame_paths:
                    full_path = find_resource_file(frame_path, primary_dir, backup_dirs)
                    if full_path:
                        future = executor.submit(get_image_size, full_path)
                        future_to_path[future] = (original_line, frame_path)
                    else:
                        current_missing_files.append(original_line)
                        frame_sizes[frame_path] = (0, 0)
                
                # 使用as_completed来更高效地处理完成的任务
                for future in as_completed(future_to_path):
                    original_line, frame_path = future_to_path[future]
                    try:
                        frame_sizes[frame_path] = future.result()
                    except Exception:
                        current_missing_files.append(original_line)
                        frame_sizes[frame_path] = (0, 0)
            
            # 第二遍扫描生成输出文件
            for line in lines:
                if line.startswith('['):
                    if size_lines:
                        size_lines.append('\n')
                    size_lines.append(line + '\n')
                    last_was_frame = False
                elif line.startswith('FrameAmount'):
                    size_lines.append(line + '\n')
                    last_was_frame = False
                elif line.startswith('Frame') and not line.startswith('FrameAmount'):
                    frame_index = line.split('=')[0]
                    frame_path = line.split('=')[1].strip()
                    width, height = frame_sizes.get(frame_path, (0, 0))
                    size_lines.append(f'{frame_index}={width},{height}\n')
                    last_was_frame = True
            
            if not last_was_frame and size_lines:
                size_lines.append('\n')
            
            # 直接写入文件，使用更大的缓冲区
            with open(size_file_path, 'w', encoding='gbk', buffering=65536) as size_file:
                size_file.writelines(size_lines)
            
            return filename if current_missing_files else None, current_missing_files
            
        except Exception as e:
            print(f"错误：处理文件 {filename} 时发生错误：{str(e)}")
            return None, []

    # 获取所有ANI文件
    ani_files = [f for f in os.listdir(ani_dir) if f.endswith('.ani')]
    total_files = len(ani_files)
    missing_files = {}
    
    if total_files == 0:
        print("没有找到需要处理的ANI文件")
        return
    
    print(f"共找到 {total_files} 个ANI文件需要处理")
    start_time = time.time()
    
    # 处理每个文件
    for index, filename in enumerate(ani_files, 1):
        print(f"\r正在处理: {filename} ({index}/{total_files})", end="")
        
        filename_with_missing, current_missing_files = process_single_file(
            filename, ani_dir, primary_dir, backup_dirs
        )
        
        if filename_with_missing and current_missing_files:
            missing_files[filename_with_missing] = current_missing_files
    
    # 处理完成，显示结果
    total_time = time.time() - start_time

    if total_time >= 60:
        minutes = int(total_time // 60)
        seconds = total_time % 60
        time_str = f"{minutes}分{seconds:.1f}秒"
    elif total_time >= 1:
        time_str = f"{total_time:.1f}秒"
    else:
        # 小于1秒时显示毫秒
        milliseconds = total_time * 1000
        time_str = f"{milliseconds:.0f}毫秒"

    print(f"\n\n处理完成！总共耗时: {time_str}")
    
    if missing_files:
        print(f"\n[警告] 发现 {len(missing_files)} 个文件存在缺失资源:")
        for filename, missing in missing_files.items():
            print(f"  文件: {filename}")
            print(f"  缺失的资源文件:")
            for line in missing:
                print(f"    - {line}")
            print("")  # 空行分隔
    else:
        print("\n所有资源文件都已找到！")

def process_game_resources(game_root,complete_dir):
    """
    处理游戏资源的主函数，接收来自Web后端的game_root路径
    
    Args:
        game_root (str): 游戏资源根目录的路径
    """
    # 设置相关目录
    # complete_dir = r'H:\DailyUpdate'  # 完整目录路径，默认为None
    source_dir = os.path.join(game_root, '原始资源')
    plus_ani_dir = os.path.join(game_root, '+ani')
    plus_ini_dir = os.path.join(game_root, '+ini')
    minus_ani_dir = os.path.join(game_root, '-ani')
    minus_ini_dir = os.path.join(game_root, '-ini')
    output_dir = os.path.join(game_root, '客户端更新')

    # 检查是否有任何相关文件夹存在
    relevant_folders = []
    folder_checks = [
        ('+ani', plus_ani_dir),
        ('-ani', minus_ani_dir),
        ('+ini', plus_ini_dir),
        ('-ini', minus_ini_dir),
        ('+script', os.path.join(game_root, '+script')),
        ('-script', os.path.join(game_root, '-script')),
        ('script', os.path.join(game_root, 'script')),
        ('+ui', os.path.join(game_root, '+ui')),
        ('-ui', os.path.join(game_root, '-ui')),
        ('ui', os.path.join(game_root, 'ui'))
    ]

    # 检查ini_x文件夹
    ini_x_pattern = re.compile(r'[\+\-]?ini_\d+')
    script_x_pattern = re.compile(r'[\+\-]?script_\d+')
    ui_x_pattern = re.compile(r'[\+\-]?ui_\d+')

    for item in os.listdir(game_root):
        if ini_x_pattern.match(item) or script_x_pattern.match(item) or ui_x_pattern.match(item):
            folder_checks.append((item, os.path.join(game_root, item)))

    for folder_name, folder_path in folder_checks:
        if os.path.exists(folder_path):
            relevant_folders.append(folder_name)

    if not relevant_folders:
        print("未发现任何相关文件夹（+ani、-ani、+ini、-ini、+script、-script、script、+ui、-ui、ui等），跳过整合处理")
        return

    print(f"发现以下相关文件夹: {', '.join(relevant_folders)}")
    print("开始执行整合处理...")

    # 确保必要的目录存在
    os.makedirs(source_dir, exist_ok=True)
    # 首先生成.size文件
    if os.path.exists(plus_ani_dir) or os.path.exists(minus_ani_dir):
        print("\n第一步：生成.size文件")
        # 构建备用资源目录列表
        backup_dirs = []
        if complete_dir and os.path.exists(complete_dir):
            # 直接添加完整目录
            backup_dirs.append(complete_dir)
        
        # 生成+ani目录的.size文件
        if os.path.exists(plus_ani_dir):
            print("\n处理+ani目录的.size文件:")
            generate_size_files(
                ani_dir=plus_ani_dir,  # +ani目录
                primary_dir=output_dir,  # 客户端更新目录
                backup_dirs=backup_dirs  # 完整目录作为备用
            )
            
        # 生成-ani目录的.size文件
        if os.path.exists(minus_ani_dir):
            print("\n处理-ani目录的.size文件:")
            generate_size_files(
                ani_dir=minus_ani_dir,  # -ani目录
                primary_dir=output_dir,  # 客户端更新目录
                backup_dirs=backup_dirs  # 完整目录作为备用
            )

    # 从完整目录复制需要的原始文件（如果完整目录存在且路径不为空）
    if complete_dir and os.path.exists(complete_dir) and os.path.isdir(complete_dir) and os.listdir(complete_dir):
        print(f'\n第二步：从完整目录复制原始文件')
        print(f'使用完整目录作为源: {complete_dir}')
        # 处理ani文件
        if os.path.exists(plus_ani_dir) or os.path.exists(minus_ani_dir):
            copy_original_files(complete_dir, source_dir, plus_ani_dir, minus_ani_dir, 'ani')
        
        # 处理ini文件
        if os.path.exists(plus_ini_dir) or os.path.exists(minus_ini_dir):
            copy_original_files(complete_dir, source_dir, plus_ini_dir, minus_ini_dir, 'ini')
    else:
        print(f'\n第二步：完整目录路径未指定或不存在，将直接使用原始资源目录进行合并')

    # 处理+ani目录和-ani目录
    print('\n第三步：处理+ani目录和-ani目录')
    has_plus_ani = os.path.exists(plus_ani_dir)
    has_minus_ani = os.path.exists(minus_ani_dir)

    if has_plus_ani or has_minus_ani:
        merge_files(source_dir, plus_ani_dir if has_plus_ani else None, output_dir, 'ani', minus_ani_dir if has_minus_ani else None)
    else:
        print(f'提示：+ani和-ani目录都不存在，跳过处理')

    # 处理+ini目录和-ini目录
    print('\n第四步：处理+ini目录和-ini目录')
    has_plus_ini = os.path.exists(plus_ini_dir)
    has_minus_ini = os.path.exists(minus_ini_dir)

    if has_plus_ini or has_minus_ini:
        merge_files(source_dir, plus_ini_dir if has_plus_ini else None, output_dir, 'ini', minus_ini_dir if has_minus_ini else None)
        # 处理mapdestination目录（只有+ini存在时才处理）
        if has_plus_ini:
            process_mapdestination(plus_ini_dir, output_dir)
    else:
        print(f'提示：+ini和-ini目录都不存在，跳过处理')

    # 处理+ini_x目录
    print('\n第五步：处理+ini_x目录')
    ini_x_pattern = re.compile(r'\+ini_\d+')
    for item in os.listdir(game_root):
        if ini_x_pattern.match(item):
            ini_x = item.replace('+', '')
            plus_ini_x_dir = os.path.join(game_root, item)
            minus_ini_x_dir = os.path.join(game_root, f'-{ini_x}')
            
            print(f'\n处理{item}目录...')
            
            # 从完整目录复制需要的原始文件（如果完整目录存在）
            if complete_dir and os.path.exists(complete_dir):
                copy_original_files(complete_dir, source_dir, plus_ini_x_dir, 
                                minus_ini_x_dir if os.path.exists(minus_ini_x_dir) else None, 
                                'ini', ini_x)
            else:
                print(f'完整目录未指定或不存在，将直接使用原始资源目录进行合并')
            
            # 合并文件
            has_plus_ini_x = os.path.exists(plus_ini_x_dir)
            has_minus_ini_x = os.path.exists(minus_ini_x_dir)

            if has_plus_ini_x or has_minus_ini_x:
                merge_files(source_dir, plus_ini_x_dir if has_plus_ini_x else None, output_dir, 'ini',
                        minus_ini_x_dir if has_minus_ini_x else None,
                        ini_x)  # 添加ini_x参数
                # 处理ini_x的mapdestination目录（只有+ini_x存在时才处理）
                if has_plus_ini_x:
                    process_mapdestination(plus_ini_x_dir, output_dir, ini_x)
            else:
                print(f'提示：{item}和对应的-{ini_x}目录都不存在，跳过处理')

    # 处理script和ui目录
    print('\n第六步：处理script和ui目录')
    process_script_and_ui(game_root, output_dir)

    print('\n所有文件处理完成！')

    print('\n第七步：清理空文件夹并规范化文件名')
    
    def has_files(directory):
        """检查目录是否包含文件（递归检查）"""
        for root, dirs, files in os.walk(directory):
            if files:
                return True
            for dir in dirs:
                if has_files(os.path.join(root, dir)):
                    return True
        return False
    
    def remove_empty_dirs(directory):
        """递归删除空文件夹"""
        for root, dirs, _ in os.walk(directory, topdown=False):
            for dir in dirs:
                dir_path = os.path.join(root, dir)
                if not has_files(dir_path):
                    try:
                        os.rmdir(dir_path)
                        print(f'已删除空文件夹：{dir_path}')
                    except OSError:
                        pass

    def normalize_filename(directory):
        """递归检查并修改大写文件名为小写，跳过根目录下的文件，检查空格"""
        modified_files = []
        files_with_spaces = []
        
        for root, dirs, files in os.walk(directory):
            # 跳过根目录下的文件
            if root == directory:
                continue
                    
            # 处理文件
            for file in files:
                # 检查文件名中的空格
                if ' ' in file:
                    files_with_spaces.append(os.path.join(root, file))
                    continue  # 跳过包含空格的文件
                    
                if any(c.isupper() for c in file):
                    old_path = os.path.join(root, file)
                    new_name = file.lower()
                    new_path = os.path.join(root, new_name)
                    
                    # 如果新文件名与旧文件名相同（忽略大小写），需要使用临时文件名
                    if old_path.lower() == new_path.lower():
                        temp_path = old_path + '.tmp'
                        os.rename(old_path, temp_path)
                        os.rename(temp_path, new_path)
                    else:
                        os.rename(old_path, new_path)
                    
                    modified_files.append((old_path, new_path))
            
            # 处理目录
            for dir in dirs:
                # 检查目录名中的空格
                if ' ' in dir:
                    files_with_spaces.append(os.path.join(root, dir))
                    continue  # 跳过包含空格的目录
                    
                if any(c.isupper() for c in dir):
                    old_path = os.path.join(root, dir)
                    new_name = dir.lower()
                    new_path = os.path.join(root, new_name)
                    
                    if old_path.lower() == new_path.lower():
                        temp_path = old_path + '.tmp'
                        os.rename(old_path, temp_path)
                        os.rename(temp_path, new_path)
                    else:
                        os.rename(old_path, new_path)
                    
                    modified_files.append((old_path, new_path))
        
        # 如果发现包含空格的文件或目录，输出提示
        if files_with_spaces:
            print('\n警告：以下文件或目录名称中包含空格，已跳过处理：')
            for path in files_with_spaces:
                print(f'  {path}')
            print('建议手动修改这些文件或目录的名称，删除其中的空格')
        
        return modified_files

    # 执行清理和规范化
    try:
        # 首先规范化文件名
        modified_files = normalize_filename(output_dir)
        if modified_files:
            print('\n以下文件已被转换为小写：')
            for old_path, new_path in modified_files:
                print(f'从：{old_path}\n到：{new_path}\n')
        else:
            print('未发现需要转换的大写文件名')

        # 然后删除空文件夹
        remove_empty_dirs(output_dir)
        print('空文件夹清理完成')
        
    except Exception as e:
        print(f'清理和规范化过程中出错：{e}')

    # 从配置文件读取需要加密的ini文件列表
    config = configparser.ConfigParser()
    config.read('merge_rules.ini', encoding='utf-8')
    required_ini_files = []
    if config.has_section('Encryption'):
        required_ini_files = [f.strip() for f in config.get('Encryption', 'required_files', fallback='').split(',') if f.strip()]
    
    if not required_ini_files:
        print('\n警告：未在merge_rules.ini中找到需要加密的文件列表，跳过加密操作')
    else:
        print('\n第八步：执行WdbManager加密操作')
        # 检查客户端更新ini目录下是否存在任意一个所需的ini文件
        ini_output_dir = os.path.join(output_dir, 'ini')
        has_required_ini = False
        if os.path.exists(ini_output_dir):
            for ini_file in required_ini_files:
                if os.path.exists(os.path.join(ini_output_dir, ini_file)):
                    has_required_ini = True
                    break

        # 如果存在完整目录且客户端更新ini目录下有所需的ini文件，执行WdbManager加密操作
        if complete_dir and os.path.exists(complete_dir) and has_required_ini:
            # 复制更新的ini文件到完整目录
            ini_output_dir = os.path.join(output_dir, 'ini')
            complete_ini_dir = os.path.join(complete_dir, 'ini')
            if os.path.exists(ini_output_dir):
                for root, _, files in os.walk(ini_output_dir):
                    for file in files:
                        if file.endswith('.ini'):
                            src_file = os.path.join(root, file)
                            rel_path = os.path.relpath(src_file, ini_output_dir)
                            dst_file = os.path.join(complete_ini_dir, rel_path)
                            os.makedirs(os.path.dirname(dst_file), exist_ok=True)
                            shutil.copy2(src_file, dst_file)
            
            # 运行WdbManager.exe
            wdb_manager_path = os.path.join(complete_dir, 'WdbManager.exe')
            if os.path.exists(wdb_manager_path):
                try:
                    # 启动WdbManager.exe
                    startupinfo = subprocess.STARTUPINFO()
                    startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                    startupinfo.wShowWindow = SW_SHOWMINIMIZED  # 直接使用SW_SHOWMINIMIZED
                    process = subprocess.Popen([wdb_manager_path], startupinfo=startupinfo)
                    
                    # 等待程序启动并连接到窗口
                    app = Application(backend='win32').connect(process=process.pid, timeout=10)
                    
                    # 获取主窗口并保持最小化
                    main_window = app.window(title='C3游戏引擎ini资源配置检查和打包工具_1.1')
                    main_window.wait('ready', timeout=10)
                    main_window.minimize()  # 确保窗口最小化
                    
                    # 点击保存按钮
                    save_button = main_window.child_window(title="保存")
                    if save_button.exists():
                        save_button.click()
                        print('已自动点击保存按钮')
                        time.sleep(2)  # 等待保存完成
                        
                        # 关闭WdbManager
                        if main_window.exists():
                            main_window.close()
                            print('WdbManager加密操作已完成')
                            
                            # 复制c3.wdb文件到客户端更新目录
                            complete_wdb_path = os.path.join(complete_dir, 'ini', 'c3.wdb')
                            output_wdb_path = os.path.join(output_dir, 'ini', 'c3.wdb')
                            if os.path.exists(complete_wdb_path):
                                os.makedirs(os.path.dirname(output_wdb_path), exist_ok=True)
                                shutil.copy2(complete_wdb_path, output_wdb_path)
                                print('已将c3.wdb文件复制到客户端更新目录')
                            else:
                                print('警告：未找到c3.wdb文件')
                    else:
                        print('未找到保存按钮，请手动点击保存')
                        
                except Exception as e:
                    print(f'自动点击保存按钮时出错：{e}')
                    print('请手动打开WdbManager.exe并点击保存按钮')
            else:
                print(f'警告：未找到WdbManager.exe，跳过加密操作') 

if __name__ == '__main__':
    # 原有的主程序代码保持不变
    game_root = r'F:\Users\administrator\appdata\roaming\91UU\921024\RecvFile\王齐超_317217\!特色服整合ini_2(1)'
    complete_dir = r'H:\DailyUpdate'  # 完整目录路径，默认为None
    process_game_resources(game_root,complete_dir)

def execute_from_web(folder_path,guanfang_dir):
    """
    从网页端接收文件夹路径并执行整包操作
    
    Args:
        folder_path (str): 从网页端传递过来的文件夹路径
        guanfang_dir (str): 完整目录路径
    """
    try:
        process_game_resources(folder_path,guanfang_dir)
        return True, "整包处理完成"
    except Exception as e:
        return False, f"整包处理失败：{str(e)}"
