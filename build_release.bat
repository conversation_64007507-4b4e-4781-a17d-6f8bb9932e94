@echo off
setlocal enabledelayedexpansion

echo.
echo ========================================
echo 资管工具平台统一发布构建工具
echo ========================================
echo.

:: 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python环境
    echo 请确保已安装Python并添加到PATH环境变量
    pause
    exit /b 1
)

:: 检查构建脚本
if not exist "build_release.py" (
    echo 错误: 找不到构建脚本 build_release.py
    pause
    exit /b 1
)

:: 运行构建脚本
echo 启动构建流程...
echo.
python build_release.py

:: 检查构建结果
if errorlevel 1 (
    echo.
    echo 构建过程中出现错误
    echo 请检查上方的错误信息
) else (
    echo.
    echo 构建流程完成
    echo 请查看 "发布包" 目录中的输出文件
)

echo.
echo 按任意键退出...
pause >nul
