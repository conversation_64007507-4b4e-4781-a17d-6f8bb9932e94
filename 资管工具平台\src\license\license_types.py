#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
许可证类型定义
"""

from enum import Enum
from dataclasses import dataclass
from typing import List, Optional
from datetime import datetime, timedelta


class LicenseType(Enum):
    """许可证类型枚举"""
    FREE = "free"
    PROFESSIONAL = "professional"
    ENTERPRISE = "enterprise"
    TRIAL = "trial"


class LicenseStatus(Enum):
    """许可证状态枚举"""
    VALID = "valid"
    EXPIRED = "expired"
    INVALID = "invalid"
    TRIAL_EXPIRED = "trial_expired"
    NOT_ACTIVATED = "not_activated"


@dataclass
class LicenseFeature:
    """许可证功能定义"""
    name: str
    display_name: str
    description: str
    enabled: bool = False


@dataclass
class LicenseInfo:
    """许可证信息"""
    license_type: LicenseType
    activation_code: str
    expire_date: Optional[datetime]
    hardware_id: str
    features: List[str]
    user_name: str = ""
    company_name: str = ""
    email: str = ""
    max_devices: int = 1
    created_date: Optional[datetime] = None
    last_validation: Optional[datetime] = None


class LicenseFeatures:
    """许可证功能配置"""
    
    # 定义所有可用功能
    ALL_FEATURES = {
        "archive_processor": LicenseFeature(
            "archive_processor",
            "压缩包处理",
            "智能压缩包版本管理和批量处理"
        ),
        "package_merger": LicenseFeature(
            "package_merger", 
            "整包工具",
            "文件合并和整包处理功能"
        ),
        "svn_uploader": LicenseFeature(
            "svn_uploader",
            "SVN上传",
            "SVN检出和文件上传功能"
        ),
        "themes": LicenseFeature(
            "themes",
            "主题切换",
            "多种界面主题和个性化设置"
        ),
        "auto_update": LicenseFeature(
            "auto_update",
            "自动更新",
            "应用程序自动更新功能"
        ),
        "advanced_settings": LicenseFeature(
            "advanced_settings",
            "高级设置",
            "高级配置和自定义选项"
        ),
        "batch_processing": LicenseFeature(
            "batch_processing",
            "批量处理",
            "大批量文件处理能力"
        ),
        "technical_support": LicenseFeature(
            "technical_support",
            "技术支持",
            "专业技术支持服务"
        ),
        "custom_rules": LicenseFeature(
            "custom_rules",
            "自定义规则",
            "自定义文件处理规则"
        ),
        "export_reports": LicenseFeature(
            "export_reports",
            "导出报告",
            "处理结果导出和报告生成"
        )
    }
    
    # 不同许可证类型的功能配置
    LICENSE_FEATURES = {
        LicenseType.FREE: [
            "archive_processor",  # 限制功能
        ],
        LicenseType.TRIAL: [
            "archive_processor",
            "package_merger",
            "svn_uploader",
            "themes"
        ],
        LicenseType.PROFESSIONAL: [
            "archive_processor",
            "package_merger", 
            "svn_uploader",
            "themes",
            "auto_update",
            "advanced_settings",
            "batch_processing",
            "technical_support"
        ],
        LicenseType.ENTERPRISE: [
            "archive_processor",
            "package_merger",
            "svn_uploader", 
            "themes",
            "auto_update",
            "advanced_settings",
            "batch_processing",
            "technical_support",
            "custom_rules",
            "export_reports"
        ]
    }
    
    # 功能限制配置
    FEATURE_LIMITS = {
        LicenseType.FREE: {
            "max_files_per_batch": 100,
            "max_archive_size_mb": 100,
            "max_concurrent_tasks": 1
        },
        LicenseType.TRIAL: {
            "max_files_per_batch": 500,
            "max_archive_size_mb": 500,
            "max_concurrent_tasks": 2,
            "trial_days": 30
        },
        LicenseType.PROFESSIONAL: {
            "max_files_per_batch": 10000,
            "max_archive_size_mb": 10000,
            "max_concurrent_tasks": 5
        },
        LicenseType.ENTERPRISE: {
            "max_files_per_batch": -1,  # 无限制
            "max_archive_size_mb": -1,  # 无限制
            "max_concurrent_tasks": 10
        }
    }
    
    @classmethod
    def get_features_for_license(cls, license_type: LicenseType) -> List[LicenseFeature]:
        """获取指定许可证类型的功能列表"""
        feature_names = cls.LICENSE_FEATURES.get(license_type, [])
        features = []
        
        for name in feature_names:
            if name in cls.ALL_FEATURES:
                feature = cls.ALL_FEATURES[name]
                feature.enabled = True
                features.append(feature)
        
        return features
    
    @classmethod
    def is_feature_enabled(cls, license_type: LicenseType, feature_name: str) -> bool:
        """检查指定功能是否在许可证中启用"""
        return feature_name in cls.LICENSE_FEATURES.get(license_type, [])
    
    @classmethod
    def get_feature_limit(cls, license_type: LicenseType, limit_name: str) -> int:
        """获取功能限制值"""
        limits = cls.FEATURE_LIMITS.get(license_type, {})
        return limits.get(limit_name, 0)
    
    @classmethod
    def get_license_display_name(cls, license_type: LicenseType) -> str:
        """获取许可证类型显示名称"""
        names = {
            LicenseType.FREE: "免费版",
            LicenseType.TRIAL: "试用版", 
            LicenseType.PROFESSIONAL: "专业版",
            LicenseType.ENTERPRISE: "企业版"
        }
        return names.get(license_type, "未知")
    
    @classmethod
    def get_license_description(cls, license_type: LicenseType) -> str:
        """获取许可证类型描述"""
        descriptions = {
            LicenseType.FREE: "基础功能，适合个人用户",
            LicenseType.TRIAL: "30天试用，体验完整功能",
            LicenseType.PROFESSIONAL: "完整功能，适合专业用户",
            LicenseType.ENTERPRISE: "企业级功能，适合团队使用"
        }
        return descriptions.get(license_type, "")


def create_trial_license(hardware_id: str, user_name: str = "", email: str = "") -> LicenseInfo:
    """创建试用许可证"""
    expire_date = datetime.now() + timedelta(days=30)
    
    return LicenseInfo(
        license_type=LicenseType.TRIAL,
        activation_code="TRIAL-" + hardware_id[:8].upper(),
        expire_date=expire_date,
        hardware_id=hardware_id,
        features=LicenseFeatures.LICENSE_FEATURES[LicenseType.TRIAL],
        user_name=user_name,
        email=email,
        created_date=datetime.now(),
        last_validation=datetime.now()
    )


def create_free_license(hardware_id: str) -> LicenseInfo:
    """创建免费许可证"""
    return LicenseInfo(
        license_type=LicenseType.FREE,
        activation_code="FREE-" + hardware_id[:8].upper(),
        expire_date=None,  # 永不过期
        hardware_id=hardware_id,
        features=LicenseFeatures.LICENSE_FEATURES[LicenseType.FREE],
        created_date=datetime.now(),
        last_validation=datetime.now()
    )
