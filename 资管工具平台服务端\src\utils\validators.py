#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证工具模块
"""

import re
from flask import request
from typing import Dict, List, Any


def validate_json(request_obj, required_fields: List[str] = None) -> Dict[str, Any]:
    """验证JSON请求数据"""
    if not request_obj.is_json:
        raise ValueError("请求必须是JSON格式")
    
    data = request_obj.get_json()
    if not data:
        raise ValueError("请求数据不能为空")
    
    if required_fields:
        missing_fields = []
        for field in required_fields:
            if field not in data or data[field] is None:
                missing_fields.append(field)
        
        if missing_fields:
            raise ValueError(f"缺少必需字段: {', '.join(missing_fields)}")
    
    return data


def validate_activation_code(code: str) -> bool:
    """验证激活码格式"""
    if not code:
        return False
    
    # 移除连字符
    clean_code = code.replace('-', '')
    
    # 检查长度和字符
    if len(clean_code) != 26:  # 4+4+4+4+4+4+2 = 26
        return False
    
    # 检查是否只包含字母和数字
    if not re.match(r'^[A-Z0-9]+$', clean_code.upper()):
        return False
    
    return True


def validate_version(version: str) -> bool:
    """验证版本号格式"""
    if not version:
        return False
    
    # 支持语义化版本号格式: x.y.z 或 x.y.z-beta.1
    pattern = r'^\d+\.\d+\.\d+(-[a-zA-Z0-9]+(\.\d+)?)?$'
    return bool(re.match(pattern, version))


def validate_email(email: str) -> bool:
    """验证邮箱格式"""
    if not email:
        return False
    
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))


def validate_hardware_id(hardware_id: str) -> bool:
    """验证硬件ID格式"""
    if not hardware_id:
        return False
    
    # 硬件ID应该是32位十六进制字符串
    pattern = r'^[a-fA-F0-9]{32}$'
    return bool(re.match(pattern, hardware_id))


def validate_platform(platform: str) -> bool:
    """验证平台名称"""
    valid_platforms = ['windows', 'linux', 'macos', 'android', 'ios']
    return platform.lower() in valid_platforms


def validate_architecture(arch: str) -> bool:
    """验证架构名称"""
    valid_archs = ['x86', 'x64', 'arm', 'arm64']
    return arch.lower() in valid_archs


def sanitize_filename(filename: str) -> str:
    """清理文件名"""
    if not filename:
        return ""
    
    # 移除危险字符
    filename = re.sub(r'[<>:"/\\|?*]', '', filename)
    
    # 移除控制字符
    filename = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', filename)
    
    # 限制长度
    if len(filename) > 255:
        name, ext = filename.rsplit('.', 1) if '.' in filename else (filename, '')
        max_name_len = 255 - len(ext) - 1 if ext else 255
        filename = name[:max_name_len] + ('.' + ext if ext else '')
    
    return filename.strip()


def validate_file_size(file_size: int, max_size: int = 100 * 1024 * 1024) -> bool:
    """验证文件大小"""
    return 0 < file_size <= max_size


def validate_license_type(license_type: str) -> bool:
    """验证许可证类型"""
    valid_types = ['free', 'trial', 'professional', 'enterprise']
    return license_type.lower() in valid_types
