#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
许可证管理器
"""

import os
import json
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from PyQt6.QtCore import QObject, pyqtSignal

from .license_types import LicenseType, LicenseStatus, LicenseInfo, LicenseFeatures, create_trial_license, create_free_license
from .activation_code import ActivationCodeValidator
from .crypto_utils import SecureStorage, generate_hardware_id
from .license_validator import LicenseValidator


class LicenseManager(QObject):
    """许可证管理器"""
    
    # 信号定义
    license_changed = pyqtSignal(LicenseInfo)  # 许可证变更信号
    license_expired = pyqtSignal()  # 许可证过期信号
    license_warning = pyqtSignal(int)  # 许可证警告信号（剩余天数）
    
    def __init__(self, config_dir: str = None, config_manager=None):
        super().__init__()

        # 配置目录
        if config_dir is None:
            config_dir = os.path.join(os.path.expanduser("~"), ".resource_management_tool")

        self.config_dir = config_dir
        self.license_file = os.path.join(config_dir, "license.dat")
        self.config_manager = config_manager

        # 初始化组件
        self.validator = ActivationCodeValidator()
        self.storage = SecureStorage(self.license_file, "ResourceMgmtTool2024")

        # 初始化服务端验证器
        self.server_validator = None
        self._init_server_validator()

        # 当前许可证信息
        self._current_license: Optional[LicenseInfo] = None
        self._hardware_id = generate_hardware_id()

        # 加载许可证
        self.load_license()
    
    @property
    def current_license(self) -> Optional[LicenseInfo]:
        """获取当前许可证"""
        return self._current_license
    
    @property
    def hardware_id(self) -> str:
        """获取硬件ID"""
        return self._hardware_id

    def _init_server_validator(self):
        """初始化服务端验证器"""
        try:
            if self.config_manager:
                # 使用正确的配置访问方法
                enabled = self.config_manager.get_config('license_server.enabled', False)
                if enabled:
                    server_url = self.config_manager.get_config('license_server.server_url', 'http://localhost:5000')
                    api_key = self.config_manager.get_config('license_server.api_key', 'client-api-key-123')

                    self.server_validator = LicenseValidator(server_url, api_key)
                    print(f"✅ 服务端验证器初始化成功: {server_url}")
                else:
                    print("📱 使用本地验证模式")
            else:
                print("⚠️ 配置管理器未提供，使用本地验证")
        except Exception as e:
            print(f"❌ 服务端验证器初始化失败: {e}")
            self.server_validator = None
    
    def load_license(self) -> bool:
        """加载许可证"""
        try:
            data = self.storage.load_data()
            if data is None:
                # 首次运行，创建免费许可证
                self._current_license = create_free_license(self._hardware_id)
                self.save_license()
                return True
            
            # 解析许可证数据
            license_info = self._parse_license_data(data)
            if license_info is None:
                # 数据损坏，重新创建免费许可证
                self._current_license = create_free_license(self._hardware_id)
                self.save_license()
                return True
            
            # 验证许可证
            if self._validate_license(license_info):
                self._current_license = license_info
                self._check_license_status()
                return True
            else:
                # 验证失败，降级到免费版
                self._current_license = create_free_license(self._hardware_id)
                self.save_license()
                return False
                
        except Exception as e:
            print(f"加载许可证失败: {e}")
            # 出错时创建免费许可证
            self._current_license = create_free_license(self._hardware_id)
            self.save_license()
            return False
    
    def save_license(self) -> bool:
        """保存许可证"""
        if self._current_license is None:
            return False
        
        try:
            data = self._serialize_license_data(self._current_license)
            return self.storage.save_data(data)
        except Exception as e:
            print(f"保存许可证失败: {e}")
            return False
    
    def activate_license(self, activation_code: str, user_info: Dict[str, str] = None) -> tuple[bool, str]:
        """激活许可证"""
        try:
            # 优先使用服务端验证
            if self.server_validator:
                print(f"🌐 使用服务端验证激活码: {activation_code}")
                success, result = self.server_validator.validate_activation_code(activation_code)

                if success:
                    # 服务端验证成功，创建许可证信息
                    license_data = result.get('license_info', {})

                    # 转换许可证类型
                    license_type_str = license_data.get('license_type', 'trial')
                    if license_type_str == 'trial':
                        license_type = LicenseType.TRIAL
                    elif license_type_str == 'standard':
                        license_type = LicenseType.PROFESSIONAL  # 映射到专业版
                    elif license_type_str == 'professional':
                        license_type = LicenseType.PROFESSIONAL
                    elif license_type_str == 'enterprise':
                        license_type = LicenseType.ENTERPRISE
                    else:
                        license_type = LicenseType.TRIAL

                    # 创建许可证信息
                    # 获取功能列表（转换为字符串列表）
                    feature_objects = LicenseFeatures.get_features_for_license(license_type)
                    feature_names = [f.name for f in feature_objects]

                    license_info = LicenseInfo(
                        license_type=license_type,
                        activation_code=activation_code,
                        expire_date=datetime.fromisoformat(license_data.get('expires_at', '2025-12-31 23:59:59').replace(' ', 'T')),
                        hardware_id=self._hardware_id,
                        features=feature_names,
                        user_name=user_info.get('user_name', '') if user_info else '',
                        email=user_info.get('email', '') if user_info else '',
                        company_name=user_info.get('company_name', '') if user_info else '',
                        max_devices=license_data.get('max_devices', 1),
                        created_date=datetime.now(),
                        last_validation=datetime.now()
                    )

                    # 保存许可证
                    self._current_license = license_info
                    if self.save_license():
                        self.license_changed.emit(license_info)
                        return True, f"激活成功！许可证类型：{license_type.value}"
                    else:
                        return False, "保存许可证失败"
                else:
                    error_msg = result.get('message', '激活码验证失败')
                    print(f"❌ 服务端验证失败: {error_msg}")
                    return False, error_msg

            # 回退到本地验证
            print(f"📱 使用本地验证激活码: {activation_code}")
            if user_info:
                license_info = self.validator.validate_and_extract_license_with_user_info(
                    activation_code,
                    user_info.get('user_name', ''),
                    user_info.get('email', ''),
                    user_info.get('company_name', ''),
                    self._hardware_id
                )
            else:
                license_info = self.validator.validate_and_extract_license(activation_code, self._hardware_id)

            if license_info is None:
                return False, "激活码无效或格式错误"

            # 检查硬件绑定
            if not self.validator.check_hardware_binding(license_info, self._hardware_id):
                # 更新硬件ID绑定
                license_info.hardware_id = self._hardware_id

            # 更新验证时间
            license_info.last_validation = datetime.now()

            # 保存许可证
            self._current_license = license_info
            if self.save_license():
                self.license_changed.emit(license_info)
                return True, f"激活成功！许可证类型：{license_info.license_type.value}"
            else:
                return False, "保存许可证失败"

        except Exception as e:
            print(f"❌ 激活异常: {e}")
            return False, f"激活失败: {str(e)}"
    
    def deactivate_license(self) -> bool:
        """停用许可证（降级到免费版）"""
        try:
            self._current_license = create_free_license(self._hardware_id)
            if self.save_license():
                self.license_changed.emit(self._current_license)
                return True
            return False
        except Exception:
            return False
    
    def start_trial(self, user_info: Dict[str, str] = None) -> tuple[bool, str]:
        """开始试用"""
        try:
            # 检查是否已经试用过
            if self._has_trial_history():
                return False, "该设备已使用过试用版"
            
            # 创建试用许可证
            user_name = user_info.get('user_name', '') if user_info else ''
            email = user_info.get('email', '') if user_info else ''
            
            trial_license = create_trial_license(self._hardware_id, user_name, email)
            
            # 保存许可证
            self._current_license = trial_license
            if self.save_license():
                # 记录试用历史
                self._record_trial_history()
                self.license_changed.emit(trial_license)
                return True, "试用版激活成功"
            else:
                return False, "保存许可证失败"
                
        except Exception as e:
            return False, f"启动试用失败: {str(e)}"
    
    def get_license_status(self) -> LicenseStatus:
        """获取许可证状态"""
        if self._current_license is None:
            return LicenseStatus.NOT_ACTIVATED
        
        # 检查是否过期
        if self.validator.is_license_expired(self._current_license):
            if self._current_license.license_type == LicenseType.TRIAL:
                return LicenseStatus.TRIAL_EXPIRED
            else:
                return LicenseStatus.EXPIRED
        
        # 检查硬件绑定
        if not self.validator.check_hardware_binding(self._current_license, self._hardware_id):
            return LicenseStatus.INVALID
        
        return LicenseStatus.VALID
    
    def is_feature_enabled(self, feature_name: str) -> bool:
        """检查功能是否启用"""
        if self._current_license is None:
            return False
        
        # 检查许可证状态
        status = self.get_license_status()
        if status not in [LicenseStatus.VALID]:
            # 过期或无效时只允许基础功能
            return feature_name in LicenseFeatures.LICENSE_FEATURES[LicenseType.FREE]
        
        return LicenseFeatures.is_feature_enabled(self._current_license.license_type, feature_name)
    
    def get_feature_limit(self, limit_name: str) -> int:
        """获取功能限制"""
        if self._current_license is None:
            return LicenseFeatures.get_feature_limit(LicenseType.FREE, limit_name)
        
        status = self.get_license_status()
        if status not in [LicenseStatus.VALID]:
            return LicenseFeatures.get_feature_limit(LicenseType.FREE, limit_name)
        
        return LicenseFeatures.get_feature_limit(self._current_license.license_type, limit_name)
    
    def get_days_until_expiry(self) -> Optional[int]:
        """获取距离过期的天数"""
        if self._current_license is None:
            return None
        
        return self.validator.get_days_until_expiry(self._current_license)
    
    def _parse_license_data(self, data: Dict[str, Any]) -> Optional[LicenseInfo]:
        """解析许可证数据"""
        try:
            license_type = LicenseType(data.get('license_type'))
            expire_date = None
            if data.get('expire_date'):
                expire_date = datetime.fromisoformat(data['expire_date'])
            
            created_date = None
            if data.get('created_date'):
                created_date = datetime.fromisoformat(data['created_date'])
            
            last_validation = None
            if data.get('last_validation'):
                last_validation = datetime.fromisoformat(data['last_validation'])
            
            return LicenseInfo(
                license_type=license_type,
                activation_code=data.get('activation_code', ''),
                expire_date=expire_date,
                hardware_id=data.get('hardware_id', ''),
                features=data.get('features', []),
                user_name=data.get('user_name', ''),
                company_name=data.get('company_name', ''),
                email=data.get('email', ''),
                max_devices=data.get('max_devices', 1),
                created_date=created_date,
                last_validation=last_validation
            )
        except Exception:
            return None
    
    def _serialize_license_data(self, license_info: LicenseInfo) -> Dict[str, Any]:
        """序列化许可证数据"""
        data = {
            'license_type': license_info.license_type.value,
            'activation_code': license_info.activation_code,
            'hardware_id': license_info.hardware_id,
            'features': license_info.features,
            'user_name': license_info.user_name,
            'company_name': license_info.company_name,
            'email': license_info.email,
            'max_devices': license_info.max_devices
        }
        
        if license_info.expire_date:
            data['expire_date'] = license_info.expire_date.isoformat()
        
        if license_info.created_date:
            data['created_date'] = license_info.created_date.isoformat()
        
        if license_info.last_validation:
            data['last_validation'] = license_info.last_validation.isoformat()
        
        return data
    
    def _validate_license(self, license_info: LicenseInfo) -> bool:
        """验证许可证"""
        # 基础验证
        if license_info.license_type not in [LicenseType.FREE, LicenseType.TRIAL, 
                                           LicenseType.PROFESSIONAL, LicenseType.ENTERPRISE]:
            return False
        
        # 硬件绑定验证
        if license_info.hardware_id != self._hardware_id:
            return False
        
        return True
    
    def _check_license_status(self):
        """检查许可证状态并发送相应信号"""
        if self._current_license is None:
            return
        
        days_left = self.get_days_until_expiry()
        
        if days_left is not None:
            if days_left <= 0:
                self.license_expired.emit()
            elif days_left <= 7:
                self.license_warning.emit(days_left)
    
    def _has_trial_history(self) -> bool:
        """检查是否有试用历史"""
        trial_file = os.path.join(self.config_dir, ".trial_history")
        return os.path.exists(trial_file)
    
    def _record_trial_history(self):
        """记录试用历史"""
        try:
            trial_file = os.path.join(self.config_dir, ".trial_history")
            os.makedirs(self.config_dir, exist_ok=True)
            
            with open(trial_file, 'w') as f:
                f.write(datetime.now().isoformat())
        except Exception:
            pass
