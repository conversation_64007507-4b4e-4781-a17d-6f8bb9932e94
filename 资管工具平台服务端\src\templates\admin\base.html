<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}激活码管理系统{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
        }
        .sidebar .nav-link {
            color: #adb5bd;
        }
        .sidebar .nav-link:hover {
            color: #fff;
        }
        .sidebar .nav-link.active {
            color: #fff;
            background-color: #495057;
        }
        .main-content {
            margin-left: 0;
        }
        @media (min-width: 768px) {
            .main-content {
                margin-left: 250px;
            }
        }
        .status-badge {
            font-size: 0.75rem;
        }
        .code-display {
            font-family: 'Courier New', monospace;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="text-white">激活码管理</h5>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'admin.dashboard' %}active{% endif %}" 
                               href="{{ url_for('admin.dashboard') }}">
                                <i class="bi bi-speedometer2"></i> 仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'admin.manage_codes' %}active{% endif %}" 
                               href="{{ url_for('admin.manage_codes') }}">
                                <i class="bi bi-key"></i> 激活码管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'admin.active_licenses' %}active{% endif %}" 
                               href="{{ url_for('admin.active_licenses') }}">
                                <i class="bi bi-shield-check"></i> 活跃许可证
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="generateCodes()">
                                <i class="bi bi-plus-circle"></i> 生成激活码
                            </a>
                        </li>
                        <li class="nav-item mt-3">
                            <a class="nav-link text-danger" href="{{ url_for('admin.logout') }}">
                                <i class="bi bi-box-arrow-right"></i> 退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">{% block page_title %}激活码管理系统{% endblock %}</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        {% block page_actions %}{% endblock %}
                    </div>
                </div>

                <!-- 消息提示 -->
                <div id="message-container"></div>

                <!-- 页面内容 -->
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    <!-- 生成激活码模态框 -->
    <div class="modal fade" id="generateModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">生成激活码</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="generateForm">
                        <div class="mb-3">
                            <label for="count" class="form-label">生成数量</label>
                            <input type="number" class="form-control" id="count" min="1" max="100" value="1" required>
                        </div>
                        <div class="mb-3">
                            <label for="licenseType" class="form-label">许可证类型</label>
                            <select class="form-select" id="licenseType" required>
                                <option value="trial">试用版 (30天)</option>
                                <option value="standard">标准版</option>
                                <option value="professional">专业版</option>
                                <option value="enterprise">企业版</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="maxDevices" class="form-label">最大设备数</label>
                            <input type="number" class="form-control" id="maxDevices" min="1" max="100" value="1" required>
                        </div>
                        <div class="mb-3">
                            <label for="validityDays" class="form-label">有效期 (天)</label>
                            <input type="number" class="form-control" id="validityDays" min="1" max="3650" value="365" required>
                        </div>
                        <div class="mb-3">
                            <label for="notes" class="form-label">备注</label>
                            <textarea class="form-control" id="notes" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="submitGenerate()">生成</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 显示消息
        function showMessage(message, type = 'info') {
            const container = document.getElementById('message-container');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type} alert-dismissible fade show`;
            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            container.appendChild(alert);
            
            // 3秒后自动消失
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 3000);
        }

        // 生成激活码
        function generateCodes() {
            const modal = new bootstrap.Modal(document.getElementById('generateModal'));
            modal.show();
        }

        // 提交生成请求
        function submitGenerate() {
            const form = document.getElementById('generateForm');
            const formData = new FormData(form);
            
            const data = {
                count: parseInt(document.getElementById('count').value),
                license_type: document.getElementById('licenseType').value,
                max_devices: parseInt(document.getElementById('maxDevices').value),
                validity_days: parseInt(document.getElementById('validityDays').value),
                notes: document.getElementById('notes').value
            };

            fetch('/admin/codes/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    showMessage(result.message, 'success');
                    bootstrap.Modal.getInstance(document.getElementById('generateModal')).hide();
                    // 刷新页面或更新列表
                    if (window.location.pathname.includes('/codes')) {
                        location.reload();
                    }
                } else {
                    showMessage(result.error || '生成失败', 'danger');
                }
            })
            .catch(error => {
                showMessage('网络错误: ' + error.message, 'danger');
            });
        }

        // 更新激活码状态
        function updateCodeStatus(code, status) {
            if (!confirm(`确定要将激活码 ${code} 设置为 ${status} 状态吗？`)) {
                return;
            }

            fetch(`/admin/codes/${code}/status`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({status: status})
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    showMessage(result.message, 'success');
                    location.reload();
                } else {
                    showMessage(result.error || '更新失败', 'danger');
                }
            })
            .catch(error => {
                showMessage('网络错误: ' + error.message, 'danger');
            });
        }

        // 延长有效期
        function extendValidity(code) {
            const days = prompt('请输入要延长的天数:', '30');
            if (!days || isNaN(days) || parseInt(days) <= 0) {
                return;
            }

            fetch(`/admin/codes/${code}/extend`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({days: parseInt(days)})
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    showMessage(result.message, 'success');
                    location.reload();
                } else {
                    showMessage(result.error || '延长失败', 'danger');
                }
            })
            .catch(error => {
                showMessage('网络错误: ' + error.message, 'danger');
            });
        }
    </script>
    {% block scripts %}{% endblock %}
</body>
</html>
