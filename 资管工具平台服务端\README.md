# 资管工具平台服务端

这是资管工具平台的独立服务端项目，提供更新服务和激活码在线验证服务。

## 📁 项目结构

```
资管工具平台服务端/
├── README.md                    # 项目说明
├── requirements.txt             # Python依赖
├── docker-compose.yml          # Docker部署配置
├── config/                     # 配置文件
│   ├── config.yaml            # 主配置文件
│   ├── nginx.conf             # Nginx配置
│   └── ssl/                   # SSL证书
├── src/                       # 源代码
│   ├── __init__.py
│   ├── app.py                 # Flask应用主文件
│   ├── config.py              # 配置管理
│   ├── database.py            # 数据库管理
│   ├── auth/                  # 认证模块
│   │   ├── __init__.py
│   │   ├── middleware.py      # 认证中间件
│   │   └── utils.py           # 认证工具
│   ├── license/               # 激活码服务
│   │   ├── __init__.py
│   │   ├── models.py          # 数据模型
│   │   ├── routes.py          # API路由
│   │   ├── validator.py       # 验证器
│   │   └── crypto.py          # 加密工具
│   ├── update/                # 更新服务
│   │   ├── __init__.py
│   │   ├── models.py          # 数据模型
│   │   ├── routes.py          # API路由
│   │   ├── version.py         # 版本管理
│   │   └── file_manager.py    # 文件管理
│   └── utils/                 # 工具模块
│       ├── __init__.py
│       ├── logger.py          # 日志工具
│       ├── response.py        # 响应工具
│       └── validators.py      # 验证工具
├── data/                      # 数据目录
│   ├── database/              # 数据库文件
│   ├── uploads/               # 上传文件
│   └── logs/                  # 日志文件
├── scripts/                   # 部署脚本
│   ├── deploy.sh              # 部署脚本
│   ├── backup.sh              # 备份脚本
│   └── init_db.py             # 数据库初始化
├── tests/                     # 测试文件
│   ├── __init__.py
│   ├── test_license.py        # 激活码测试
│   └── test_update.py         # 更新服务测试
└── docs/                      # 文档
    ├── api.md                 # API文档
    ├── deployment.md          # 部署文档
    └── development.md         # 开发文档
```

## 🎯 功能特性

### 激活码在线验证服务
- ✅ 激活码验证API
- ✅ 许可证状态查询
- ✅ 硬件绑定验证
- ✅ 使用统计记录
- ✅ 管理后台界面

### 软件更新服务
- ✅ 版本检查API
- ✅ 更新包下载
- ✅ 增量更新支持
- ✅ 版本管理后台
- ✅ 下载统计分析

## 🚀 快速开始

### 开发环境
```bash
# 1. 克隆项目
git clone <repository-url>
cd 资管工具平台服务端

# 2. 安装依赖
pip install -r requirements.txt

# 3. 初始化数据库
python scripts/init_db.py

# 4. 启动开发服务器
python src/app.py
```

### 生产部署
```bash
# 使用Docker Compose部署
docker-compose up -d
```

## 📚 API文档

### 激活码验证API
- `POST /api/license/validate` - 验证激活码
- `GET /api/license/status` - 查询许可证状态
- `POST /api/license/deactivate` - 停用许可证

### 更新服务API
- `GET /api/update/check` - 检查更新
- `GET /api/update/download` - 下载更新包
- `GET /api/update/changelog` - 获取更新日志

## 🔧 配置说明

主要配置文件位于 `config/config.yaml`，包含：
- 数据库配置
- 服务端口配置
- 安全密钥配置
- 日志配置

## 📊 监控和日志

- 访问日志：`data/logs/access.log`
- 错误日志：`data/logs/error.log`
- 应用日志：`data/logs/app.log`

## 🛡️ 安全特性

- HTTPS强制加密
- API密钥认证
- 请求频率限制
- SQL注入防护
- XSS攻击防护

## 📞 技术支持

如有问题，请联系技术支持团队。
