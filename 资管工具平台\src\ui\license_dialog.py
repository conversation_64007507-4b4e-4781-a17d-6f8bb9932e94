#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
许可证管理对话框
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTabWidget,
                            QWidget, QLabel, QLineEdit, QPushButton, QTextEdit,
                            QGroupBox, QGridLayout, QMessageBox, QFrame,
                            QSpacerItem, QSizePolicy, QListWidget, QListWidgetItem)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QIcon, QPixmap

from license.license_manager import LicenseManager
from license.license_types import LicenseType, LicenseFeatures
from .activation_dialog import ActivationDialog
from .license_status_widget import LicenseStatusWidget


class LicenseDialog(QDialog):
    """许可证管理对话框"""
    
    # 信号定义
    license_updated = pyqtSignal()  # 许可证更新信号
    
    def __init__(self, license_manager: LicenseManager, parent=None):
        super().__init__(parent)
        self.license_manager = license_manager
        self.init_ui()
        self.setup_connections()
        self.load_license_info()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("许可证管理")
        self.setModal(True)
        self.setFixedSize(850, 800)  # 进一步增加窗口大小
        
        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(30, 30, 30, 30)  # 增加边距
        layout.setSpacing(25)  # 增加间距
        
        # 创建标题区域
        self.create_header(layout)
        
        # 创建标签页
        self.create_tabs(layout)
        
        # 创建按钮区域
        self.create_buttons(layout)
    
    def create_header(self, layout):
        """创建标题区域"""
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(15, 15, 15, 15)
        
        # 许可证图标
        icon_label = QLabel()
        try:
            icon_label.setPixmap(QIcon("resources/icons/app_icon.png").pixmap(48, 48))
        except:
            icon_label.setText("📜")
            icon_label.setFont(QFont("Arial", 24))
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(icon_label)
        
        # 标题信息
        title_layout = QVBoxLayout()
        
        title_label = QLabel("许可证管理")
        title_label.setFont(QFont("Microsoft YaHei UI", 16, QFont.Weight.Bold))
        title_layout.addWidget(title_label)
        
        subtitle_label = QLabel("管理您的软件许可证和功能权限")
        subtitle_label.setFont(QFont("Microsoft YaHei UI", 9))
        subtitle_label.setStyleSheet("color: #666666;")
        title_layout.addWidget(subtitle_label)
        
        header_layout.addLayout(title_layout)
        header_layout.addStretch()
        
        layout.addWidget(header_frame)
    
    def create_tabs(self, layout):
        """创建标签页"""
        self.tab_widget = QTabWidget()
        
        # 许可证状态标签页
        self.create_status_tab()
        
        # 功能权限标签页
        self.create_features_tab()
        
        # 许可证历史标签页
        self.create_history_tab()
        
        layout.addWidget(self.tab_widget)
    
    def create_status_tab(self):
        """创建状态标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        
        # 添加许可证状态组件
        self.status_widget = LicenseStatusWidget(self.license_manager)
        self.status_widget.activation_requested.connect(self.show_activation_dialog)
        self.status_widget.upgrade_requested.connect(self.show_activation_dialog)
        layout.addWidget(self.status_widget)
        
        # 许可证详细信息组
        details_group = QGroupBox("许可证详细信息")
        details_group.setFont(QFont("Microsoft YaHei UI", 12))  # 增加组标题字体
        details_layout = QGridLayout(details_group)
        details_layout.setSpacing(12)  # 增加间距

        # 激活码
        activation_label = QLabel("激活码:")
        activation_label.setFont(QFont("Microsoft YaHei UI", 11))
        details_layout.addWidget(activation_label, 0, 0)
        self.activation_code_label = QLabel("未激活")
        self.activation_code_label.setFont(QFont("Consolas", 11))  # 增加字体大小
        details_layout.addWidget(self.activation_code_label, 0, 1)

        # 硬件ID
        hardware_label = QLabel("硬件ID:")
        hardware_label.setFont(QFont("Microsoft YaHei UI", 11))
        details_layout.addWidget(hardware_label, 1, 0)
        self.hardware_id_label = QLabel(self.license_manager.hardware_id)
        self.hardware_id_label.setFont(QFont("Consolas", 10))  # 增加字体大小
        self.hardware_id_label.setStyleSheet("color: #666666; padding: 2px;")
        details_layout.addWidget(self.hardware_id_label, 1, 1)

        # 许可证类型
        type_label = QLabel("许可证类型:")
        type_label.setFont(QFont("Microsoft YaHei UI", 11))
        details_layout.addWidget(type_label, 2, 0)
        self.license_type_detail_label = QLabel("--")
        self.license_type_detail_label.setFont(QFont("Microsoft YaHei UI", 11, QFont.Weight.Bold))
        details_layout.addWidget(self.license_type_detail_label, 2, 1)

        # 到期时间
        expiry_label = QLabel("到期时间:")
        expiry_label.setFont(QFont("Microsoft YaHei UI", 11))
        details_layout.addWidget(expiry_label, 3, 0)
        self.expiry_date_label = QLabel("--")
        self.expiry_date_label.setFont(QFont("Microsoft YaHei UI", 11))
        details_layout.addWidget(self.expiry_date_label, 3, 1)

        # 创建时间
        created_label = QLabel("创建时间:")
        created_label.setFont(QFont("Microsoft YaHei UI", 11))
        details_layout.addWidget(created_label, 4, 0)
        self.created_date_label = QLabel("--")
        self.created_date_label.setFont(QFont("Microsoft YaHei UI", 11))
        details_layout.addWidget(self.created_date_label, 4, 1)

        # 最后验证时间
        validation_label = QLabel("最后验证:")
        validation_label.setFont(QFont("Microsoft YaHei UI", 11))
        details_layout.addWidget(validation_label, 5, 0)
        self.last_validation_label = QLabel("--")
        self.last_validation_label.setFont(QFont("Microsoft YaHei UI", 11))
        details_layout.addWidget(self.last_validation_label, 5, 1)
        
        layout.addWidget(details_group)
        
        self.tab_widget.addTab(widget, "许可证状态")
    
    def create_features_tab(self):
        """创建功能权限标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        
        # 功能列表
        features_group = QGroupBox("功能权限详情")
        features_layout = QVBoxLayout(features_group)
        
        self.features_list = QListWidget()
        features_layout.addWidget(self.features_list)
        
        layout.addWidget(features_group)
        
        # 版本对比
        comparison_group = QGroupBox("版本功能对比")
        comparison_layout = QVBoxLayout(comparison_group)
        
        comparison_text = QTextEdit()
        comparison_text.setReadOnly(True)
        comparison_text.setMaximumHeight(200)
        comparison_text.setHtml(self._get_version_comparison_html())
        comparison_layout.addWidget(comparison_text)
        
        layout.addWidget(comparison_group)
        
        self.tab_widget.addTab(widget, "功能权限")
    
    def create_history_tab(self):
        """创建历史标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        
        # 操作历史
        history_group = QGroupBox("许可证操作历史")
        history_layout = QVBoxLayout(history_group)
        
        self.history_list = QListWidget()
        history_layout.addWidget(self.history_list)
        
        layout.addWidget(history_group)
        
        # 系统信息
        system_group = QGroupBox("系统信息")
        system_layout = QGridLayout(system_group)
        
        import platform
        system_layout.addWidget(QLabel("操作系统:"), 0, 0)
        system_layout.addWidget(QLabel(platform.system() + " " + platform.release()), 0, 1)
        
        system_layout.addWidget(QLabel("处理器:"), 1, 0)
        system_layout.addWidget(QLabel(platform.processor() or "未知"), 1, 1)
        
        system_layout.addWidget(QLabel("架构:"), 2, 0)
        system_layout.addWidget(QLabel(platform.machine()), 2, 1)
        
        layout.addWidget(system_group)
        
        self.tab_widget.addTab(widget, "历史记录")
    
    def create_buttons(self, layout):
        """创建按钮区域"""
        button_layout = QHBoxLayout()
        
        self.activate_button = QPushButton("激活许可证")
        self.activate_button.setMinimumSize(120, 35)
        button_layout.addWidget(self.activate_button)
        
        self.deactivate_button = QPushButton("停用许可证")
        self.deactivate_button.setMinimumSize(120, 35)
        button_layout.addWidget(self.deactivate_button)
        
        button_layout.addStretch()
        
        self.refresh_button = QPushButton("刷新")
        self.refresh_button.setMinimumSize(80, 35)
        button_layout.addWidget(self.refresh_button)
        
        self.close_button = QPushButton("关闭")
        self.close_button.setMinimumSize(80, 35)
        button_layout.addWidget(self.close_button)
        
        layout.addLayout(button_layout)
    
    def setup_connections(self):
        """设置信号连接"""
        self.activate_button.clicked.connect(self.show_activation_dialog)
        self.deactivate_button.clicked.connect(self.deactivate_license)
        self.refresh_button.clicked.connect(self.load_license_info)
        self.close_button.clicked.connect(self.accept)
        
        # 连接许可证管理器信号
        self.license_manager.license_changed.connect(self.on_license_changed)
    
    def load_license_info(self):
        """加载许可证信息"""
        license_info = self.license_manager.current_license
        
        if license_info:
            # 更新详细信息
            self.activation_code_label.setText(license_info.activation_code)

            # 许可证类型
            type_names = {
                LicenseType.FREE: "免费版",
                LicenseType.TRIAL: "试用版",
                LicenseType.PROFESSIONAL: "专业版",
                LicenseType.ENTERPRISE: "企业版"
            }
            type_name = type_names.get(license_info.license_type, "未知")
            self.license_type_detail_label.setText(type_name)

            # 到期时间
            try:
                if license_info.expire_date:
                    self.expiry_date_label.setText(license_info.expire_date.strftime("%Y-%m-%d %H:%M:%S"))
                else:
                    self.expiry_date_label.setText("永久有效")
            except AttributeError:
                self.expiry_date_label.setText("永久有效")

            if license_info.created_date:
                self.created_date_label.setText(license_info.created_date.strftime("%Y-%m-%d %H:%M:%S"))

            if license_info.last_validation:
                self.last_validation_label.setText(license_info.last_validation.strftime("%Y-%m-%d %H:%M:%S"))

            # 更新按钮状态
            self.deactivate_button.setEnabled(license_info.license_type != LicenseType.FREE)
        else:
            self.activation_code_label.setText("未激活")
            self.license_type_detail_label.setText("--")
            self.expiry_date_label.setText("--")
            self.created_date_label.setText("--")
            self.last_validation_label.setText("--")
            self.deactivate_button.setEnabled(False)
        
        # 更新功能列表
        self.update_features_list()
        
        # 更新历史记录
        self.update_history_list()
    
    def update_features_list(self):
        """更新功能列表"""
        self.features_list.clear()
        
        license_info = self.license_manager.current_license
        if not license_info:
            return
        
        # 获取所有功能
        all_features = LicenseFeatures.ALL_FEATURES
        enabled_features = LicenseFeatures.LICENSE_FEATURES.get(license_info.license_type, [])
        
        for feature_key, feature_info in all_features.items():
            item = QListWidgetItem()
            
            # 设置图标和文本
            if feature_key in enabled_features:
                item.setText(f"✅ {feature_info.display_name}")
                item.setToolTip(f"{feature_info.description} - 已启用")
            else:
                item.setText(f"❌ {feature_info.display_name}")
                item.setToolTip(f"{feature_info.description} - 未启用")
            
            self.features_list.addItem(item)
    
    def update_history_list(self):
        """更新历史记录列表"""
        self.history_list.clear()
        
        # 添加一些示例历史记录
        history_items = [
            "应用程序启动 - " + self.license_manager.current_license.created_date.strftime("%Y-%m-%d %H:%M:%S") if self.license_manager.current_license and self.license_manager.current_license.created_date else "应用程序启动",
            "许可证验证成功",
            "功能权限检查完成"
        ]
        
        for item_text in history_items:
            item = QListWidgetItem(item_text)
            self.history_list.addItem(item)
    
    def show_activation_dialog(self):
        """显示激活对话框"""
        dialog = ActivationDialog(self.license_manager, self)
        dialog.activation_success.connect(self.on_activation_success)
        dialog.exec()
    
    def deactivate_license(self):
        """停用许可证"""
        reply = QMessageBox.question(
            self, "确认停用",
            "确定要停用当前许可证吗？\n这将使应用程序降级到免费版功能。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            success = self.license_manager.deactivate_license()
            if success:
                QMessageBox.information(self, "成功", "许可证已停用")
                self.license_updated.emit()
            else:
                QMessageBox.warning(self, "失败", "停用许可证失败")
    
    def on_activation_success(self):
        """激活成功处理"""
        self.load_license_info()
        self.license_updated.emit()
    
    def on_license_changed(self, license_info):
        """许可证变更处理"""
        self.load_license_info()
    
    def _get_version_comparison_html(self) -> str:
        """获取版本对比HTML"""
        return """
        <table border="1" cellpadding="8" cellspacing="0" style="width: 100%; border-collapse: collapse;">
        <tr style="background-color: #f0f0f0; font-weight: bold;">
            <th>功能</th>
            <th>免费版</th>
            <th>试用版</th>
            <th>专业版</th>
            <th>企业版</th>
        </tr>
        <tr>
            <td>压缩包处理</td>
            <td style="color: orange;">✓ 限制</td>
            <td style="color: green;">✓ 完整</td>
            <td style="color: green;">✓ 完整</td>
            <td style="color: green;">✓ 完整</td>
        </tr>
        <tr>
            <td>整包工具</td>
            <td style="color: red;">✗</td>
            <td style="color: green;">✓</td>
            <td style="color: green;">✓</td>
            <td style="color: green;">✓</td>
        </tr>
        <tr>
            <td>SVN上传</td>
            <td style="color: red;">✗</td>
            <td style="color: green;">✓</td>
            <td style="color: green;">✓</td>
            <td style="color: green;">✓</td>
        </tr>
        <tr>
            <td>主题切换</td>
            <td style="color: red;">✗</td>
            <td style="color: green;">✓</td>
            <td style="color: green;">✓</td>
            <td style="color: green;">✓</td>
        </tr>
        <tr>
            <td>自动更新</td>
            <td style="color: red;">✗</td>
            <td style="color: green;">✓</td>
            <td style="color: green;">✓</td>
            <td style="color: green;">✓</td>
        </tr>
        <tr>
            <td>技术支持</td>
            <td style="color: red;">✗</td>
            <td style="color: red;">✗</td>
            <td style="color: green;">✓</td>
            <td style="color: green;">✓</td>
        </tr>
        <tr>
            <td>自定义规则</td>
            <td style="color: red;">✗</td>
            <td style="color: red;">✗</td>
            <td style="color: red;">✗</td>
            <td style="color: green;">✓</td>
        </tr>
        <tr>
            <td>使用期限</td>
            <td>永久</td>
            <td>30天</td>
            <td>1年</td>
            <td>1年</td>
        </tr>
        </table>
        """
