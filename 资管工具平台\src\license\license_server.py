#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
许可证服务器模拟器
用于在线验证和管理许可证
"""

import json
import hashlib
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from pathlib import Path

from .license_types import LicenseType, LicenseStatus
from .crypto_utils import CryptoUtils


@dataclass
class ServerLicenseRecord:
    """服务器端许可证记录"""
    license_id: str
    activation_code: str
    license_type: str
    user_name: str
    email: str
    company_name: str
    hardware_id: str
    created_date: str
    expire_date: Optional[str]
    last_validation: str
    validation_count: int
    is_active: bool
    max_activations: int = 1


class LicenseServer:
    """许可证服务器模拟器"""
    
    def __init__(self, db_path: str = None):
        if db_path is None:
            db_path = Path(__file__).parent.parent.parent / "data" / "license_server.db"
        
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        self.crypto = CryptoUtils()
        self.init_database()
    
    def init_database(self):
        """初始化数据库"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 创建许可证表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS licenses (
                    license_id TEXT PRIMARY KEY,
                    activation_code TEXT UNIQUE NOT NULL,
                    license_type TEXT NOT NULL,
                    user_name TEXT,
                    email TEXT,
                    company_name TEXT,
                    hardware_id TEXT,
                    created_date TEXT NOT NULL,
                    expire_date TEXT,
                    last_validation TEXT,
                    validation_count INTEGER DEFAULT 0,
                    is_active BOOLEAN DEFAULT 1,
                    max_activations INTEGER DEFAULT 1
                )
            ''')
            
            # 创建验证日志表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS validation_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    license_id TEXT,
                    hardware_id TEXT,
                    validation_time TEXT,
                    ip_address TEXT,
                    user_agent TEXT,
                    result TEXT,
                    FOREIGN KEY (license_id) REFERENCES licenses (license_id)
                )
            ''')
            
            # 创建更新记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS update_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    license_id TEXT,
                    from_version TEXT,
                    to_version TEXT,
                    update_time TEXT,
                    update_type TEXT,
                    FOREIGN KEY (license_id) REFERENCES licenses (license_id)
                )
            ''')
            
            conn.commit()
    
    def register_license(self, license_data: Dict[str, Any]) -> tuple[bool, str]:
        """注册新许可证"""
        try:
            license_id = self._generate_license_id()
            
            record = ServerLicenseRecord(
                license_id=license_id,
                activation_code=license_data['activation_code'],
                license_type=license_data['license_type'],
                user_name=license_data.get('user_name', ''),
                email=license_data.get('email', ''),
                company_name=license_data.get('company_name', ''),
                hardware_id=license_data.get('hardware_id', ''),
                created_date=datetime.now().isoformat(),
                expire_date=license_data.get('expire_date'),
                last_validation=datetime.now().isoformat(),
                validation_count=0,
                is_active=True,
                max_activations=license_data.get('max_activations', 1)
            )
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 检查激活码是否已存在
                cursor.execute(
                    "SELECT license_id FROM licenses WHERE activation_code = ?",
                    (record.activation_code,)
                )
                
                if cursor.fetchone():
                    return False, "激活码已存在"
                
                # 插入新记录
                cursor.execute('''
                    INSERT INTO licenses (
                        license_id, activation_code, license_type, user_name,
                        email, company_name, hardware_id, created_date,
                        expire_date, last_validation, validation_count,
                        is_active, max_activations
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    record.license_id, record.activation_code, record.license_type,
                    record.user_name, record.email, record.company_name,
                    record.hardware_id, record.created_date, record.expire_date,
                    record.last_validation, record.validation_count,
                    record.is_active, record.max_activations
                ))
                
                conn.commit()
                
                return True, f"许可证注册成功: {license_id}"
                
        except Exception as e:
            return False, f"注册失败: {str(e)}"
    
    def validate_license(self, activation_code: str, hardware_id: str, 
                        client_info: Dict[str, Any] = None) -> Dict[str, Any]:
        """验证许可证"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 查找许可证
                cursor.execute('''
                    SELECT * FROM licenses WHERE activation_code = ?
                ''', (activation_code,))
                
                row = cursor.fetchone()
                if not row:
                    return {
                        'valid': False,
                        'error': 'INVALID_CODE',
                        'message': '激活码无效'
                    }
                
                # 解析记录
                columns = [desc[0] for desc in cursor.description]
                record = dict(zip(columns, row))
                
                # 检查许可证是否激活
                if not record['is_active']:
                    return {
                        'valid': False,
                        'error': 'DEACTIVATED',
                        'message': '许可证已被停用'
                    }
                
                # 检查硬件绑定
                if record['hardware_id'] and record['hardware_id'] != hardware_id:
                    return {
                        'valid': False,
                        'error': 'HARDWARE_MISMATCH',
                        'message': '硬件ID不匹配'
                    }
                
                # 检查过期时间
                if record['expire_date']:
                    expire_date = datetime.fromisoformat(record['expire_date'])
                    if datetime.now() > expire_date:
                        return {
                            'valid': False,
                            'error': 'EXPIRED',
                            'message': '许可证已过期'
                        }
                
                # 更新验证记录
                cursor.execute('''
                    UPDATE licenses 
                    SET last_validation = ?, validation_count = validation_count + 1,
                        hardware_id = ?
                    WHERE license_id = ?
                ''', (datetime.now().isoformat(), hardware_id, record['license_id']))
                
                # 记录验证日志
                self._log_validation(
                    cursor, record['license_id'], hardware_id, 
                    'SUCCESS', client_info
                )
                
                conn.commit()
                
                # 返回验证结果
                return {
                    'valid': True,
                    'license_info': {
                        'license_id': record['license_id'],
                        'license_type': record['license_type'],
                        'user_name': record['user_name'],
                        'email': record['email'],
                        'company_name': record['company_name'],
                        'expire_date': record['expire_date'],
                        'created_date': record['created_date']
                    }
                }
                
        except Exception as e:
            return {
                'valid': False,
                'error': 'SERVER_ERROR',
                'message': f'服务器错误: {str(e)}'
            }
    
    def deactivate_license(self, activation_code: str, reason: str = '') -> tuple[bool, str]:
        """停用许可证"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    UPDATE licenses SET is_active = 0 
                    WHERE activation_code = ?
                ''', (activation_code,))
                
                if cursor.rowcount == 0:
                    return False, "许可证不存在"
                
                conn.commit()
                return True, "许可证已停用"
                
        except Exception as e:
            return False, f"停用失败: {str(e)}"
    
    def get_license_info(self, activation_code: str) -> Optional[Dict[str, Any]]:
        """获取许可证信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT * FROM licenses WHERE activation_code = ?
                ''', (activation_code,))
                
                row = cursor.fetchone()
                if not row:
                    return None
                
                columns = [desc[0] for desc in cursor.description]
                return dict(zip(columns, row))
                
        except Exception:
            return None
    
    def get_validation_logs(self, license_id: str, limit: int = 100) -> List[Dict[str, Any]]:
        """获取验证日志"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT * FROM validation_logs 
                    WHERE license_id = ? 
                    ORDER BY validation_time DESC 
                    LIMIT ?
                ''', (license_id, limit))
                
                rows = cursor.fetchall()
                columns = [desc[0] for desc in cursor.description]
                
                return [dict(zip(columns, row)) for row in rows]
                
        except Exception:
            return []
    
    def get_license_statistics(self) -> Dict[str, Any]:
        """获取许可证统计信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 总许可证数
                cursor.execute("SELECT COUNT(*) FROM licenses")
                total_licenses = cursor.fetchone()[0]
                
                # 活跃许可证数
                cursor.execute("SELECT COUNT(*) FROM licenses WHERE is_active = 1")
                active_licenses = cursor.fetchone()[0]
                
                # 按类型统计
                cursor.execute('''
                    SELECT license_type, COUNT(*) 
                    FROM licenses 
                    GROUP BY license_type
                ''')
                type_stats = dict(cursor.fetchall())
                
                # 今日验证次数
                today = datetime.now().date().isoformat()
                cursor.execute('''
                    SELECT COUNT(*) FROM validation_logs 
                    WHERE DATE(validation_time) = ?
                ''', (today,))
                today_validations = cursor.fetchone()[0]
                
                return {
                    'total_licenses': total_licenses,
                    'active_licenses': active_licenses,
                    'type_statistics': type_stats,
                    'today_validations': today_validations
                }
                
        except Exception:
            return {}
    
    def _generate_license_id(self) -> str:
        """生成许可证ID"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        random_part = hashlib.md5(str(datetime.now().timestamp()).encode()).hexdigest()[:8]
        return f"LIC-{timestamp}-{random_part.upper()}"
    
    def _log_validation(self, cursor, license_id: str, hardware_id: str, 
                       result: str, client_info: Dict[str, Any] = None):
        """记录验证日志"""
        cursor.execute('''
            INSERT INTO validation_logs (
                license_id, hardware_id, validation_time, 
                ip_address, user_agent, result
            ) VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            license_id, hardware_id, datetime.now().isoformat(),
            client_info.get('ip_address', '') if client_info else '',
            client_info.get('user_agent', '') if client_info else '',
            result
        ))


class LicenseServerAPI:
    """许可证服务器API接口"""
    
    def __init__(self, server: LicenseServer):
        self.server = server
    
    def handle_validation_request(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理验证请求"""
        activation_code = request_data.get('activation_code')
        hardware_id = request_data.get('hardware_id')
        client_info = request_data.get('client_info', {})
        
        if not activation_code or not hardware_id:
            return {
                'success': False,
                'error': 'MISSING_PARAMETERS',
                'message': '缺少必要参数'
            }
        
        result = self.server.validate_license(activation_code, hardware_id, client_info)
        
        return {
            'success': result['valid'],
            'data': result if result['valid'] else None,
            'error': result.get('error') if not result['valid'] else None,
            'message': result.get('message', '')
        }
    
    def handle_registration_request(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理注册请求"""
        try:
            success, message = self.server.register_license(request_data)
            
            return {
                'success': success,
                'message': message
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': 'SERVER_ERROR',
                'message': f'服务器错误: {str(e)}'
            }
    
    def handle_info_request(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理信息查询请求"""
        activation_code = request_data.get('activation_code')
        
        if not activation_code:
            return {
                'success': False,
                'error': 'MISSING_PARAMETERS',
                'message': '缺少激活码参数'
            }
        
        info = self.server.get_license_info(activation_code)
        
        if info:
            return {
                'success': True,
                'data': info
            }
        else:
            return {
                'success': False,
                'error': 'NOT_FOUND',
                'message': '许可证不存在'
            }


# 示例用法和测试
def demo_license_server():
    """演示许可证服务器功能"""
    print("🖥️ 许可证服务器演示")
    print("=" * 50)
    
    # 创建服务器实例
    server = LicenseServer()
    api = LicenseServerAPI(server)
    
    # 注册测试许可证
    test_license = {
        'activation_code': '0003-01Z0-FBQU-FBQM-9PBE-X005-9A',
        'license_type': 'professional',
        'user_name': '测试用户',
        'email': '<EMAIL>',
        'company_name': '测试公司',
        'expire_date': (datetime.now() + timedelta(days=365)).isoformat()
    }
    
    print("📝 注册许可证:")
    success, message = server.register_license(test_license)
    print(f"   结果: {message}")
    
    if success:
        # 验证许可证
        print("\n🔍 验证许可证:")
        result = server.validate_license(
            test_license['activation_code'],
            'TEST-HARDWARE-ID-12345'
        )
        print(f"   验证结果: {'✅ 成功' if result['valid'] else '❌ 失败'}")
        
        if result['valid']:
            license_info = result['license_info']
            print(f"   许可证ID: {license_info['license_id']}")
            print(f"   许可证类型: {license_info['license_type']}")
            print(f"   用户: {license_info['user_name']}")
    
    # 获取统计信息
    print("\n📊 服务器统计:")
    stats = server.get_license_statistics()
    print(f"   总许可证数: {stats.get('total_licenses', 0)}")
    print(f"   活跃许可证: {stats.get('active_licenses', 0)}")
    print(f"   今日验证次数: {stats.get('today_validations', 0)}")


if __name__ == "__main__":
    demo_license_server()
