#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
激活码生成脚本
用于生成测试用的激活码
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from license.activation_code import ActivationCodeGenerator
from license.license_types import LicenseType
from datetime import datetime, timedelta


def generate_test_activation_codes():
    """生成测试激活码"""
    generator = ActivationCodeGenerator()
    
    print("=" * 60)
    print("资管工具平台 - 测试激活码生成器")
    print("=" * 60)
    print()
    
    # 定义测试许可证
    test_licenses = [
        {
            'name': '试用版',
            'license_type': LicenseType.TRIAL,
            'user_name': '试用用户',
            'email': '<EMAIL>',
            'features': ['archive_processor', 'package_merger', 'svn_uploader', 'themes'],
            'expire_date': (datetime.now() + timedelta(days=30)).isoformat(),
            'description': '30天试用，包含所有基础功能'
        },
        {
            'name': '专业版',
            'license_type': LicenseType.PROFESSIONAL,
            'user_name': '专业用户',
            'email': '<EMAIL>',
            'features': ['archive_processor', 'package_merger', 'svn_uploader', 'themes', 'auto_update', 'advanced_settings'],
            'expire_date': (datetime.now() + timedelta(days=365)).isoformat(),
            'description': '1年有效期，包含所有专业功能'
        },
        {
            'name': '企业版',
            'license_type': LicenseType.ENTERPRISE,
            'user_name': '企业用户',
            'email': '<EMAIL>',
            'features': ['archive_processor', 'package_merger', 'svn_uploader', 'themes', 'auto_update', 'advanced_settings', 'custom_rules', 'export_reports'],
            'expire_date': (datetime.now() + timedelta(days=365)).isoformat(),
            'description': '1年有效期，包含所有企业级功能'
        }
    ]
    
    # 生成激活码
    for license_data in test_licenses:
        print(f"📋 {license_data['name']}")
        print(f"   描述: {license_data['description']}")
        
        # 生成激活码
        activation_code = generator.generate_activation_code(license_data)
        
        if activation_code:
            print(f"   激活码: {activation_code}")
            print(f"   用户: {license_data['user_name']}")
            print(f"   邮箱: {license_data['email']}")
            print(f"   到期: {license_data['expire_date'][:10]}")
        else:
            print(f"   ❌ 生成失败")
        
        print()
    
    print("=" * 60)
    print("使用说明:")
    print("1. 复制上面的激活码")
    print("2. 启动资管工具平台")
    print("3. 点击菜单 '工具' -> '软件激活'")
    print("4. 输入激活码并点击激活")
    print("=" * 60)
    print()
    
    # 验证激活码
    print("🔍 激活码验证测试:")
    print("-" * 40)
    
    from license.activation_code import ActivationCodeValidator
    validator = ActivationCodeValidator()
    
    for license_data in test_licenses:
        activation_code = generator.generate_activation_code(license_data)
        if activation_code:
            result = validator.validate_and_extract_license(activation_code)
            if result:
                print(f"✅ {license_data['name']}: 验证成功")
                print(f"   许可证类型: {result.license_type.value}")
                print(f"   到期时间: {result.expire_date}")
            else:
                print(f"❌ {license_data['name']}: 验证失败")
        print()


def generate_custom_activation_code():
    """生成自定义激活码"""
    print("=" * 60)
    print("自定义激活码生成器")
    print("=" * 60)
    
    # 获取用户输入
    print("请选择许可证类型:")
    print("1. 试用版 (30天)")
    print("2. 专业版 (1年)")
    print("3. 企业版 (1年)")
    
    try:
        choice = input("请输入选择 (1-3): ").strip()
        
        license_type_map = {
            '1': LicenseType.TRIAL,
            '2': LicenseType.PROFESSIONAL,
            '3': LicenseType.ENTERPRISE
        }
        
        if choice not in license_type_map:
            print("❌ 无效选择")
            return
        
        license_type = license_type_map[choice]
        
        # 获取用户信息
        user_name = input("用户名 (可选): ").strip()
        email = input("邮箱 (可选): ").strip()
        
        # 设置到期时间
        if license_type == LicenseType.TRIAL:
            expire_date = datetime.now() + timedelta(days=30)
        else:
            expire_date = datetime.now() + timedelta(days=365)
        
        # 构建许可证数据
        license_data = {
            'license_type': license_type,
            'user_name': user_name,
            'email': email,
            'expire_date': expire_date.isoformat()
        }
        
        # 生成激活码
        generator = ActivationCodeGenerator()
        activation_code = generator.generate_activation_code(license_data)
        
        if activation_code:
            print()
            print("✅ 激活码生成成功!")
            print(f"激活码: {activation_code}")
            print(f"许可证类型: {license_type.value}")
            print(f"用户名: {user_name or '未设置'}")
            print(f"邮箱: {email or '未设置'}")
            print(f"到期时间: {expire_date.strftime('%Y-%m-%d')}")
        else:
            print("❌ 激活码生成失败")
            
    except KeyboardInterrupt:
        print("\n操作已取消")
    except Exception as e:
        print(f"❌ 生成失败: {e}")


def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == '--custom':
        generate_custom_activation_code()
    else:
        generate_test_activation_codes()


if __name__ == "__main__":
    main()
