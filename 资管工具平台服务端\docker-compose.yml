version: '3.8'

services:
  # 主应用服务
  app:
    build: .
    container_name: resource_mgmt_server
    restart: unless-stopped
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=sqlite:///data/database/app.db
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-here}
      - JWT_SECRET=${JWT_SECRET:-your-jwt-secret-here}
    volumes:
      - ./data:/app/data
      - ./config:/app/config
      - ./logs:/app/logs
    ports:
      - "5000:5000"
    depends_on:
      - redis
    networks:
      - app-network

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: resource_mgmt_redis
    restart: unless-stopped
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - app-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: resource_mgmt_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf
      - ./config/ssl:/etc/nginx/ssl
      - ./data/uploads:/var/www/uploads
    depends_on:
      - app
    networks:
      - app-network

  # 数据库备份服务
  backup:
    build:
      context: .
      dockerfile: Dockerfile.backup
    container_name: resource_mgmt_backup
    restart: unless-stopped
    environment:
      - BACKUP_SCHEDULE=0 2 * * *  # 每天凌晨2点备份
    volumes:
      - ./data:/app/data
      - ./backups:/app/backups
    depends_on:
      - app
    networks:
      - app-network

  # 监控服务（可选）
  prometheus:
    image: prom/prometheus:latest
    container_name: resource_mgmt_prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - app-network

  # 日志收集（可选）
  grafana:
    image: grafana/grafana:latest
    container_name: resource_mgmt_grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
    depends_on:
      - prometheus
    networks:
      - app-network

volumes:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  app-network:
    driver: bridge
