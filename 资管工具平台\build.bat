@echo off
echo Building Resource Management Tool...

rem Clean old files
if exist dist rmdir /s /q dist
if exist build rmdir /s /q build

rem Build with PyInstaller
pyinstaller --onedir --windowed --name "ResourceManagementTool" --add-data "config;config" --add-data "resources;resources" src/main.py

rem Check if build succeeded
if exist "dist\ResourceManagementTool\ResourceManagementTool.exe" (
    echo.
    echo Build completed successfully!
    echo Location: dist\ResourceManagementTool\
    echo.
    explorer dist\ResourceManagementTool
) else (
    echo.
    echo Build failed!
    echo.
)

pause
