#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
激活码生成和验证模块
"""

import re
import json
import base64
import struct
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from .crypto_utils import CryptoUtils, generate_hardware_id
from .license_types import LicenseType, LicenseInfo


class ActivationCodeGenerator:
    """激活码生成器"""
    
    # 产品代码映射
    PRODUCT_CODES = {
        LicenseType.FREE: "0001",
        LicenseType.TRIAL: "0002", 
        LicenseType.PROFESSIONAL: "0003",
        LicenseType.ENTERPRISE: "0004"
    }
    
    # 版本代码
    VERSION_CODE = "01"
    
    def __init__(self, master_secret: str = "ResourceManagementTool2024"):
        self.crypto = CryptoUtils()
        self.master_secret = master_secret
        self.master_key = self.crypto.generate_master_key(master_secret)
    
    def generate_activation_code(self, license_info: Dict[str, Any]) -> str:
        """生成激活码"""
        try:
            # 构建许可证数据
            license_type = license_info['license_type']
            if hasattr(license_type, 'value'):
                license_type = license_type.value

            license_data = {
                'type': license_type,
                'expire': license_info.get('expire_date'),
                'user': license_info.get('user_name', ''),
                'email': license_info.get('email', ''),
                'features': license_info.get('features', []),
                'max_devices': license_info.get('max_devices', 1),
                'created': datetime.now().isoformat()
            }
            
            # 序列化数据
            json_data = json.dumps(license_data, separators=(',', ':'))
            
            # 加密数据
            encrypted_data = self.crypto.encrypt_data(json_data, self.master_key)
            
            # 生成激活码
            activation_code = self._format_activation_code(
                license_info['license_type'], 
                encrypted_data
            )
            
            return activation_code
            
        except Exception as e:
            print(f"生成激活码失败: {e}")
            return ""
    
    def _format_activation_code(self, license_type: LicenseType, encrypted_data: str) -> str:
        """格式化激活码"""
        # 获取产品代码
        product_code = self.PRODUCT_CODES.get(license_type, "0000")

        # 生成核心码：使用时间戳和随机数据
        import time
        import random
        timestamp = int(time.time()) % 1000000  # 6位时间戳
        random_part = random.randint(0, 1023)   # 10位随机数

        # 组合成16位核心码
        core_code = f"{timestamp:06d}{random_part:04d}{''.join(random.choices('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', k=6))}"

        # 生成校验码
        check_data = product_code + self.VERSION_CODE + core_code
        checksum = self._calculate_checksum(check_data)

        # 组合完整激活码
        full_code = product_code + self.VERSION_CODE + core_code + checksum

        # 格式化为 XXXX-XXXX-XXXX-XXXX-XXXX-XXXX-XX 格式（26位分成7组）
        formatted_code = '-'.join([full_code[i:i+4] for i in range(0, len(full_code), 4)])

        return formatted_code
    
    def _calculate_checksum(self, data: str) -> str:
        """计算校验码"""
        checksum = 0
        for char in data:
            checksum += ord(char)
        
        # 转换为4位十六进制
        return f"{checksum % 65536:04X}"
    
    def validate_activation_code(self, activation_code: str, hardware_id: str = None) -> Optional[Dict[str, Any]]:
        """验证激活码"""
        try:
            # 清理激活码格式
            clean_code = activation_code.replace('-', '').replace(' ', '').upper()
            
            # 检查长度（应该是26位：4位产品码 + 2位版本码 + 16位核心码 + 4位校验码）
            if len(clean_code) != 26:
                return None
            
            # 解析激活码 (26位: 4+2+16+4)
            product_code = clean_code[:4]
            version_code = clean_code[4:6]
            core_code = clean_code[6:22]
            checksum = clean_code[22:26]


            
            # 验证校验码
            check_data = product_code + version_code + core_code
            expected_checksum = self._calculate_checksum(check_data)
            
            if checksum != expected_checksum:
                return None
            
            # 验证版本代码
            if version_code != self.VERSION_CODE:
                return None
            
            # 获取许可证类型
            license_type = None
            for lt, code in self.PRODUCT_CODES.items():
                if code == product_code:
                    license_type = lt
                    break
            
            if license_type is None:
                return None
            
            # 尝试解密核心码获取用户信息
            user_info = self._decrypt_core_code(core_code)

            # 构建验证结果
            validation_result = {
                'valid': True,
                'license_type': license_type,
                'product_code': product_code,
                'core_code': core_code,
                'activation_code': activation_code,
                'user_info': user_info
            }

            return validation_result
            
        except Exception as e:
            return None

    def _decrypt_core_code(self, core_code: str) -> Dict[str, Any]:
        """从核心码中提取信息"""
        try:
            # 核心码结构：6位时间戳 + 4位随机数 + 6位随机字符
            if len(core_code) >= 16:
                timestamp_part = core_code[:6]
                random_part = core_code[6:10]
                char_part = core_code[10:16]

                # 从时间戳推算创建时间（近似）
                try:
                    timestamp = int(timestamp_part)
                    # 这是一个简化的时间戳，实际使用中可以更复杂
                    created_time = datetime.now().isoformat()
                except:
                    created_time = datetime.now().isoformat()

                return {
                    'user_name': '',  # 用户信息需要在激活时手动输入
                    'email': '',
                    'company': '',
                    'created': created_time,
                    'core_info': {
                        'timestamp': timestamp_part,
                        'random': random_part,
                        'chars': char_part
                    }
                }
            else:
                return {
                    'user_name': '',
                    'email': '',
                    'company': '',
                    'created': datetime.now().isoformat()
                }
        except Exception:
            return {
                'user_name': '',
                'email': '',
                'company': '',
                'created': datetime.now().isoformat()
            }
    
    def is_activation_code_format_valid(self, activation_code: str) -> bool:
        """检查激活码格式是否有效"""
        # 清理激活码，移除所有分隔符
        clean_code = activation_code.replace('-', '').replace(' ', '').upper()

        # 检查长度和字符
        if len(clean_code) != 26:
            return False

        # 检查是否只包含字母和数字
        if not clean_code.isalnum():
            return False

        return True


class ActivationCodeValidator:
    """激活码验证器"""
    
    def __init__(self):
        self.generator = ActivationCodeGenerator()
        self.crypto = CryptoUtils()
    
    def validate_and_extract_license(self, activation_code: str, hardware_id: str = None) -> Optional[LicenseInfo]:
        """验证激活码并提取许可证信息"""
        try:
            # 基础格式验证
            if not self.generator.is_activation_code_format_valid(activation_code):
                return None

            # 验证激活码
            validation_result = self.generator.validate_activation_code(activation_code, hardware_id)
            if not validation_result or not validation_result.get('valid', False):
                return None

            # 创建许可证信息
            license_type = validation_result['license_type']

            # 根据许可证类型创建相应的许可证信息
            if license_type == LicenseType.TRIAL:
                expire_date = datetime.now() + timedelta(days=30)
            elif license_type == LicenseType.FREE:
                expire_date = None
            else:
                # 专业版和企业版默认1年有效期
                expire_date = datetime.now() + timedelta(days=365)

            # 获取硬件ID
            if hardware_id is None:
                hardware_id = generate_hardware_id()

            # 获取用户信息（激活码中的信息为空，需要在激活时填写）
            user_info = validation_result.get('user_info', {})

            # 创建许可证信息
            license_info = LicenseInfo(
                license_type=license_type,
                activation_code=activation_code,
                expire_date=expire_date,
                hardware_id=hardware_id,
                features=self._get_features_for_license_type(license_type),
                created_date=datetime.now(),
                last_validation=datetime.now(),
                user_name=user_info.get('user_name', ''),
                email=user_info.get('email', ''),
                company_name=user_info.get('company', '')
            )

            return license_info

        except Exception as e:
            return None

    def validate_and_extract_license_with_user_info(self, activation_code: str,
                                                   user_name: str = '',
                                                   email: str = '',
                                                   company_name: str = '',
                                                   hardware_id: str = None) -> Optional[LicenseInfo]:
        """验证激活码并提取许可证信息，包含用户输入的信息"""
        # 先进行基础验证
        license_info = self.validate_and_extract_license(activation_code, hardware_id)

        if license_info:
            # 更新用户信息
            license_info.user_name = user_name
            license_info.email = email
            license_info.company_name = company_name

        return license_info
    
    def _get_features_for_license_type(self, license_type: LicenseType) -> list:
        """根据许可证类型获取功能列表"""
        from .license_types import LicenseFeatures
        return LicenseFeatures.LICENSE_FEATURES.get(license_type, [])
    
    def check_hardware_binding(self, license_info: LicenseInfo, current_hardware_id: str = None) -> bool:
        """检查硬件绑定"""
        if current_hardware_id is None:
            current_hardware_id = generate_hardware_id()
        
        return license_info.hardware_id == current_hardware_id
    
    def is_license_expired(self, license_info: LicenseInfo) -> bool:
        """检查许可证是否过期"""
        if license_info.expire_date is None:
            return False  # 永不过期
        
        return datetime.now() > license_info.expire_date
    
    def get_days_until_expiry(self, license_info: LicenseInfo) -> Optional[int]:
        """获取距离过期的天数"""
        if license_info.expire_date is None:
            return None  # 永不过期
        
        delta = license_info.expire_date - datetime.now()
        return max(0, delta.days)


def generate_sample_activation_codes():
    """生成示例激活码（用于测试）"""
    generator = ActivationCodeGenerator()
    
    sample_licenses = [
        {
            'license_type': LicenseType.TRIAL,
            'user_name': '试用用户',
            'email': '<EMAIL>',
            'features': ['archive_processor', 'package_merger'],
            'expire_date': (datetime.now() + timedelta(days=30)).isoformat()
        },
        {
            'license_type': LicenseType.PROFESSIONAL,
            'user_name': '专业用户',
            'email': '<EMAIL>',
            'features': ['archive_processor', 'package_merger', 'svn_uploader', 'themes'],
            'expire_date': (datetime.now() + timedelta(days=365)).isoformat()
        },
        {
            'license_type': LicenseType.ENTERPRISE,
            'user_name': '企业用户',
            'email': '<EMAIL>',
            'features': ['archive_processor', 'package_merger', 'svn_uploader', 'themes', 'custom_rules'],
            'expire_date': (datetime.now() + timedelta(days=365)).isoformat()
        }
    ]
    
    codes = {}
    for license_data in sample_licenses:
        code = generator.generate_activation_code(license_data)
        codes[license_data['license_type'].value] = code
    
    return codes


if __name__ == "__main__":
    # 测试激活码生成和验证
    print("生成示例激活码:")
    codes = generate_sample_activation_codes()
    for license_type, code in codes.items():
        print(f"{license_type}: {code}")
    
    print("\n验证激活码:")
    validator = ActivationCodeValidator()
    for license_type, code in codes.items():
        result = validator.validate_and_extract_license(code)
        if result:
            print(f"{license_type}: 验证成功 - {result.license_type.value}")
        else:
            print(f"{license_type}: 验证失败")
