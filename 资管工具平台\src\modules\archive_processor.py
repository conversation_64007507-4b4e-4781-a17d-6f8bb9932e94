#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
压缩包处理模块
基于原始的压缩包处理工具，提供文件版本管理功能
"""

import os
import re
from typing import Dict, List, Optional, Tuple
from PyQt6.QtCore import QObject, pyqtSignal, QThread


class ArchiveInfo:
    """压缩包信息类"""
    
    def __init__(self, filename: str):
        self.filename = filename
        self.date = ""
        self.category = ""
        self.description = ""
        self.extra_info = ""
        self.version = ""
        self.author = ""
        self.extension = ""
        self.is_valid = False
        
        self._parse_filename()
        
    def _parse_filename(self):
        """解析文件名信息"""
        # 正则表达式更新，支持.zip和.rar格式，并支持多级版本号
        pattern = r'^(\d{8})【手机魔域】\[(.*?)\](.*?)' \
                  r'(?:\((.*?)\))?v(\d+(?:\.\d+)*)\((.*?)\)\.(zip|rar)$'
        match = re.match(pattern, self.filename)
        
        if match:
            self.date = match.group(1)
            self.category = match.group(2)
            self.description = match.group(3)
            self.extra_info = match.group(4) if match.group(4) else ''
            self.version = match.group(5)
            self.author = match.group(6)
            self.extension = match.group(7)
            self.is_valid = True
            
    def get_group_key(self) -> Tuple[str, str]:
        """获取分组键（类别和描述）"""
        return (self.category, self.description)
        
    def version_to_tuple(self) -> Tuple[int, ...]:
        """将版本号转换为元组，以便比较大小"""
        try:
            if not self.version or self.version is None:
                return (0,)

            # 确保version是字符串
            version_str = str(self.version).strip()
            if not version_str:
                return (0,)

            # 分割版本号并转换为整数
            parts = version_str.split('.')
            result = []
            for part in parts:
                try:
                    result.append(int(part.strip()))
                except (ValueError, AttributeError):
                    result.append(0)

            return tuple(result) if result else (0,)
        except (ValueError, AttributeError, TypeError):
            return (0,)
            
    def __str__(self):
        return f"ArchiveInfo({self.filename}, v{self.version}, {self.date})"


class ArchiveProcessor(QObject):
    """压缩包处理器"""
    
    # 信号定义
    progress_updated = pyqtSignal(int)  # 进度更新
    log_message = pyqtSignal(str)       # 日志消息
    processing_finished = pyqtSignal(bool, str)  # 处理完成(成功, 消息)
    
    def __init__(self):
        super().__init__()
        
    def process_directory(self, directory: str, dry_run: bool = False, backup_files: bool = True, delete_invalid: bool = False) -> bool:
        """
        处理目录中的压缩包文件

        Args:
            directory: 要处理的目录路径
            dry_run: 是否为试运行（不实际删除文件）
            backup_files: 是否备份原始文件
            delete_invalid: 是否删除无效文件（不符合命名规范）

        Returns:
            bool: 处理是否成功
        """
        try:
            self.log_message.emit(f"开始处理目录: {directory}")
            
            # 获取目录中的所有文件
            if not os.path.exists(directory):
                self.log_message.emit("错误：目录不存在")
                self.processing_finished.emit(False, "目录不存在")
                return False
                
            files = [f for f in os.listdir(directory) 
                    if os.path.isfile(os.path.join(directory, f))]
            
            if not files:
                self.log_message.emit("目录中没有文件")
                self.processing_finished.emit(True, "目录中没有文件")
                return True
                
            self.log_message.emit(f"目录中的文件: {files}")
            
            # 备份文件（如果需要且不是试运行）
            if backup_files and not dry_run:
                backup_dir = os.path.join(directory, "backup")
                if not os.path.exists(backup_dir):
                    os.makedirs(backup_dir)
                    self.log_message.emit(f"创建备份目录: {backup_dir}")

                self.log_message.emit("开始备份文件...")
                for filename in files:
                    src_path = os.path.join(directory, filename)
                    dst_path = os.path.join(backup_dir, filename)
                    try:
                        import shutil
                        shutil.copy2(src_path, dst_path)
                        self.log_message.emit(f"备份文件: {filename}")
                    except Exception as e:
                        self.log_message.emit(f"备份文件失败 {filename}: {str(e)}")

                self.log_message.emit("文件备份完成")

            # 解析文件信息
            archive_infos = []
            invalid_files = []

            for filename in files:
                info = ArchiveInfo(filename)
                if info.is_valid:
                    archive_infos.append(info)
                    self.log_message.emit(f"解析文件: {info}")
                else:
                    invalid_files.append(filename)
                    self.log_message.emit(f"无效文件: {filename} (不匹配模式)")

            # 处理无效文件
            if delete_invalid and invalid_files:
                self.log_message.emit(f"\n发现 {len(invalid_files)} 个无效文件:")
                for filename in invalid_files:
                    self.log_message.emit(f"  {filename}")

                if not dry_run:
                    deleted_invalid = 0
                    for filename in invalid_files:
                        file_path = os.path.join(directory, filename)
                        try:
                            os.remove(file_path)
                            self.log_message.emit(f"删除无效文件: {filename}")
                            deleted_invalid += 1
                        except Exception as e:
                            self.log_message.emit(f"删除无效文件失败 {filename}: {str(e)}")
                    self.log_message.emit(f"删除了 {deleted_invalid} 个无效文件")
                else:
                    self.log_message.emit(f"试运行：将删除 {len(invalid_files)} 个无效文件")

            if not archive_infos:
                message = "没有找到有效的压缩包文件"
                if delete_invalid and invalid_files:
                    if dry_run:
                        message += f"，但发现 {len(invalid_files)} 个无效文件可删除"
                    else:
                        message += f"，已删除 {len(invalid_files)} 个无效文件"
                self.log_message.emit(message)
                self.processing_finished.emit(True, message)
                return True
                
            # 获取最新文件列表
            latest_files = self._get_latest_files(archive_infos)
            self.log_message.emit("最新的文件列表:")
            for filename in latest_files:
                self.log_message.emit(f"  {filename}")
                
            # 删除非最新文件
            deleted_count = self._remove_non_latest_files(
                directory, latest_files, archive_infos, dry_run)
                
            if dry_run:
                message = f"试运行完成，将删除 {deleted_count} 个文件"
            else:
                message = f"处理完成，删除了 {deleted_count} 个文件"
                
            self.log_message.emit(message)
            self.processing_finished.emit(True, message)
            return True
            
        except Exception as e:
            error_msg = f"处理过程中发生错误: {str(e)}"
            self.log_message.emit(error_msg)
            self.processing_finished.emit(False, error_msg)
            return False
            
    def _get_latest_files(self, archive_infos: List[ArchiveInfo]) -> List[str]:
        """
        获取每个描述组合的最新文件
        如果版本号不同，选择版本号较高的文件
        如果版本号相同，选择日期较新的文件
        """
        latest_files = {}
        
        for info in archive_infos:
            key = info.get_group_key()
            
            if key not in latest_files:
                latest_files[key] = info
                self.log_message.emit(
                    f"添加新组: 类别={info.category}, 描述={info.description} -> {info.filename}")
            else:
                try:
                    current_version = info.version_to_tuple()
                    existing_version = latest_files[key].version_to_tuple()

                    # 确保版本元组不为None
                    if current_version is None:
                        current_version = (0,)
                    if existing_version is None:
                        existing_version = (0,)

                    if current_version > existing_version:
                        self.log_message.emit(
                            f"更新组 '{key}': 版本 {info.version} > {latest_files[key].version}")
                        latest_files[key] = info
                    elif current_version == existing_version:
                        # 如果版本相同，比较日期
                        try:
                            current_date = info.date if info.date is not None else ""
                            existing_date = latest_files[key].date if latest_files[key].date is not None else ""

                            if current_date > existing_date:
                                self.log_message.emit(
                                    f"更新组 '{key}': 日期 {info.date} > {latest_files[key].date}")
                                latest_files[key] = info
                            else:
                                self.log_message.emit(
                                    f"不更新组 '{key}': 当前文件日期 {info.date} <= 已存在文件日期 {latest_files[key].date}")
                        except (TypeError, AttributeError):
                            # 日期比较失败，保持现有文件
                            self.log_message.emit(
                                f"不更新组 '{key}': 日期比较失败，保持现有文件")
                    else:
                        self.log_message.emit(
                            f"不更新组 '{key}': 版本 {info.version} <= 已存在文件版本 {latest_files[key].version}")
                except (TypeError, AttributeError, ValueError) as e:
                    # 版本比较失败，跳过此文件
                    self.log_message.emit(
                        f"跳过组 '{key}': 版本比较失败 - {str(e)}")
                    continue
                    
        return [info.filename for info in latest_files.values()]
        
    def _remove_non_latest_files(self, directory: str, latest_files: List[str], 
                                archive_infos: List[ArchiveInfo], dry_run: bool) -> int:
        """
        删除非最新的文件，只保留最新文件
        
        Returns:
            int: 删除的文件数量
        """
        all_valid_files = [info.filename for info in archive_infos]
        files_to_delete = set(all_valid_files) - set(latest_files)
        
        self.log_message.emit(f"要删除的文件: {files_to_delete}")
        
        deleted_count = 0
        for filename in files_to_delete:
            file_path = os.path.join(directory, filename)
            self.log_message.emit(f"准备删除文件: {file_path}")
            
            if dry_run:
                self.log_message.emit(f"[试运行] 将删除文件: {file_path}")
                deleted_count += 1
            else:
                try:
                    os.remove(file_path)
                    self.log_message.emit(f"成功删除文件: {file_path}")
                    deleted_count += 1
                except Exception as e:
                    self.log_message.emit(f"删除文件失败: {file_path}, 错误: {e}")
                    
        return deleted_count
        
    def get_directory_analysis(self, directory: str) -> Dict:
        """
        分析目录中的压缩包文件，返回分析结果
        
        Returns:
            Dict: 包含文件分析结果的字典
        """
        try:
            if not os.path.exists(directory):
                return {"error": "目录不存在"}
                
            files = [f for f in os.listdir(directory) 
                    if os.path.isfile(os.path.join(directory, f))]
                    
            archive_infos = []
            invalid_files = []
            
            for filename in files:
                info = ArchiveInfo(filename)
                if info.is_valid:
                    archive_infos.append(info)
                else:
                    invalid_files.append(filename)
                    
            # 按组分类
            groups = {}
            for info in archive_infos:
                key = info.get_group_key()
                if key not in groups:
                    groups[key] = []
                groups[key].append(info)
                
            # 统计信息
            latest_files = self._get_latest_files(archive_infos)
            files_to_delete = [info.filename for info in archive_infos 
                             if info.filename not in latest_files]
                             
            return {
                "total_files": len(files),
                "valid_archives": len(archive_infos),
                "invalid_files": invalid_files,
                "groups": groups,
                "latest_files": latest_files,
                "files_to_delete": files_to_delete,
                "delete_count": len(files_to_delete)
            }
            
        except Exception as e:
            return {"error": str(e)}


class ArchiveProcessorWorker(QThread):
    """压缩包处理工作线程"""
    
    progress_updated = pyqtSignal(int)
    log_message = pyqtSignal(str)
    processing_finished = pyqtSignal(bool, str)
    
    def __init__(self, directory: str, dry_run: bool = False, backup_files: bool = True, delete_invalid: bool = False):
        super().__init__()
        self.directory = directory
        self.dry_run = dry_run
        self.backup_files = backup_files
        self.delete_invalid = delete_invalid
        self.processor = ArchiveProcessor()

        # 连接信号
        self.processor.progress_updated.connect(self.progress_updated)
        self.processor.log_message.connect(self.log_message)
        self.processor.processing_finished.connect(self.processing_finished)

    def run(self):
        """运行处理任务"""
        self.processor.process_directory(self.directory, self.dry_run, self.backup_files, self.delete_invalid)
