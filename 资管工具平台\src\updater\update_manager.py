#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新管理器
"""

import os
import sys
import json
import shutil
import subprocess
import tempfile
from pathlib import Path
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from PyQt6.QtCore import QObject, pyqtSignal, QTimer

from .version_checker import Version<PERSON><PERSON><PERSON>, UpdateCheckResult, VersionInfo, UpdateChannel
from .download_manager import DownloadManager
from utils.config_manager import ConfigManager


class UpdateManager(QObject):
    """更新管理器"""
    
    # 信号定义
    update_available = pyqtSignal(VersionInfo)      # 发现新版本
    update_check_finished = pyqtSignal(UpdateCheckResult)  # 检查完成
    download_progress = pyqtSignal(int, int, int)   # 下载进度
    update_ready = pyqtSignal(str)                  # 更新准备就绪
    update_failed = pyqtSignal(str)                 # 更新失败
    update_installed = pyqtSignal()                 # 更新安装完成
    
    def __init__(self, license_manager=None, config_manager: ConfigManager = None):
        super().__init__()
        
        self.license_manager = license_manager
        self.config_manager = config_manager or ConfigManager()
        
        # 初始化组件
        self.version_checker = VersionChecker(
            current_version=self._get_current_version(),
            license_manager=license_manager
        )
        self.download_manager = DownloadManager()
        
        # 连接信号
        self.download_manager.download_progress.connect(self.download_progress.emit)
        self.download_manager.download_completed.connect(self._on_download_completed)
        self.download_manager.download_failed.connect(self.update_failed.emit)
        
        # 自动检查定时器
        self.auto_check_timer = QTimer()
        self.auto_check_timer.timeout.connect(self.check_for_updates)
        
        # 当前更新信息
        self.current_update_info: Optional[VersionInfo] = None
        self.downloaded_update_path: Optional[str] = None
        
        # 启动自动检查
        self._setup_auto_check()
    
    def check_for_updates(self, manual: bool = False) -> bool:
        """检查更新"""
        try:
            # 检查是否支持自动更新
            if not manual and not self._is_auto_update_enabled():
                return False
            
            # 检查许可证是否支持更新
            if not self._is_update_allowed():
                if manual:
                    result = UpdateCheckResult(
                        has_update=False,
                        current_version=self._get_current_version(),
                        error_message="当前许可证不支持自动更新功能"
                    )
                    self.update_check_finished.emit(result)
                return False
            
            # 获取更新渠道
            channel = self._get_update_channel()
            
            # 执行检查
            result = self.version_checker.check_for_updates(channel)
            
            # 更新最后检查时间
            if not manual:
                self._update_last_check_time()
            
            # 发送结果信号
            self.update_check_finished.emit(result)
            
            # 如果有更新，发送更新可用信号
            if result.has_update and result.latest_version:
                self.current_update_info = result.latest_version
                self.update_available.emit(result.latest_version)
            
            return True
            
        except Exception as e:
            error_result = UpdateCheckResult(
                has_update=False,
                current_version=self._get_current_version(),
                error_message=f"检查更新失败: {str(e)}"
            )
            self.update_check_finished.emit(error_result)
            return False
    
    def download_update(self, version_info: VersionInfo = None) -> bool:
        """下载更新"""
        if version_info is None:
            version_info = self.current_update_info
        
        if version_info is None:
            self.update_failed.emit("没有可用的更新信息")
            return False
        
        try:
            # 生成下载文件名
            filename = f"ResourceManagementTool-{version_info.version}.zip"
            
            # 开始下载
            success = self.download_manager.download_update(
                download_url=version_info.download_url,
                filename=filename,
                expected_hash=version_info.file_hash
            )
            
            if not success:
                self.update_failed.emit("启动下载失败")
            
            return success
            
        except Exception as e:
            self.update_failed.emit(f"下载更新失败: {str(e)}")
            return False
    
    def install_update(self, update_file_path: str = None) -> bool:
        """安装更新"""
        if update_file_path is None:
            update_file_path = self.downloaded_update_path
        
        if not update_file_path or not os.path.exists(update_file_path):
            self.update_failed.emit("更新文件不存在")
            return False
        
        try:
            # 创建更新脚本
            update_script = self._create_update_script(update_file_path)
            
            # 执行更新脚本
            self._execute_update_script(update_script)
            
            return True
            
        except Exception as e:
            self.update_failed.emit(f"安装更新失败: {str(e)}")
            return False
    
    def cancel_update(self):
        """取消更新"""
        self.download_manager.cancel_download()
        self.current_update_info = None
        self.downloaded_update_path = None
    
    def get_update_settings(self) -> Dict[str, Any]:
        """获取更新设置"""
        return {
            'auto_check': self.config_manager.get_config("update.auto_check", True),
            'check_interval': self.config_manager.get_config("update.check_interval", 24),  # 小时
            'update_channel': self.config_manager.get_config("update.channel", "stable"),
            'auto_download': self.config_manager.get_config("update.auto_download", False),
            'auto_install': self.config_manager.get_config("update.auto_install", False)
        }
    
    def update_settings(self, settings: Dict[str, Any]):
        """更新设置"""
        for key, value in settings.items():
            self.config_manager.set_config(f"update.{key}", value)
        
        self.config_manager.save_user_config()
        
        # 重新设置自动检查
        self._setup_auto_check()
    
    def _get_current_version(self) -> str:
        """获取当前版本"""
        return self.config_manager.get_config("app.version", "1.0.0")
    
    def _is_auto_update_enabled(self) -> bool:
        """检查是否启用自动更新"""
        return self.config_manager.get_config("update.auto_check", True)
    
    def _is_update_allowed(self) -> bool:
        """检查是否允许更新"""
        if not self.license_manager:
            # 如果没有许可证管理器，默认允许更新
            return True

        if not self.license_manager.current_license:
            return False

        # 检查许可证是否支持自动更新
        return self.license_manager.is_feature_enabled("auto_update")

    def _should_auto_check(self) -> bool:
        """检查是否应该自动检查更新"""
        if not self._is_auto_update_enabled():
            return False

        # 检查时间间隔
        last_check = self._get_last_check_time()
        if last_check is None:
            return True

        try:
            check_interval = self.config_manager.get_config("update.check_interval", 24)  # 小时
            if check_interval is None:
                check_interval = 24

            time_since_last = (datetime.now() - last_check).total_seconds() / 3600

            return time_since_last >= check_interval
        except (TypeError, AttributeError):
            return True
    
    def _get_update_channel(self) -> UpdateChannel:
        """获取更新渠道"""
        channel_name = self.config_manager.get_config("update.channel", "stable")
        try:
            return UpdateChannel(channel_name)
        except ValueError:
            return UpdateChannel.STABLE
    
    def _setup_auto_check(self):
        """设置自动检查"""
        self.auto_check_timer.stop()
        
        if not self._is_auto_update_enabled():
            return
        
        # 检查是否需要立即检查
        try:
            last_check = self._get_last_check_time()
            check_interval = self.config_manager.get_config("update.check_interval", 24)  # 小时

            if check_interval is None:
                check_interval = 24

            should_check = (last_check is None or
                          datetime.now() - last_check > timedelta(hours=check_interval))

            if should_check:
                # 延迟5秒后检查，避免启动时阻塞
                QTimer.singleShot(5000, lambda: self.check_for_updates(manual=False))
        except (TypeError, AttributeError):
            # 如果时间比较出错，默认进行检查
            QTimer.singleShot(5000, lambda: self.check_for_updates(manual=False))
        
        # 设置定期检查
        try:
            if check_interval is None or not isinstance(check_interval, (int, float)):
                check_interval = 24  # 默认24小时

            interval_ms = int(check_interval * 60 * 60 * 1000)  # 转换为毫秒
            if interval_ms > 0:
                self.auto_check_timer.start(interval_ms)
        except (TypeError, ValueError):
            # 如果计算失败，使用默认间隔
            self.auto_check_timer.start(24 * 60 * 60 * 1000)  # 24小时
    
    def _get_last_check_time(self) -> Optional[datetime]:
        """获取最后检查时间"""
        timestamp = self.config_manager.get_config("update.last_check", None)
        if timestamp:
            try:
                return datetime.fromisoformat(timestamp)
            except ValueError:
                pass
        return None
    
    def _update_last_check_time(self):
        """更新最后检查时间"""
        self.config_manager.set_config("update.last_check", datetime.now().isoformat())
        self.config_manager.save_user_config()
    
    def _on_download_completed(self, file_path: str):
        """下载完成处理"""
        self.downloaded_update_path = file_path
        self.update_ready.emit(file_path)
        
        # 如果启用自动安装，则自动安装
        if self.config_manager.get_config("update.auto_install", False):
            self.install_update(file_path)
    
    def _create_update_script(self, update_file_path: str) -> str:
        """创建更新脚本"""
        # 获取应用程序路径
        app_dir = Path(sys.executable).parent if getattr(sys, 'frozen', False) else Path(__file__).parent.parent.parent
        
        # 创建临时更新脚本
        script_content = self._generate_update_script_content(update_file_path, str(app_dir))
        
        script_path = os.path.join(tempfile.gettempdir(), "update_script.bat")
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        return script_path
    
    def _generate_update_script_content(self, update_file_path: str, app_dir: str) -> str:
        """生成更新脚本内容"""
        return f"""@echo off
echo 正在准备更新...

REM 等待主程序退出
timeout /t 3 /nobreak >nul

echo 备份当前版本...
if exist "{app_dir}\\backup" rmdir /s /q "{app_dir}\\backup"
mkdir "{app_dir}\\backup"
xcopy "{app_dir}\\*" "{app_dir}\\backup\\" /e /i /h /y >nul

echo 解压更新文件...
powershell -command "Expand-Archive -Path '{update_file_path}' -DestinationPath '{app_dir}' -Force"

if %errorlevel% neq 0 (
    echo 更新失败，正在恢复备份...
    rmdir /s /q "{app_dir}"
    move "{app_dir}\\backup" "{app_dir}"
    echo 更新失败，已恢复到原版本
    pause
    exit /b 1
)

echo 清理备份文件...
rmdir /s /q "{app_dir}\\backup"

echo 更新完成，正在重启应用程序...
start "" "{app_dir}\\run.py"

REM 删除更新文件和脚本
del "{update_file_path}"
del "%~f0"
"""
    
    def _execute_update_script(self, script_path: str):
        """执行更新脚本"""
        try:
            # 启动更新脚本
            subprocess.Popen([script_path], shell=True, creationflags=subprocess.CREATE_NEW_CONSOLE)
            
            # 发送更新安装完成信号
            self.update_installed.emit()
            
            # 退出当前应用程序
            QTimer.singleShot(1000, self._exit_application)
            
        except Exception as e:
            raise Exception(f"执行更新脚本失败: {str(e)}")
    
    def _exit_application(self):
        """退出应用程序"""
        from PyQt6.QtWidgets import QApplication
        QApplication.instance().quit()
    
    def get_update_history(self) -> list:
        """获取更新历史"""
        return self.version_checker.get_update_history()
    
    def cleanup_old_updates(self):
        """清理旧的更新文件"""
        self.download_manager.cleanup_old_downloads()
