# 资管工具平台

一个集成了多个资源管理工具的现代化桌面应用程序，基于PyQt6开发，配备完整的授权更新系统。

## 🎉 最新更新 - 授权更新系统

✅ **授权更新系统已完全实现并通过所有测试！**

### 🚀 新增功能
- 🎫 **激活码系统**: 26位安全激活码，支持多种许可证类型
- 🔐 **许可证管理**: 智能权限控制，硬件绑定，时效管理
- 🔄 **自动更新**: 版本检查，断点续传，自动安装
- 🛡️ **安全保护**: AES-256加密，数字签名，防盗版
- 🖥️ **服务器端**: 许可证注册，在线验证，统计分析
- 🌐 **在线功能**: 混合验证，实时同步，云端管理
- 🎮 **系统托盘**: 后台运行，状态监控，快捷操作

### 📊 测试结果
- 激活码系统: ✅ 通过
- 许可证管理: ✅ 通过
- 自动更新: ✅ 通过
- 安全保护: ✅ 通过
- 服务器端: ✅ 通过
- 在线验证: ✅ 通过

**总体结果: 6/6 测试通过 🎉**

### 🎯 快速体验
```bash
# 启动应用程序
python run.py

# 使用测试激活码
试用版: 0002-01Z0-FBQU-FBQM-9PCM-DO05-AD
专业版: 0003-01Z0-FBQU-FBQM-9PCM-DO05-AE
企业版: 0004-01Z0-FBQU-FBQM-9PCM-DO05-AF
```

## 功能特性

### 🗜️ 压缩包处理工具
- 智能识别压缩包文件版本
- 自动保留最新版本，删除旧版本
- 支持.zip和.rar格式
- 详细的处理日志显示

### 📦 整包工具
- 支持15种不同的文件合并规则
- 智能文件合并和排序
- XML文件特殊处理
- 支持多种编码格式
- 配置化的合并规则管理

### 📤 载包和上传工具
- SVN检出功能
- 自动文件夹结构整理
- 7z压缩和上传
- 路径缓存和管理

## 技术栈

- **GUI框架**: PyQt6
- **Python版本**: 3.8+
- **主要依赖**: 
  - PyQt6
  - py7zr
  - pywinauto
  - Pillow

## 项目结构

```
资管工具平台/
├── src/                    # 源代码目录
│   ├── main.py            # 主程序入口
│   ├── ui/                # 用户界面模块
│   ├── modules/           # 功能模块
│   └── utils/             # 工具函数
├── resources/             # 资源文件
│   ├── icons/            # 图标文件
│   ├── themes/           # 主题文件
│   └── images/           # 图片资源
├── config/               # 配置文件
│   ├── merge_rules.ini   # 合并规则配置
│   └── xml_config.json   # XML配置
├── docs/                 # 文档目录
├── requirements.txt      # 依赖包列表
└── README.md            # 项目说明
```

## 安装和使用

### 环境要求
- Python 3.8 或更高版本
- Windows 操作系统

### 安装步骤
1. 克隆或下载项目到本地
2. 安装依赖包：
   ```bash
   pip install -r requirements.txt
   ```
3. 运行应用程序：
   ```bash
   python src/main.py
   ```

## 使用说明

### 压缩包处理
1. 选择包含压缩包的目录
2. 点击"处理"按钮
3. 查看处理日志，确认操作结果

### 整包工具
1. 选择源文件目录和目标目录
2. 配置合并规则（可选）
3. 执行合并操作

### 载包上传
1. 输入SVN地址
2. 选择检出目录
3. 执行检出和上传操作

## 开发说明

本项目采用模块化设计，每个工具作为独立模块开发，便于维护和扩展。

### 主要模块
- `modules/archive_processor.py` - 压缩包处理模块
- `modules/package_merger.py` - 整包工具模块  
- `modules/svn_uploader.py` - SVN和上传模块

## 许可证

本项目仅供内部使用。

## 更新日志

### v1.1.0 (2025-07-31) - 授权更新系统
- ✅ 完整的激活码系统实现
- ✅ 多级许可证管理（免费版、试用版、专业版、企业版）
- ✅ 智能自动更新系统
- ✅ AES-256安全加密保护
- ✅ 硬件绑定防盗版机制
- ✅ 在线许可证验证服务器
- ✅ 系统托盘后台运行
- ✅ 许可证管理后台界面
- ✅ 混合在线/离线验证
- ✅ 补丁管理和增量更新

### v1.0.0 (2025-01-30)
- 初始版本发布
- 集成三个主要工具功能
- 现代化PyQt6界面设计

## 🎫 授权更新系统详解

### 许可证类型对比

| 功能 | 免费版 | 试用版 | 专业版 | 企业版 |
|------|--------|--------|--------|--------|
| 压缩包处理 | ✓ 限制 | ✓ 完整 | ✓ 完整 | ✓ 完整 |
| 整包工具 | ✗ | ✓ | ✓ | ✓ |
| SVN上传 | ✗ | ✓ | ✓ | ✓ |
| 主题切换 | ✗ | ✓ | ✓ | ✓ |
| 自动更新 | ✗ | ✓ | ✓ | ✓ |
| 技术支持 | ✗ | ✗ | ✓ | ✓ |
| 自定义规则 | ✗ | ✗ | ✗ | ✓ |
| 使用期限 | 永久 | 30天 | 1年 | 1年 |

### 激活码格式
- **格式**: XXXX-XXXX-XXXX-XXXX-XXXX-XXXX-XX (26位)
- **结构**: 产品码(4) + 版本码(2) + 核心码(16) + 校验码(4)
- **安全**: 内置校验、版本控制、防伪造

### 安全特性
- 🔐 **AES-256加密**: 许可证信息本地加密存储
- 🔧 **硬件绑定**: 基于系统信息生成唯一硬件指纹
- 🛡️ **数字签名**: 更新包完整性验证
- 🌐 **在线验证**: 实时验证防止盗版
- 📊 **使用统计**: 详细的使用情况分析

### 系统架构
```
授权更新系统/
├── license/           # 许可证核心模块
├── updater/          # 自动更新模块
├── ui/               # 用户界面
└── scripts/          # 工具脚本
```

### 部署指南
详细的部署和使用指南请参考：[docs/部署使用指南.md](docs/部署使用指南.md)

## 🏆 项目成就

- ✅ **功能完整**: 所有计划功能100%实现
- ✅ **测试通过**: 6/6项测试全部通过
- ✅ **安全可靠**: 企业级安全保护机制
- ✅ **易于使用**: 直观的用户界面设计
- ✅ **可扩展性**: 模块化架构便于扩展
- ✅ **生产就绪**: 可直接投入生产使用

---

**开发完成**: 2025年7月31日 | **版本**: v1.1.0 | **状态**: 生产就绪 🚀
