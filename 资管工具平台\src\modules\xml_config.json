{"no_sort": {"Achieve": [["AchieveType", "AchieveActivityID"], ["AchieveType", "AchieveIdType"], ["AchieveInfo", "AchieveID"]], "AdBoard": [["AdBoardInfo", "idNpc"]], "AreaInfo": [["Area", "AreaID"]], "Group": [["Cheats", "CheatsID"], ["HelpModule", "WndID"], ["Equip", "EquipName"], ["Equip", "TotalLev"], ["Profession", "ProfessionID"], ["Function", "FunctionType"], ["<PERSON><PERSON>", "ItemList"], ["Task", "TaskID"], ["Plots", "Profession"], ["Task", "id"]], "BossInfo": [["Boss", "idType"]], "CameraMagicParent": [["<PERSON><PERSON><PERSON>", "MagicID"]], "ClosetMount": [["ExchangeInfo", "EudTypeID"]], "CopyInfo": [["Copy", "CopyID"]], "RANK": [["RANK_TYPE", "rank_type"], ["RANK_MAIN", "rank_main_name"]], "Deification": [["Realm", "Level"]], "DevilTower": [["ChildLevelInfo", "ChildLevelID"]], "group": [["format", "newTitle"]], "EmotionPackage": [["<PERSON><PERSON>", "IndexType"]], "ExchangeInfo": [["Exchange", "NpcID"]], "Root": [["<PERSON><PERSON><PERSON><PERSON>", "idType"], ["GoalTask", "TaskContents Index"], ["SynGoalList", "SynGoal TaskIndex"], ["SynMember", "Amount"]], "EudRecommend": [["ProfessionEud", "Profession"]], "familypointmap": [["map", "idmap"]], "flymap": [["FlymapInfo", "idmap"]], "GiftBagInfo": [["GiftBag", "GiftBagId"]], "HelpMedal": [["Medalstate", "Type"]], "MagicAddiDesc": [["Magic", "ID"]], "MagicSoul": [["<PERSON><PERSON>", "TypeID"]], "gameplayui": [["mapgameplay", "gameplay"]], "PartGroup": [["Preset", "Type"]], "RebuildCondition": [["RebuildType", "Type"]], "SingleRecipes": [["SingleRecipes", "RecipeItemType"]], "confidant": [["level", "value"]], "SpiritualPowerEvalutionInfo": [["PublicStr", "HelpGuideIndex"]], "Story": [["Group", "Group"]], "SynPartys": [["SynParty", "level"]], "STARs": [["STAR", "stage"]], "SynWarReward": [["RewardType", "Type"]], "TEAM": [["TEAM_MAIN", "name"]], "TEXTURE": [["TEX_INFO", "TextureID"]], "Track": [["MagicType", "TypeID"]], "StageTask": [["TaskType", "type"]], "UpdateReward": [["Version", "value"]], "VIPs": [["VIP", "level"]], "Shortcuts": [["Shortcut", "id"]], "Eudemon": [["EnumType", "SkillType"]], "YaTeNoble": [["Lev", "level"]]}, "sort": {}}