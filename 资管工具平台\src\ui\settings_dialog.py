#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设置对话框
"""

import os
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTabWidget,
                            QWidget, QLabel, QLineEdit, QPushButton, QComboBox,
                            QCheckBox, QGroupBox, QGridLayout, QFileDialog,
                            QSpacerItem, QSizePolicy, QMessageBox, QSpinBox,
                            QScrollArea)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

from utils.config_manager import ConfigManager


class SettingsDialog(QDialog):
    """设置对话框"""
    
    # 信号定义
    theme_changed = pyqtSignal(str)  # 主题变更信号
    settings_applied = pyqtSignal()   # 设置应用信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.config_manager = ConfigManager()
        self.init_ui()
        self.load_settings()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("设置")
        self.setModal(True)
        self.setFixedSize(600, 500)
        
        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 创建标签页控件
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 添加各个设置页面
        self.create_general_tab()
        self.create_paths_tab()
        self.create_appearance_tab()
        self.create_behavior_tab()
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.reset_btn = QPushButton("重置默认")
        self.cancel_btn = QPushButton("取消")
        self.apply_btn = QPushButton("应用")
        self.ok_btn = QPushButton("确定")
        
        self.reset_btn.clicked.connect(self.reset_to_defaults)
        self.cancel_btn.clicked.connect(self.reject)
        self.apply_btn.clicked.connect(self.apply_settings)
        self.ok_btn.clicked.connect(self.accept_settings)
        
        button_layout.addWidget(self.reset_btn)
        button_layout.addWidget(self.cancel_btn)
        button_layout.addWidget(self.apply_btn)
        button_layout.addWidget(self.ok_btn)
        
        layout.addLayout(button_layout)
        
    def create_general_tab(self):
        """创建常规设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 启动设置组
        startup_group = QGroupBox("启动设置")
        startup_layout = QGridLayout(startup_group)
        
        startup_layout.addWidget(QLabel("启动时打开标签页:"), 0, 0)
        self.startup_tab_combo = QComboBox()
        self.startup_tab_combo.addItems(["压缩包处理", "整包工具", "载包上传"])
        startup_layout.addWidget(self.startup_tab_combo, 0, 1)
        
        self.remember_window_cb = QCheckBox("记住窗口大小和位置")
        startup_layout.addWidget(self.remember_window_cb, 1, 0, 1, 2)
        
        self.auto_save_settings_cb = QCheckBox("自动保存设置")
        startup_layout.addWidget(self.auto_save_settings_cb, 2, 0, 1, 2)
        
        layout.addWidget(startup_group)
        
        # 语言设置组
        language_group = QGroupBox("语言设置")
        language_layout = QGridLayout(language_group)
        
        language_layout.addWidget(QLabel("界面语言:"), 0, 0)
        self.language_combo = QComboBox()
        self.language_combo.addItems(["简体中文", "English"])
        language_layout.addWidget(self.language_combo, 0, 1)
        
        layout.addWidget(language_group)
        
        layout.addStretch()
        self.tab_widget.addTab(widget, "常规")
        
    def create_paths_tab(self):
        """创建路径设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 路径行为设置组
        behavior_group = QGroupBox("路径行为")
        behavior_layout = QVBoxLayout(behavior_group)
        
        self.remember_last_paths_cb = QCheckBox("记住上次使用的路径")
        behavior_layout.addWidget(self.remember_last_paths_cb)
        
        self.auto_create_dirs_cb = QCheckBox("自动创建不存在的目录")
        behavior_layout.addWidget(self.auto_create_dirs_cb)
        
        layout.addWidget(behavior_group)
        
        layout.addStretch()
        self.tab_widget.addTab(widget, "路径")
        
    def create_appearance_tab(self):
        """创建外观设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 主题设置组
        theme_group = QGroupBox("主题设置")
        theme_layout = QGridLayout(theme_group)
        
        theme_layout.addWidget(QLabel("应用主题:"), 0, 0)
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["默认", "深色", "蓝色"])
        self.theme_combo.currentTextChanged.connect(self.on_theme_changed)
        theme_layout.addWidget(self.theme_combo, 0, 1)
        
        layout.addWidget(theme_group)
        
        # 字体设置组
        font_group = QGroupBox("字体设置")
        font_layout = QGridLayout(font_group)
        
        font_layout.addWidget(QLabel("界面字体大小:"), 0, 0)
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 20)
        self.font_size_spin.setValue(9)
        font_layout.addWidget(self.font_size_spin, 0, 1)
        
        font_layout.addWidget(QLabel("日志字体大小:"), 1, 0)
        self.log_font_size_spin = QSpinBox()
        self.log_font_size_spin.setRange(8, 16)
        self.log_font_size_spin.setValue(9)
        font_layout.addWidget(self.log_font_size_spin, 1, 1)
        
        layout.addWidget(font_group)
        
        layout.addStretch()
        self.tab_widget.addTab(widget, "外观")
        
    def create_behavior_tab(self):
        """创建行为设置标签页"""
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)

        # 创建内容widget
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)

        # 操作确认设置组
        confirm_group = QGroupBox("操作确认")
        confirm_layout = QVBoxLayout(confirm_group)
        confirm_layout.setSpacing(8)

        self.confirm_delete_cb = QCheckBox("删除文件前确认")
        confirm_layout.addWidget(self.confirm_delete_cb)

        self.confirm_overwrite_cb = QCheckBox("覆盖文件前确认")
        confirm_layout.addWidget(self.confirm_overwrite_cb)

        self.confirm_exit_cb = QCheckBox("退出程序前确认")
        confirm_layout.addWidget(self.confirm_exit_cb)

        layout.addWidget(confirm_group)

        # 备份设置组
        backup_group = QGroupBox("备份设置")
        backup_layout = QVBoxLayout(backup_group)
        backup_layout.setSpacing(8)

        self.auto_backup_cb = QCheckBox("自动备份原始文件")
        backup_layout.addWidget(self.auto_backup_cb)

        backup_count_layout = QHBoxLayout()
        backup_count_layout.addWidget(QLabel("保留备份数量:"))
        self.backup_count_spin = QSpinBox()
        self.backup_count_spin.setRange(1, 10)
        self.backup_count_spin.setValue(3)
        self.backup_count_spin.setFixedWidth(80)
        backup_count_layout.addWidget(self.backup_count_spin)
        backup_count_layout.addStretch()
        backup_layout.addLayout(backup_count_layout)

        layout.addWidget(backup_group)

        # 用户体验设置组
        ux_group = QGroupBox("用户体验")
        ux_layout = QVBoxLayout(ux_group)
        ux_layout.setSpacing(8)

        self.enable_drag_drop_cb = QCheckBox("启用拖拽支持")
        ux_layout.addWidget(self.enable_drag_drop_cb)

        self.show_tooltips_cb = QCheckBox("显示工具提示")
        ux_layout.addWidget(self.show_tooltips_cb)

        self.enable_shortcuts_cb = QCheckBox("启用快捷键")
        ux_layout.addWidget(self.enable_shortcuts_cb)

        self.minimize_to_tray_cb = QCheckBox("最小化到系统托盘")
        self.minimize_to_tray_cb.setToolTip("关闭窗口时最小化到系统托盘而不是退出")
        ux_layout.addWidget(self.minimize_to_tray_cb)

        layout.addWidget(ux_group)

        # 性能设置组
        performance_group = QGroupBox("性能设置")
        performance_layout = QVBoxLayout(performance_group)
        performance_layout.setSpacing(8)

        self.enable_animations_cb = QCheckBox("启用界面动画")
        performance_layout.addWidget(self.enable_animations_cb)

        max_log_lines_layout = QHBoxLayout()
        max_log_lines_layout.addWidget(QLabel("最大日志行数:"))
        self.max_log_lines_spin = QSpinBox()
        self.max_log_lines_spin.setRange(100, 10000)
        self.max_log_lines_spin.setValue(1000)
        self.max_log_lines_spin.setFixedWidth(100)
        max_log_lines_layout.addWidget(self.max_log_lines_spin)
        max_log_lines_layout.addStretch()
        performance_layout.addLayout(max_log_lines_layout)

        layout.addWidget(performance_group)

        layout.addStretch()

        # 设置滚动区域的内容
        scroll_area.setWidget(content_widget)
        self.tab_widget.addTab(scroll_area, "行为")
        
    def browse_directory(self, entry_widget, title):
        """浏览目录"""
        directory = QFileDialog.getExistingDirectory(self, title, entry_widget.text())
        if directory:
            entry_widget.setText(directory)
            
    def on_theme_changed(self, theme_name):
        """主题变更处理"""
        theme_map = {"默认": "default", "深色": "dark", "蓝色": "blue"}
        theme_key = theme_map.get(theme_name, "default")
        self.theme_changed.emit(theme_key)
        
    def load_settings(self):
        """加载设置"""
        # 常规设置
        startup_tab = self.config_manager.get_config("general.startup_tab", 0)
        self.startup_tab_combo.setCurrentIndex(startup_tab)
        
        self.remember_window_cb.setChecked(
            self.config_manager.get_config("general.remember_window", True))
        self.auto_save_settings_cb.setChecked(
            self.config_manager.get_config("general.auto_save_settings", True))
        
        language = self.config_manager.get_config("general.language", "简体中文")
        self.language_combo.setCurrentText(language)
        
        # 路径设置
        self.remember_last_paths_cb.setChecked(
            self.config_manager.get_config("paths.remember_last_paths", True))
        self.auto_create_dirs_cb.setChecked(
            self.config_manager.get_config("paths.auto_create_dirs", False))
        
        # 外观设置
        theme = self.config_manager.get_config("appearance.theme", "default")
        theme_map = {"default": "默认", "dark": "深色", "blue": "蓝色"}
        self.theme_combo.setCurrentText(theme_map.get(theme, "默认"))
        
        self.font_size_spin.setValue(
            self.config_manager.get_config("appearance.font_size", 9))
        self.log_font_size_spin.setValue(
            self.config_manager.get_config("appearance.log_font_size", 9))
        
        # 行为设置
        self.confirm_delete_cb.setChecked(
            self.config_manager.get_config("behavior.confirm_delete", True))
        self.confirm_overwrite_cb.setChecked(
            self.config_manager.get_config("behavior.confirm_overwrite", True))
        self.confirm_exit_cb.setChecked(
            self.config_manager.get_config("behavior.confirm_exit", False))
        
        self.auto_backup_cb.setChecked(
            self.config_manager.get_config("behavior.auto_backup", True))
        self.backup_count_spin.setValue(
            self.config_manager.get_config("behavior.backup_count", 3))

        # 用户体验设置
        self.enable_drag_drop_cb.setChecked(
            self.config_manager.get_config("behavior.enable_drag_drop", True))
        self.show_tooltips_cb.setChecked(
            self.config_manager.get_config("behavior.show_tooltips", True))
        self.enable_shortcuts_cb.setChecked(
            self.config_manager.get_config("behavior.enable_shortcuts", True))
        self.minimize_to_tray_cb.setChecked(
            self.config_manager.get_config("behavior.minimize_to_tray", True))

        # 性能设置
        self.enable_animations_cb.setChecked(
            self.config_manager.get_config("behavior.enable_animations", True))
        self.max_log_lines_spin.setValue(
            self.config_manager.get_config("behavior.max_log_lines", 1000))

    def save_settings(self):
        """保存设置"""
        # 常规设置
        self.config_manager.set_config("general.startup_tab",
                                     self.startup_tab_combo.currentIndex())
        self.config_manager.set_config("general.remember_window",
                                     self.remember_window_cb.isChecked())
        self.config_manager.set_config("general.auto_save_settings",
                                     self.auto_save_settings_cb.isChecked())
        self.config_manager.set_config("general.language",
                                     self.language_combo.currentText())

        # 路径设置
        self.config_manager.set_config("paths.remember_last_paths",
                                     self.remember_last_paths_cb.isChecked())
        self.config_manager.set_config("paths.auto_create_dirs",
                                     self.auto_create_dirs_cb.isChecked())

        # 外观设置
        theme_map = {"默认": "default", "深色": "dark", "蓝色": "blue"}
        theme = theme_map.get(self.theme_combo.currentText(), "default")
        self.config_manager.set_config("appearance.theme", theme)
        self.config_manager.set_config("appearance.font_size",
                                     self.font_size_spin.value())
        self.config_manager.set_config("appearance.log_font_size",
                                     self.log_font_size_spin.value())

        # 行为设置
        self.config_manager.set_config("behavior.confirm_delete",
                                     self.confirm_delete_cb.isChecked())
        self.config_manager.set_config("behavior.confirm_overwrite",
                                     self.confirm_overwrite_cb.isChecked())
        self.config_manager.set_config("behavior.confirm_exit",
                                     self.confirm_exit_cb.isChecked())
        self.config_manager.set_config("behavior.auto_backup",
                                     self.auto_backup_cb.isChecked())
        self.config_manager.set_config("behavior.backup_count",
                                     self.backup_count_spin.value())

        # 用户体验设置
        self.config_manager.set_config("behavior.enable_drag_drop",
                                     self.enable_drag_drop_cb.isChecked())
        self.config_manager.set_config("behavior.show_tooltips",
                                     self.show_tooltips_cb.isChecked())
        self.config_manager.set_config("behavior.enable_shortcuts",
                                     self.enable_shortcuts_cb.isChecked())
        self.config_manager.set_config("behavior.minimize_to_tray",
                                     self.minimize_to_tray_cb.isChecked())

        # 性能设置
        self.config_manager.set_config("behavior.enable_animations",
                                     self.enable_animations_cb.isChecked())
        self.config_manager.set_config("behavior.max_log_lines",
                                     self.max_log_lines_spin.value())

        # 保存配置到文件
        self.config_manager.save_user_config()
        print("设置已保存到配置文件")

    def reset_to_defaults(self):
        """重置为默认设置"""
        reply = QMessageBox.question(self, "确认重置",
                                   "确定要重置所有设置为默认值吗？",
                                   QMessageBox.StandardButton.Yes |
                                   QMessageBox.StandardButton.No)

        if reply == QMessageBox.StandardButton.Yes:
            # 重置为默认值
            self.startup_tab_combo.setCurrentIndex(0)
            self.remember_window_cb.setChecked(True)
            self.auto_save_settings_cb.setChecked(True)
            self.language_combo.setCurrentText("简体中文")

            self.remember_last_paths_cb.setChecked(True)
            self.auto_create_dirs_cb.setChecked(False)

            self.theme_combo.setCurrentText("默认")
            self.font_size_spin.setValue(9)
            self.log_font_size_spin.setValue(9)

            self.confirm_delete_cb.setChecked(True)
            self.confirm_overwrite_cb.setChecked(True)
            self.confirm_exit_cb.setChecked(False)
            self.auto_backup_cb.setChecked(True)
            self.backup_count_spin.setValue(3)

    def apply_settings(self):
        """应用设置"""
        self.save_settings()
        self.settings_applied.emit()
        QMessageBox.information(self, "设置", "设置已应用")

    def accept_settings(self):
        """确定并关闭"""
        self.save_settings()
        self.settings_applied.emit()
        self.accept()

    def get_current_theme(self):
        """获取当前选择的主题"""
        theme_map = {"默认": "default", "深色": "dark", "蓝色": "blue"}
        return theme_map.get(self.theme_combo.currentText(), "default")
