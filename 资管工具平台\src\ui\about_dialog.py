#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
关于对话框
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTextEdit, QFrame, QSpacerItem, 
                            QSizePolicy)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont, QIcon, QPixmap


class AboutDialog(QDialog):
    """关于对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("关于 - 资管工具平台")
        self.setFixedSize(500, 400)
        self.setModal(True)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题区域
        title_frame = self.create_title_frame()
        layout.addWidget(title_frame)
        
        # 信息区域
        info_text = self.create_info_text()
        layout.addWidget(info_text)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        ok_button = QPushButton("确定")
        ok_button.setMinimumWidth(80)
        ok_button.clicked.connect(self.accept)
        button_layout.addWidget(ok_button)
        
        layout.addLayout(button_layout)
        
    def create_title_frame(self):
        """创建标题区域"""
        frame = QFrame()
        frame.setFrameStyle(QFrame.Shape.StyledPanel)
        layout = QHBoxLayout(frame)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # 应用图标
        icon_label = QLabel()
        try:
            icon_label.setPixmap(QIcon("resources/icons/app_icon.png").pixmap(64, 64))
        except:
            icon_label.setText("📦")
            icon_label.setFont(QFont("Arial", 32))
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(icon_label)
        
        # 标题和版本信息
        title_layout = QVBoxLayout()
        
        title_label = QLabel("资管工具平台")
        title_label.setFont(QFont("Microsoft YaHei UI", 18, QFont.Weight.Bold))
        title_layout.addWidget(title_label)
        
        version_label = QLabel("版本 1.0.1")
        version_label.setFont(QFont("Microsoft YaHei UI", 10))
        version_label.setStyleSheet("color: #666666;")
        title_layout.addWidget(version_label)
        
        layout.addLayout(title_layout)
        layout.addStretch()
        
        return frame
        
    def create_info_text(self):
        """创建信息文本区域"""
        text_edit = QTextEdit()
        text_edit.setReadOnly(True)
        text_edit.setMaximumHeight(200)
        
        info_text = """
<h3>关于资管工具平台</h3>

<p><b>资管工具平台</b>是一个集成了多个资源管理工具的现代化桌面应用程序，旨在提高资源管理工作的效率。</p>

<h4>主要功能：</h4>
<ul>
<li><b>压缩包处理工具</b> - 智能识别和管理压缩包版本</li>
<li><b>整包工具</b> - 支持15种合并规则的文件整合工具</li>
<li><b>载包上传工具</b> - SVN检出和文件上传功能</li>
</ul>

<h4>技术信息：</h4>
<ul>
<li>基于 PyQt6 开发</li>
<li>支持多种文件格式和编码</li>
<li>现代化的用户界面设计</li>
<li>模块化架构，易于扩展</li>
</ul>

<h4>开发信息：</h4>
<p>
<b>开发者：</b>资源管理部<br>
<b>版本：</b>1.0.1<br>
<b>发布日期：</b>2025年1月30日
</p>

<p><i>本软件仅供内部使用。</i></p>
        """
        
        text_edit.setHtml(info_text)
        return text_edit
