{"window": {"width": 1400, "height": 900, "maximized": false, "position": {"x": -1, "y": -1}}, "theme": {"name": "default", "dark_mode": false}, "paths": {"last_archive_dir": "", "last_source_dir": "", "last_output_dir": "", "last_checkout_dir": "", "last_plus_dir": "", "last_minus_dir": ""}, "archive_processor": {"auto_backup": true, "confirm_delete": true, "dry_run_default": true}, "package_merger": {"default_encoding": "gbk", "backup_original": true, "overwrite_output": true, "default_file_type": "ini"}, "svn_uploader": {"remember_credentials": false, "auto_compress": true, "save_checkout_path": true, "auto_process_checkout": true}, "ui": {"show_tooltips": true, "auto_scroll_logs": true, "confirm_operations": true}, "license_server": {"enabled": true, "server_url": "http://localhost:5000", "api_key": "client-api-key-123", "timeout": 30, "offline_grace_period": 86400}, "app": {"version": "1.0.1"}}