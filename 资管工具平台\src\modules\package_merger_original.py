#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
整包工具模块 - 直接调用原工具
"""

import os
import subprocess
from pathlib import Path
from PyQt6.QtCore import QObject, pyqtSignal, QThread

# 使用本地复制的原工具文件
current_dir = Path(__file__).parent
original_tool_path = current_dir / "original_package_tool.py"

class PackageMergerWorker(QThread):
    """整包处理工作线程"""

    # 信号定义
    progress_updated = pyqtSignal(int)
    log_message = pyqtSignal(str)
    processing_finished = pyqtSignal(bool, str)

    def __init__(self, game_root: str, complete_dir: str):
        super().__init__()
        self.game_root = game_root
        self.complete_dir = complete_dir

    def run(self):
        """运行整包处理"""
        try:
            self.log_message.emit(f"开始整包处理...")
            self.log_message.emit(f"工作目录: {self.game_root}")
            self.log_message.emit(f"完整目录: {self.complete_dir}")
            self.progress_updated.emit(10)

            # 检查目录是否存在
            if not os.path.exists(self.game_root):
                error_msg = f"工作目录不存在: {self.game_root}"
                self.log_message.emit(error_msg)
                self.processing_finished.emit(False, error_msg)
                return

            if not os.path.exists(self.complete_dir):
                error_msg = f"完整目录不存在: {self.complete_dir}"
                self.log_message.emit(error_msg)
                self.processing_finished.emit(False, error_msg)
                return

            self.progress_updated.emit(20)

            # 检查工作目录中的资源文件
            self._check_resources()

            # 直接调用原工具
            try:
                # 切换到原工具所在目录
                original_cwd = os.getcwd()
                os.chdir(current_dir)

                self.progress_updated.emit(30)

                # 修改原工具文件中的路径并执行
                self._execute_original_tool()

                # 恢复原工作目录
                os.chdir(original_cwd)

                self.progress_updated.emit(90)

            except Exception as e:
                error_msg = f"❌ 原工具执行失败: {str(e)}"
                self.log_message.emit(error_msg)
                self.processing_finished.emit(False, error_msg)
                return

            self.progress_updated.emit(100)
            self.processing_finished.emit(True, "整包处理完成")

        except Exception as e:
            error_msg = f"❌ 整包处理失败: {str(e)}"
            self.log_message.emit(error_msg)
            self.processing_finished.emit(False, error_msg)

    def _check_resources(self):
        """检查工作目录中的资源文件"""
        try:
            # 检查常见的资源目录
            resource_dirs = ['+ani', '+ini', 'script', '+ui', '+script']
            missing_resources = []
            found_resources = []

            for dir_name in resource_dirs:
                dir_path = os.path.join(self.game_root, dir_name)
                if os.path.exists(dir_path) and os.path.isdir(dir_path):
                    # 检查目录是否为空
                    files = os.listdir(dir_path)
                    if files:
                        found_resources.append(dir_name)
                    else:
                        missing_resources.append(f"{dir_name} (目录为空)")
                else:
                    missing_resources.append(dir_name)

            # 报告发现的资源
            if found_resources:
                self.log_message.emit(f"✅ 发现资源目录: {', '.join(found_resources)}")

            # 不再显示缺失资源目录提示

            # 检查是否完全没有资源
            if not found_resources:
                self.log_message.emit("⚠️ 警告: 未发现任何资源文件，可能无法正常处理")

        except Exception as e:
            self.log_message.emit(f"❌ 资源检查失败: {str(e)}")

    def _execute_original_tool(self):
        """执行原工具"""
        self.log_message.emit("🔧 开始执行整包处理...")

        # 创建临时的执行脚本
        script_content = f'''# -*- coding: utf-8 -*-
import sys
import os
sys.path.insert(0, r"{current_dir}")

# 设置控制台编码为UTF-8
if os.name == 'nt':  # Windows
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

from original_package_tool import process_game_resources

if __name__ == "__main__":
    process_game_resources(r"{self.game_root}", r"{self.complete_dir}")
'''

        temp_script = current_dir / "temp_execute.py"
        with open(temp_script, 'w', encoding='utf-8') as f:
            f.write(script_content)

        try:
            # 使用Popen实现实时输出，但只显示错误和重要信息
            process = subprocess.Popen([
                'python', str(temp_script)
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE,
               text=True, cwd=str(current_dir), bufsize=1, universal_newlines=True,
               encoding='utf-8', errors='replace')

            # 实时读取输出，显示错误和重要信息
            error_keywords = [
                '错误', 'error', '失败', 'failed', '异常', 'exception',
                '警告', 'warning', '不存在', 'not found', '无法访问',
                'cannot access', '找不到', 'not found', '缺失', 'missing'
            ]
            important_keywords = [
                '处理完成！总共耗时', '所有文件处理完成！', '所有资源文件都已找到！',
                '原始文件不存在', '原始资源不存在', '资源不存在', '目录不存在',
                '请手动整合', '未整合', '配置未整合',
                '.size生成', 'size文件', '无法生成',
                '[错误]', '[警告]', '警告',
                '发现', '个文件存在缺失资源', '文件:', '缺失的资源文件:', '- '
            ]

            # 需要过滤掉的信息（即使包含重要关键词也要过滤）
            filter_out_keywords = [
                '已复制文件:', '在其他位置找到文件:'
            ]

            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    clean_output = output.strip()
                    if clean_output:
                        # 首先检查是否需要过滤掉
                        should_filter_out = any(keyword in clean_output for keyword in filter_out_keywords)

                        if not should_filter_out:
                            # 只显示包含错误关键词或重要关键词的日志
                            lower_output = clean_output.lower()
                            if (any(keyword in lower_output for keyword in error_keywords) or
                                any(keyword in clean_output for keyword in important_keywords)):
                                self.log_message.emit(f"  {clean_output}")

            # 获取返回码
            return_code = process.poll()

            # 读取剩余的错误输出
            stderr_output = process.stderr.read()

            if return_code != 0:
                error_msg = f"❌ 原工具执行失败 (返回码: {return_code})"
                if stderr_output:
                    error_msg += f": {stderr_output}"
                    self.log_message.emit(f"❌ 错误详情: {stderr_output}")
                raise Exception(error_msg)

            self.log_message.emit("✅ 整包处理完成")

        finally:
            # 清理临时文件
            if temp_script.exists():
                temp_script.unlink()
