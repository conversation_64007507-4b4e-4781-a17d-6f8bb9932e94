@echo off
chcp 65001 >nul
title 资管工具平台

echo 正在启动资管工具平台...
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python，请先安装Python 3.8或更高版本
    pause
    exit /b 1
)

REM 检查依赖包
echo 检查依赖包...
python -c "import PyQt6" >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误：依赖包安装失败
        pause
        exit /b 1
    )
)

REM 启动应用程序
echo 启动应用程序...
python run.py

if errorlevel 1 (
    echo.
    echo 应用程序异常退出
    pause
)
