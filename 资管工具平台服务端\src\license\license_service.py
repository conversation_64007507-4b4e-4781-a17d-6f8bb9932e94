#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
许可证服务API
"""

from flask import Blueprint, request, jsonify
from datetime import datetime, timedelta
import hashlib
import json
import sqlite3
import os
from ..database import get_db_connection
from ..auth.auth_manager import require_api_key

license_bp = Blueprint('license', __name__, url_prefix='/api/license')

def get_license_db_connection():
    """获取许可证数据库连接"""
    db_path = os.path.join(os.path.dirname(__file__), '..', '..', 'data', 'database', 'license.db')
    return sqlite3.connect(db_path)

@license_bp.route('/validate', methods=['POST'])
@require_api_key
def validate_activation_code():
    """验证激活码"""
    try:
        data = request.get_json()
        activation_code = data.get('activation_code', '').strip()
        machine_id = data.get('machine_id', '').strip()
        machine_info = data.get('machine_info', {})
        
        if not activation_code or not machine_id:
            return jsonify({
                'success': False,
                'error': 'missing_parameters',
                'message': '缺少必要参数'
            }), 400
        
        conn = get_license_db_connection()
        cursor = conn.cursor()
        
        # 查询激活码信息
        cursor.execute("""
            SELECT code, license_type, max_devices, expires_at, status
            FROM activation_codes 
            WHERE code = ?
        """, (activation_code,))
        
        code_info = cursor.fetchone()
        
        if not code_info:
            # 记录验证失败日志
            log_license_check(cursor, activation_code, machine_id, 'failed', 
                            request.remote_addr, request.headers.get('User-Agent'),
                            '激活码不存在')
            conn.commit()
            conn.close()
            return jsonify({
                'success': False,
                'error': 'invalid_code',
                'message': '激活码不存在'
            }), 404
        
        code, license_type, max_devices, expires_at, status = code_info
        
        # 检查激活码状态
        if status != 'active':
            log_license_check(cursor, activation_code, machine_id, 'failed',
                            request.remote_addr, request.headers.get('User-Agent'),
                            f'激活码状态: {status}')
            conn.commit()
            conn.close()
            return jsonify({
                'success': False,
                'error': 'code_disabled',
                'message': f'激活码已{status}'
            }), 403
        
        # 检查过期时间
        expire_time = datetime.fromisoformat(expires_at)
        if datetime.now() > expire_time:
            # 更新激活码状态为过期
            cursor.execute("""
                UPDATE activation_codes 
                SET status = 'expired', updated_at = CURRENT_TIMESTAMP
                WHERE code = ?
            """, (activation_code,))
            
            log_license_check(cursor, activation_code, machine_id, 'failed',
                            request.remote_addr, request.headers.get('User-Agent'),
                            '激活码已过期')
            conn.commit()
            conn.close()
            return jsonify({
                'success': False,
                'error': 'code_expired',
                'message': '激活码已过期'
            }), 403
        
        # 检查设备数量限制
        cursor.execute("""
            SELECT COUNT(*) FROM active_licenses 
            WHERE activation_code = ? AND status = 'active'
        """, (activation_code,))
        
        active_count = cursor.fetchone()[0]
        
        # 检查当前机器是否已经激活
        cursor.execute("""
            SELECT id FROM active_licenses 
            WHERE activation_code = ? AND machine_id = ? AND status = 'active'
        """, (activation_code, machine_id))
        
        existing_license = cursor.fetchone()
        
        if existing_license:
            # 更新最后检查时间
            cursor.execute("""
                UPDATE active_licenses 
                SET last_check = CURRENT_TIMESTAMP
                WHERE activation_code = ? AND machine_id = ?
            """, (activation_code, machine_id))
            
            log_license_check(cursor, activation_code, machine_id, 'success',
                            request.remote_addr, request.headers.get('User-Agent'),
                            '许可证验证成功（已存在）')
            conn.commit()
            conn.close()
            
            return jsonify({
                'success': True,
                'message': '许可证验证成功',
                'license_info': {
                    'activation_code': activation_code,
                    'license_type': license_type,
                    'expires_at': expires_at,
                    'max_devices': max_devices,
                    'current_devices': active_count
                }
            })
        
        elif active_count >= max_devices:
            log_license_check(cursor, activation_code, machine_id, 'failed',
                            request.remote_addr, request.headers.get('User-Agent'),
                            f'设备数量超限: {active_count}/{max_devices}')
            conn.commit()
            conn.close()
            return jsonify({
                'success': False,
                'error': 'device_limit_exceeded',
                'message': f'设备数量已达上限 ({max_devices})'
            }), 403
        
        # 激活新设备
        cursor.execute("""
            INSERT INTO active_licenses 
            (activation_code, machine_id, machine_info, status)
            VALUES (?, ?, ?, 'active')
        """, (activation_code, machine_id, json.dumps(machine_info)))
        
        # 更新激活码状态为已使用
        cursor.execute("""
            UPDATE activation_codes 
            SET status = 'used', updated_at = CURRENT_TIMESTAMP
            WHERE code = ?
        """, (activation_code,))
        
        log_license_check(cursor, activation_code, machine_id, 'success',
                        request.remote_addr, request.headers.get('User-Agent'),
                        '新设备激活成功')
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': '激活成功',
            'license_info': {
                'activation_code': activation_code,
                'license_type': license_type,
                'expires_at': expires_at,
                'max_devices': max_devices,
                'current_devices': active_count + 1
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'server_error',
            'message': f'服务器内部错误: {str(e)}'
        }), 500

@license_bp.route('/status', methods=['POST'])
@require_api_key
def check_license_status():
    """检查许可证状态"""
    try:
        data = request.get_json()
        activation_code = data.get('activation_code', '').strip()
        machine_id = data.get('machine_id', '').strip()
        
        if not activation_code or not machine_id:
            return jsonify({
                'success': False,
                'error': 'missing_parameters',
                'message': '缺少必要参数'
            }), 400
        
        conn = get_license_db_connection()
        cursor = conn.cursor()
        
        # 查询许可证信息
        cursor.execute("""
            SELECT al.status, ac.license_type, ac.expires_at, ac.max_devices, ac.status as code_status
            FROM active_licenses al
            JOIN activation_codes ac ON al.activation_code = ac.code
            WHERE al.activation_code = ? AND al.machine_id = ?
        """, (activation_code, machine_id))
        
        license_info = cursor.fetchone()
        
        if not license_info:
            log_license_check(cursor, activation_code, machine_id, 'failed',
                            request.remote_addr, request.headers.get('User-Agent'),
                            '许可证不存在')
            conn.commit()
            conn.close()
            return jsonify({
                'success': False,
                'error': 'license_not_found',
                'message': '许可证不存在'
            }), 404
        
        license_status, license_type, expires_at, max_devices, code_status = license_info
        
        # 检查激活码状态
        if code_status not in ['used', 'active']:
            log_license_check(cursor, activation_code, machine_id, 'failed',
                            request.remote_addr, request.headers.get('User-Agent'),
                            f'激活码状态异常: {code_status}')
            conn.commit()
            conn.close()
            return jsonify({
                'success': False,
                'error': 'code_disabled',
                'message': '激活码已被禁用'
            }), 403
        
        # 检查过期时间
        expire_time = datetime.fromisoformat(expires_at)
        if datetime.now() > expire_time:
            # 更新许可证状态为过期
            cursor.execute("""
                UPDATE active_licenses 
                SET status = 'expired'
                WHERE activation_code = ? AND machine_id = ?
            """, (activation_code, machine_id))
            
            log_license_check(cursor, activation_code, machine_id, 'failed',
                            request.remote_addr, request.headers.get('User-Agent'),
                            '许可证已过期')
            conn.commit()
            conn.close()
            return jsonify({
                'success': False,
                'error': 'license_expired',
                'message': '许可证已过期'
            }), 403
        
        # 更新最后检查时间
        cursor.execute("""
            UPDATE active_licenses 
            SET last_check = CURRENT_TIMESTAMP
            WHERE activation_code = ? AND machine_id = ?
        """, (activation_code, machine_id))
        
        # 获取当前设备数量
        cursor.execute("""
            SELECT COUNT(*) FROM active_licenses 
            WHERE activation_code = ? AND status = 'active'
        """, (activation_code,))
        current_devices = cursor.fetchone()[0]
        
        log_license_check(cursor, activation_code, machine_id, 'success',
                        request.remote_addr, request.headers.get('User-Agent'),
                        '状态检查成功')
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': '许可证有效',
            'license_info': {
                'activation_code': activation_code,
                'license_type': license_type,
                'expires_at': expires_at,
                'max_devices': max_devices,
                'current_devices': current_devices,
                'status': license_status
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'server_error',
            'message': f'服务器内部错误: {str(e)}'
        }), 500

@license_bp.route('/deactivate', methods=['POST'])
@require_api_key
def deactivate_license():
    """停用许可证"""
    try:
        data = request.get_json()
        activation_code = data.get('activation_code', '').strip()
        machine_id = data.get('machine_id', '').strip()
        
        if not activation_code or not machine_id:
            return jsonify({
                'success': False,
                'error': 'missing_parameters',
                'message': '缺少必要参数'
            }), 400
        
        conn = get_license_db_connection()
        cursor = conn.cursor()
        
        # 停用许可证
        cursor.execute("""
            UPDATE active_licenses 
            SET status = 'deactivated'
            WHERE activation_code = ? AND machine_id = ?
        """, (activation_code, machine_id))
        
        if cursor.rowcount == 0:
            conn.close()
            return jsonify({
                'success': False,
                'error': 'license_not_found',
                'message': '许可证不存在'
            }), 404
        
        log_license_check(cursor, activation_code, machine_id, 'deactivated',
                        request.remote_addr, request.headers.get('User-Agent'),
                        '许可证已停用')
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': '许可证已停用'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'server_error',
            'message': f'服务器内部错误: {str(e)}'
        }), 500

def log_license_check(cursor, activation_code, machine_id, status, ip_address, user_agent, result):
    """记录许可证检查日志"""
    try:
        cursor.execute("""
            INSERT INTO license_check_logs 
            (activation_code, machine_id, status, ip_address, user_agent, result)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (activation_code, machine_id, status, ip_address, user_agent, result))
    except Exception as e:
        print(f"记录日志失败: {e}")
