🌐 更新服务器使用说明

📋 快速开始:
1. 配置IP地址: python setup_ip_server.py
2. 配置防火墙: setup_firewall.bat (以管理员身份运行)
3. 启动服务器: python server.py
4. 测试访问: 浏览器打开 http://你的IP:8080/api/status

📁 文件说明:
- server.py: 更新服务器主程序
- setup_ip_server.py: IP配置工具
- setup_firewall.bat: 防火墙配置脚本
- manage_versions.py: 版本管理工具
- start_server.bat: 服务器启动脚本

🔧 版本管理:
运行 python manage_versions.py 可以:
- 添加新版本
- 删除版本
- 查看版本列表

🌐 访问地址:
- 本机: http://127.0.0.1:8080
- 局域网: http://你的IP:8080
- 状态页面: /api/status
- 版本检查: /api/version/check
- 文件下载: /api/download/文件名

💡 注意事项:
- 确保防火墙允许端口8080
- 确保设备在同一网络
- 服务器需要保持运行状态
