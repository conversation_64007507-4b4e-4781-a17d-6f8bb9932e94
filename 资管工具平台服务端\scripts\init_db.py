#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库初始化脚本
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.app import create_app
from src.database import db
from src.license.models import LicenseRecord, LicenseValidation
from src.update.models import VersionInfo, UpdateChannel, ClientInfo


def init_database():
    """初始化数据库"""
    print("🔧 初始化数据库...")
    
    # 创建应用上下文
    app = create_app('production')
    
    with app.app_context():
        try:
            # 创建所有表
            db.create_all()
            print("✅ 数据库表创建完成")
            
            # 创建初始数据
            create_initial_data()
            
            print("🎉 数据库初始化完成！")
            
        except Exception as e:
            print(f"❌ 数据库初始化失败: {e}")
            raise


def create_initial_data():
    """创建初始数据"""
    print("📊 创建初始数据...")
    
    # 创建更新渠道
    create_update_channels()
    
    # 创建初始版本
    create_initial_version()
    
    # 创建测试许可证（仅开发环境）
    if os.getenv('FLASK_ENV') == 'development':
        create_test_licenses()
    
    db.session.commit()
    print("✅ 初始数据创建完成")


def create_update_channels():
    """创建更新渠道"""
    channels = [
        {
            'name': 'stable',
            'description': '稳定版本渠道',
            'auto_update': True,
            'beta_updates': False,
            'force_update_threshold': '0.8.0'
        },
        {
            'name': 'beta',
            'description': '测试版本渠道',
            'auto_update': True,
            'beta_updates': True,
            'force_update_threshold': '0.9.0'
        },
        {
            'name': 'dev',
            'description': '开发版本渠道',
            'auto_update': False,
            'beta_updates': True,
            'force_update_threshold': None
        }
    ]
    
    for channel_data in channels:
        existing_channel = UpdateChannel.query.filter_by(name=channel_data['name']).first()
        if not existing_channel:
            channel = UpdateChannel(**channel_data)
            db.session.add(channel)
            print(f"  ✅ 创建更新渠道: {channel_data['name']}")


def create_initial_version():
    """创建初始版本"""
    platforms = [
        {'platform': 'windows', 'architecture': 'x64'},
        {'platform': 'windows', 'architecture': 'x86'},
        {'platform': 'linux', 'architecture': 'x64'},
        {'platform': 'macos', 'architecture': 'x64'},
        {'platform': 'macos', 'architecture': 'arm64'}
    ]
    
    version = '1.0.0'
    
    for platform_info in platforms:
        existing_version = VersionInfo.query.filter_by(
            version=version,
            platform=platform_info['platform'],
            architecture=platform_info['architecture']
        ).first()
        
        if not existing_version:
            version_info = VersionInfo(
                version=version,
                platform=platform_info['platform'],
                architecture=platform_info['architecture'],
                release_date=datetime.utcnow(),
                description='资管工具平台初始版本',
                changelog='''
# 版本 1.0.0

## 新功能
- ✅ 压缩包处理功能
- ✅ 整包工具功能  
- ✅ SVN上传功能
- ✅ 完整的授权系统
- ✅ 自动更新功能

## 改进
- 🔧 优化用户界面
- 🔧 提升性能表现
- 🔧 增强稳定性

## 修复
- 🐛 修复已知问题
                '''.strip(),
                file_path=f'updates/v{version}/{platform_info["platform"]}_{platform_info["architecture"]}.zip',
                file_size=50 * 1024 * 1024,  # 50MB
                is_active=True,
                is_force_update=False
            )
            
            db.session.add(version_info)
            print(f"  ✅ 创建初始版本: {version} - {platform_info['platform']} {platform_info['architecture']}")


def create_test_licenses():
    """创建测试许可证（仅开发环境）"""
    print("🧪 创建测试许可证...")
    
    test_licenses = [
        {
            'activation_code': 'TEST-0001-FREE-0000-0000-0000-00',
            'license_type': 'free',
            'user_name': '测试用户（免费版）',
            'email': '<EMAIL>',
            'company_name': '测试公司',
            'expire_date': None,
            'max_activations': 1
        },
        {
            'activation_code': 'TEST-0002-TRIAL-0000-0000-0000-00',
            'license_type': 'trial',
            'user_name': '测试用户（试用版）',
            'email': '<EMAIL>',
            'company_name': '测试公司',
            'expire_date': datetime.utcnow() + timedelta(days=30),
            'max_activations': 1
        },
        {
            'activation_code': 'TEST-0003-PROF-0000-0000-0000-00',
            'license_type': 'professional',
            'user_name': '测试用户（专业版）',
            'email': '<EMAIL>',
            'company_name': '测试公司',
            'expire_date': datetime.utcnow() + timedelta(days=365),
            'max_activations': 3
        },
        {
            'activation_code': 'TEST-0004-ENTER-0000-0000-0000-00',
            'license_type': 'enterprise',
            'user_name': '测试用户（企业版）',
            'email': '<EMAIL>',
            'company_name': '测试公司',
            'expire_date': datetime.utcnow() + timedelta(days=365),
            'max_activations': 10
        }
    ]
    
    for license_data in test_licenses:
        existing_license = LicenseRecord.query.filter_by(
            activation_code=license_data['activation_code']
        ).first()
        
        if not existing_license:
            license_record = LicenseRecord(
                activation_code=license_data['activation_code'],
                license_type=license_data['license_type'],
                user_name=license_data['user_name'],
                email=license_data['email'],
                company_name=license_data['company_name'],
                created_date=datetime.utcnow(),
                expire_date=license_data['expire_date'],
                max_activations=license_data['max_activations'],
                is_active=True
            )
            
            db.session.add(license_record)
            print(f"  ✅ 创建测试许可证: {license_data['license_type']} - {license_data['activation_code']}")


def create_directories():
    """创建必要的目录"""
    print("📁 创建目录结构...")
    
    directories = [
        'data/database',
        'data/uploads',
        'data/logs',
        'data/temp',
        'data/backups',
        'data/uploads/updates',
        'config/ssl'
    ]
    
    for directory in directories:
        dir_path = Path(directory)
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"  ✅ 创建目录: {directory}")


def check_environment():
    """检查环境"""
    print("🔍 检查环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ Python版本过低，需要3.8或更高版本")
        sys.exit(1)
    
    print(f"  ✅ Python版本: {sys.version}")
    
    # 检查必要的环境变量
    required_env_vars = ['SECRET_KEY', 'JWT_SECRET']
    missing_vars = []
    
    for var in required_env_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"⚠️  缺少环境变量: {', '.join(missing_vars)}")
        print("   将使用默认值，生产环境请设置正确的值")
    else:
        print("  ✅ 环境变量检查通过")


def main():
    """主函数"""
    print("🚀 资管工具平台服务端数据库初始化")
    print("=" * 50)
    
    try:
        # 检查环境
        check_environment()
        
        # 创建目录
        create_directories()
        
        # 初始化数据库
        init_database()
        
        print("\n" + "=" * 50)
        print("🎉 初始化完成！")
        print("\n📋 测试许可证（仅开发环境）:")
        print("  免费版: TEST-0001-FREE-0000-0000-0000-00")
        print("  试用版: TEST-0002-TRIAL-0000-0000-0000-00")
        print("  专业版: TEST-0003-PROF-0000-0000-0000-00")
        print("  企业版: TEST-0004-ENTER-0000-0000-0000-00")
        print("\n🔧 下一步:")
        print("  1. 启动服务: python src/app.py")
        print("  2. 访问健康检查: http://localhost:5000/health")
        print("  3. 查看API信息: http://localhost:5000/api/info")
        
    except Exception as e:
        print(f"\n❌ 初始化失败: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
