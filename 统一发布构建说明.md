# 🚀 统一发布构建工具使用说明

## 📋 工具介绍

统一发布构建工具可以一键完成以下操作：
1. **版本号管理** - 统一更新所有文件中的版本号
2. **自动构建** - 构建客户端和服务端
3. **打包发布** - 创建标准化的发布包
4. **生成文档** - 自动生成发布说明和更新日志

## 🛠️ 使用方法

### **方法1: 使用批处理文件 (推荐)**
```bash
# 双击运行或在命令行执行
build_release.bat
```

### **方法2: 直接运行Python脚本**
```bash
python build_release.py
```

## 📊 构建流程

### **1. 版本号管理**
```
是否更新版本号? 当前: 1.0.1 (y/N): y
请输入新版本号 (如 1.0.2): 1.0.2

🔄 更新版本号: 1.0.1 → 1.0.2
✅ 版本号更新成功: 1.0.2
```

### **2. 更新说明输入**
```
📝 请输入本次更新的说明:
(每行一个更新点，空行结束输入)
• 修复了整包工具的编码问题
• 优化了SVN操作保护机制
• 改进了用户界面响应速度
• 
```

### **3. 确认构建信息**
```
📊 构建信息:
- 版本号: 1.0.2
- 更新说明: 3 条
- 输出目录: E:\...\发布包

确认开始构建? (y/N): y
```

### **4. 自动构建过程**
```
🔨 开始构建 v1.0.2...

🧹 清理构建目录...
✅ 清理: 资管工具平台/dist
✅ 清理: 资管工具平台/build

🔨 构建客户端...
✅ 客户端构建成功

🔨 构建服务端...
✅ 服务端准备完成 (Python源码)

📦 创建客户端发布包...
✅ 客户端包: ResourceManagementTool_v1.0.2_Windows_x64.zip

📦 创建服务端发布包...
✅ 服务端包: ResourceManagementServer_v1.0.2.zip

📦 创建更新包...
✅ 更新包: windows_x64.zip

📋 生成发布说明...
✅ 发布说明: 发布说明_v1.0.2.md

🎉 构建完成!
✅ 成功创建 3/3 个发布包
📋 发布说明: 发布说明_v1.0.2.md
📁 输出目录: 发布包

🚀 v1.0.2 发布包已准备就绪!
```

## 📁 输出结构

构建完成后，`发布包/` 目录结构如下：

```
发布包/
├── 客户端/
│   └── ResourceManagementTool_v1.0.2_Windows_x64.zip
├── 服务端/
│   └── ResourceManagementServer_v1.0.2.zip
├── 更新包/
│   └── windows_x64.zip
├── 发布说明_v1.0.2.md
└── README.txt
```

## 📋 发布包内容

### **🖥️ 客户端包**
- **文件**: `ResourceManagementTool_v{版本号}_Windows_x64.zip`
- **内容**: 完整的Windows客户端程序
- **用途**: 分发给最终用户

### **🖥️ 服务端包**
- **文件**: `ResourceManagementServer_v{版本号}.zip`
- **内容**: 完整的服务端源码和配置
- **用途**: 服务器部署

### **🔄 更新包**
- **文件**: `windows_x64.zip`
- **内容**: 客户端自动更新文件
- **用途**: 放置在更新服务器

### **📋 发布说明**
- **文件**: `发布说明_v{版本号}.md`
- **内容**: 详细的版本信息和更新说明
- **用途**: 用户了解新版本特性

## ⚙️ 配置要求

### **环境要求**
- Python 3.7+ 环境
- PyInstaller (客户端构建)
- 完整的项目源码

### **目录结构要求**
```
项目根目录/
├── 资管工具平台/          # 客户端项目
│   ├── src/              # 源码
│   ├── build.bat         # 构建脚本
│   └── ...
├── 资管工具平台服务端/     # 服务端项目
│   ├── src/              # 源码
│   └── ...
├── update_version.py     # 版本管理工具
├── build_release.py      # 统一构建脚本
└── build_release.bat     # 批处理启动器
```

## 🔧 自定义配置

### **修改构建脚本**
如需自定义构建过程，可以编辑 `build_release.py`：

```python
# 修改发布包命名规则
package_name = f"CustomName_v{self.current_version}_Windows_x64.zip"

# 修改构建输出目录
self.release_dir = self.project_root / "自定义发布目录"

# 添加额外的构建步骤
def custom_build_step(self):
    # 自定义构建逻辑
    pass
```

### **修改发布说明模板**
在 `generate_release_notes()` 方法中修改模板：

```python
release_notes = f"""# 自定义发布说明模板
版本: {self.current_version}
更新内容: {changelog}
...
"""
```

## 🚨 常见问题

### **Q: 构建失败怎么办？**
A: 检查以下项目：
1. Python环境是否正确安装
2. 项目目录结构是否完整
3. `build.bat` 脚本是否存在
4. 是否有足够的磁盘空间

### **Q: 版本号更新失败？**
A: 确保：
1. `update_version.py` 文件存在
2. 源码文件没有被其他程序占用
3. 版本号格式正确 (x.y.z)

### **Q: 客户端构建失败？**
A: 检查：
1. `资管工具平台/build.bat` 是否存在
2. PyInstaller 是否正确安装
3. 源码是否有语法错误

### **Q: 如何跳过版本号更新？**
A: 在询问时直接按回车或输入 'N'

## 💡 最佳实践

### **发布前检查清单**
- [ ] 代码已提交到版本控制
- [ ] 所有功能测试通过
- [ ] 版本号符合语义化规范
- [ ] 更新说明详细准确
- [ ] 构建环境干净无误

### **版本号规范**
- **修复版本**: 1.0.1 → 1.0.2
- **功能版本**: 1.0.2 → 1.1.0  
- **重大版本**: 1.1.0 → 2.0.0

### **更新说明建议**
- 使用简洁明了的语言
- 按重要性排序
- 包含用户关心的改进
- 避免技术术语

## 🎯 使用示例

### **日常版本发布**
```bash
# 1. 运行构建工具
build_release.bat

# 2. 更新版本号 (如 1.0.1 → 1.0.2)
是否更新版本号? (y/N): y
请输入新版本号: 1.0.2

# 3. 输入更新说明
• 修复了文件上传问题
• 优化了界面响应速度
• 

# 4. 确认构建
确认开始构建? (y/N): y

# 5. 等待完成，检查输出
```

### **重大版本发布**
```bash
# 更新到新的主版本
请输入新版本号: 2.0.0

# 详细的更新说明
• 全新的用户界面设计
• 重构了核心架构
• 新增批量处理功能
• 提升了处理性能
• 改进了错误处理机制
• 
```

现在您有了完整的统一发布构建工具！🎉

**使用方法**: 直接双击 `build_release.bat` 或运行 `python build_release.py` 即可开始构建流程。
