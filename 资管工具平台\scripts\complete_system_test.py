#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整系统测试脚本
测试授权更新系统的所有功能
"""

import sys
import os
import tempfile
import shutil
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))


def test_activation_system():
    """测试激活系统"""
    print("🎫 测试激活系统")
    print("-" * 40)
    
    from license.activation_code import ActivationCodeGenerator, ActivationCodeValidator
    from license.license_types import LicenseType
    
    generator = ActivationCodeGenerator()
    validator = ActivationCodeValidator()
    
    # 测试不同类型的激活码
    test_cases = [
        (LicenseType.TRIAL, "试用版", 30),
        (LicenseType.PROFESSIONAL, "专业版", 365),
        (LicenseType.ENTERPRISE, "企业版", 365)
    ]
    
    results = {}
    
    for license_type, name, days in test_cases:
        print(f"\n📋 测试 {name}:")
        
        # 生成激活码
        license_data = {
            'license_type': license_type,
            'user_name': f'{name}用户',
            'email': f'{license_type.value}@test.com',
            'expire_date': (datetime.now() + timedelta(days=days)).isoformat()
        }
        
        activation_code = generator.generate_activation_code(license_data)
        print(f"   生成激活码: {activation_code}")
        
        # 验证激活码
        result = validator.validate_and_extract_license(activation_code)
        if result:
            print(f"   ✅ 验证成功")
            print(f"   类型: {result.license_type.value}")
            print(f"   用户: {result.user_name}")
            results[license_type] = activation_code
        else:
            print(f"   ❌ 验证失败")
    
    return results


def test_license_manager():
    """测试许可证管理器"""
    print("\n\n🔐 测试许可证管理器")
    print("-" * 40)
    
    from license.license_manager import LicenseManager
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    
    try:
        manager = LicenseManager(temp_dir)
        
        print(f"📊 初始状态:")
        print(f"   许可证类型: {manager.current_license.license_type.value}")
        print(f"   硬件ID: {manager.hardware_id}")
        print(f"   状态: {manager.get_license_status().value}")
        
        # 测试功能权限
        print(f"\n🔧 功能权限测试:")
        features = [
            'archive_processor', 'package_merger', 'svn_uploader', 
            'themes', 'auto_update', 'advanced_settings'
        ]
        
        for feature in features:
            enabled = manager.is_feature_enabled(feature)
            status = "✅" if enabled else "❌"
            print(f"   {status} {feature}")
        
        # 测试试用激活
        print(f"\n🔄 测试试用激活:")
        success, message = manager.start_trial({
            'user_name': '测试用户',
            'email': '<EMAIL>'
        })
        print(f"   结果: {message}")
        
        if success:
            print(f"   新类型: {manager.current_license.license_type.value}")
            print(f"   剩余天数: {manager.get_days_until_expiry()}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    
    finally:
        # 清理临时目录
        shutil.rmtree(temp_dir, ignore_errors=True)


def test_update_system():
    """测试更新系统"""
    print("\n\n🔄 测试更新系统")
    print("-" * 40)
    
    from updater.version_checker import VersionChecker, UpdateChannel
    from updater.update_manager import UpdateManager
    from license.license_manager import LicenseManager
    from utils.config_manager import ConfigManager
    
    # 创建临时环境
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 创建管理器
        license_manager = LicenseManager(temp_dir)
        config_manager = ConfigManager()
        
        # 激活试用版以测试更新功能
        license_manager.start_trial({'user_name': '测试用户'})
        
        print(f"📊 更新检查测试:")
        print(f"   当前版本: 1.0.0")
        print(f"   许可证: {license_manager.current_license.license_type.value}")
        
        # 测试版本检查器
        checker = VersionChecker(
            current_version="1.0.0",
            license_manager=license_manager
        )
        
        result = checker.check_for_updates(UpdateChannel.STABLE)
        print(f"   检查结果: {'有更新' if result.has_update else '无更新'}")
        
        if result.error_message:
            print(f"   错误: {result.error_message}")
        
        if result.has_update and result.latest_version:
            version_info = result.latest_version
            print(f"   最新版本: {version_info.version}")
            print(f"   文件大小: {version_info.file_size / (1024*1024):.1f} MB")
            print(f"   更新类型: {version_info.update_type.value}")
        
        # 测试更新管理器
        print(f"\n📦 更新管理器测试:")
        update_manager = UpdateManager(license_manager, config_manager)
        
        settings = update_manager.get_update_settings()
        print(f"   自动检查: {settings['auto_check']}")
        print(f"   检查间隔: {settings['check_interval']} 小时")
        print(f"   更新渠道: {settings['update_channel']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    
    finally:
        # 清理临时目录
        shutil.rmtree(temp_dir, ignore_errors=True)


def test_security_features():
    """测试安全功能"""
    print("\n\n🛡️ 测试安全功能")
    print("-" * 40)
    
    from license.crypto_utils import CryptoUtils, SecureStorage, generate_hardware_id
    
    crypto = CryptoUtils()
    
    # 测试硬件指纹
    hardware_id = generate_hardware_id()
    print(f"🔧 硬件指纹: {hardware_id}")
    
    # 测试加密功能
    print(f"\n🔐 加密功能测试:")
    test_data = "这是敏感的许可证数据"
    master_key = crypto.generate_master_key("test_password")
    
    encrypted = crypto.encrypt_data(test_data, master_key)
    decrypted = crypto.decrypt_data(encrypted, master_key)
    
    encryption_success = decrypted == test_data
    print(f"   加密/解密: {'✅ 成功' if encryption_success else '❌ 失败'}")
    
    # 测试安全存储
    print(f"\n💾 安全存储测试:")
    temp_file = tempfile.mktemp()
    
    try:
        storage = SecureStorage(temp_file, "test_password")
        
        test_config = {
            "license_type": "professional",
            "user_name": "测试用户",
            "activation_code": "TEST-CODE-1234",
            "expire_date": "2024-12-31"
        }
        
        save_success = storage.save_data(test_config)
        loaded_data = storage.load_data()
        storage_success = loaded_data == test_config
        
        print(f"   保存/加载: {'✅ 成功' if storage_success else '❌ 失败'}")
        
        return encryption_success and storage_success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    
    finally:
        # 清理临时文件
        try:
            os.remove(temp_file)
            os.remove(temp_file + ".key")
        except:
            pass


def test_license_server():
    """测试许可证服务器"""
    print("\n\n🖥️ 测试许可证服务器")
    print("-" * 40)
    
    from license.license_server import LicenseServer, LicenseServerAPI
    
    # 创建临时数据库
    temp_db = tempfile.mktemp(suffix='.db')
    
    try:
        server = LicenseServer(temp_db)
        api = LicenseServerAPI(server)
        
        # 测试许可证注册
        print(f"📝 测试许可证注册:")
        test_license = {
            'activation_code': 'TEST-0003-01Z0-FBQU-FBQM-9PBE-X005-9A',
            'license_type': 'professional',
            'user_name': '测试用户',
            'email': '<EMAIL>',
            'company_name': '测试公司',
            'expire_date': (datetime.now() + timedelta(days=365)).isoformat()
        }
        
        success, message = server.register_license(test_license)
        print(f"   注册结果: {message}")
        
        if success:
            # 测试许可证验证
            print(f"\n🔍 测试许可证验证:")
            result = server.validate_license(
                test_license['activation_code'],
                'TEST-HARDWARE-ID'
            )
            
            validation_success = result['valid']
            print(f"   验证结果: {'✅ 成功' if validation_success else '❌ 失败'}")
            
            if validation_success:
                license_info = result['license_info']
                print(f"   许可证ID: {license_info['license_id']}")
                print(f"   用户: {license_info['user_name']}")
            
            # 测试统计信息
            print(f"\n📊 测试统计信息:")
            stats = server.get_license_statistics()
            print(f"   总许可证数: {stats.get('total_licenses', 0)}")
            print(f"   活跃许可证: {stats.get('active_licenses', 0)}")
            
            return validation_success
        
        return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    
    finally:
        # 清理临时文件
        try:
            os.remove(temp_db)
        except:
            pass


def test_online_validation():
    """测试在线验证"""
    print("\n\n🌐 测试在线验证")
    print("-" * 40)
    
    from license.online_validator import MockLicenseServer, HybridLicenseValidator
    
    # 创建模拟服务器
    mock_server = MockLicenseServer()
    
    # 测试在线验证
    test_code = '0003-01Z0-FBQU-FBQM-9PBE-X005-9A'
    test_hardware_id = 'TEST-HARDWARE-12345'
    
    print(f"📝 测试激活码: {test_code}")
    
    result = mock_server.validate(test_code, test_hardware_id)
    
    if result['success']:
        print(f"   ✅ 在线验证成功")
        license_info = result['data']['license_info']
        print(f"   许可证类型: {license_info['license_type']}")
        print(f"   用户: {license_info['user_name']}")
        return True
    else:
        print(f"   ❌ 在线验证失败: {result['message']}")
        return False


def run_complete_test():
    """运行完整测试"""
    print("🚀 资管工具平台 - 完整系统测试")
    print("=" * 60)
    print("测试开始时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print()
    
    test_results = {}
    
    try:
        # 运行所有测试
        test_results['activation'] = test_activation_system()
        test_results['license_manager'] = test_license_manager()
        test_results['update_system'] = test_update_system()
        test_results['security'] = test_security_features()
        test_results['license_server'] = test_license_server()
        test_results['online_validation'] = test_online_validation()
        
        # 统计结果
        print("\n" + "=" * 60)
        print("📊 测试结果汇总")
        print("=" * 60)
        
        passed_tests = 0
        total_tests = len(test_results)
        
        for test_name, result in test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_name:<20}: {status}")
            if result:
                passed_tests += 1
        
        print(f"\n总体结果: {passed_tests}/{total_tests} 测试通过")
        
        if passed_tests == total_tests:
            print("🎉 所有测试通过！系统功能完整。")
        else:
            print("⚠️ 部分测试失败，请检查相关功能。")
        
        # 功能总结
        print("\n📋 系统功能总结:")
        print("   🎫 激活码系统 - 生成、验证、格式化")
        print("   🔐 许可证管理 - 类型控制、权限管理、状态监控")
        print("   🔄 自动更新 - 版本检查、下载管理、安装升级")
        print("   🛡️ 安全保护 - 加密存储、硬件绑定、数据完整性")
        print("   🖥️ 服务器端 - 许可证注册、验证、统计管理")
        print("   🌐 在线验证 - 实时验证、远程管理、防盗版")
        
        print("\n🎯 部署建议:")
        print("   1. 生产环境部署许可证服务器")
        print("   2. 配置HTTPS和SSL证书")
        print("   3. 设置数据库备份策略")
        print("   4. 监控系统运行状态")
        print("   5. 定期更新安全策略")
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生严重错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    run_complete_test()
