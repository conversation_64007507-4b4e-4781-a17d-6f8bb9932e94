#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
先锋文件处理器
处理先锋版本文件的识别、分类和整合逻辑
"""

import os
import re
import shutil
from pathlib import Path
from typing import Dict, List, Tuple, Optional


class PioneerProcessor:
    """先锋文件处理器"""
    
    def __init__(self):
        self.pioneer_pattern = r'\(先锋\)'
        
    def extract_version_from_dirname(self, dirname: str) -> Tuple[str, str]:
        """
        从目录名中提取版本信息
        
        Args:
            dirname: 目录名
            
        Returns:
            (prefix, version): 前缀部分和版本部分
            
        Examples:
            "20250801【手机魔域】[任务包][常规脚本]年中主题18赐福活动制作下架v1.0(龚杰翔)"
            -> ("20250801【手机魔域】[任务包][常规脚本]年中主题18赐福活动制作下架", "v1.0(龚杰翔)")
        """
        # 查找版本号模式 v数字.数字
        version_match = re.search(r'(v\d+\.\d+.*)', dirname)
        if version_match:
            version_start = version_match.start()
            prefix = dirname[:version_start]
            version = dirname[version_start:]
            return prefix, version
        else:
            # 如果没有版本号，在最后添加
            return dirname, ""
    
    def create_tagged_dirname(self, original_dirname: str, tag: str) -> str:
        """
        创建带标签的目录名
        
        Args:
            original_dirname: 原始目录名
            tag: 标签，如"[官方]"或"[先锋]"
            
        Returns:
            带标签的目录名
        """
        prefix, version = self.extract_version_from_dirname(original_dirname)
        if version:
            return f"{prefix}{tag}{version}"
        else:
            return f"{original_dirname}{tag}"
    
    def analyze_files(self, work_dir: str) -> Dict[str, Dict]:
        """
        分析工作目录中的文件，识别先锋和官方版本
        
        Args:
            work_dir: 工作目录路径
            
        Returns:
            文件分析结果字典
            {
                "common_files": [文件列表],  # 通用文件（没有先锋版本）
                "paired_files": {           # 成对文件
                    "base_name": {
                        "official": "官方文件路径",
                        "pioneer": "先锋文件路径"
                    }
                },
                "orphan_pioneer": [文件列表]  # 孤立的先锋文件
            }
        """
        result = {
            "common_files": [],
            "paired_files": {},
            "orphan_pioneer": []
        }
        
        if not os.path.exists(work_dir):
            return result
        
        # 扫描所有文件夹和文件（包括+/-文件夹内部的文件）
        all_items = []
        other_items = []  # 不以+/-开头的普通文件和文件夹

        for item in os.listdir(work_dir):
            item_path = os.path.join(work_dir, item)
            if item.startswith(('+', '-')):
                if os.path.isfile(item_path):
                    # +/-顶层文件
                    all_items.append(item)
                elif os.path.isdir(item_path):
                    # +/-文件夹 - 需要检查内部文件
                    all_items.append(item)
                    # 扫描文件夹内部的所有文件
                    try:
                        for sub_item in os.listdir(item_path):
                            sub_item_path = os.path.join(item_path, sub_item)
                            if os.path.isfile(sub_item_path):
                                # 使用相对路径表示文件夹内的文件
                                relative_path = f"{item}/{sub_item}"
                                all_items.append(relative_path)
                    except (PermissionError, OSError):
                        # 如果无法访问文件夹，跳过
                        pass
            else:
                # 普通文件和文件夹（不以+/-开头）
                other_items.append(item)
        
        # 分类处理
        pioneer_items = {}  # {base_name: pioneer_item}
        official_items = {}  # {base_name: official_item}
        
        for item in all_items:
            if re.search(self.pioneer_pattern, item):
                # 先锋版本
                base_name = re.sub(self.pioneer_pattern, '', item)
                pioneer_items[base_name] = item
            else:
                # 可能是官方版本
                official_items[item] = item
        
        # 匹配成对文件
        for base_name, pioneer_item in pioneer_items.items():
            if base_name in official_items:
                # 找到对应的官方版本
                result["paired_files"][base_name] = {
                    "official": official_items[base_name],
                    "pioneer": pioneer_item
                }
                # 从官方列表中移除已配对的
                del official_items[base_name]
            else:
                # 孤立的先锋文件
                result["orphan_pioneer"].append(pioneer_item)
        
        # 剩余的官方文件作为通用文件
        result["common_files"] = list(official_items.values())

        # 添加普通文件和文件夹（不以+/-开头的）
        result["common_files"].extend(other_items)

        return result
    
    def create_official_work_dir(self, source_dir: str, target_dir: str,
                                file_analysis: Dict) -> bool:
        """
        创建官方工作目录

        Args:
            source_dir: 源目录
            target_dir: 目标目录
            file_analysis: 文件分析结果

        Returns:
            是否成功
        """
        try:
            # 创建目标目录
            os.makedirs(target_dir, exist_ok=True)

            # 获取所有需要处理的文件夹
            folders_with_pioneer = set()
            for base_name, files in file_analysis["paired_files"].items():
                if "/" in files["official"]:
                    folder_name = files["official"].split("/")[0]
                    folders_with_pioneer.add(folder_name)

            # 复制通用文件
            for item in file_analysis["common_files"]:
                source_path = os.path.join(source_dir, item)
                target_path = os.path.join(target_dir, item)
                if "/" in item:
                    # 文件夹内的文件，需要确保目标文件夹存在
                    os.makedirs(os.path.dirname(target_path), exist_ok=True)
                    shutil.copy2(source_path, target_path)
                elif os.path.isdir(source_path):
                    # 检查这个文件夹是否包含先锋文件
                    if item in folders_with_pioneer:
                        # 包含先锋文件的文件夹，只创建空文件夹，不复制内容
                        os.makedirs(target_path, exist_ok=True)
                    else:
                        # 不包含先锋文件的文件夹，直接复制整个文件夹
                        shutil.copytree(source_path, target_path, dirs_exist_ok=True)
                else:
                    shutil.copy2(source_path, target_path)

            # 复制官方版本的成对文件（只复制官方版本，不复制先锋版本）
            for base_name, files in file_analysis["paired_files"].items():
                official_file = files["official"]
                source_path = os.path.join(source_dir, official_file)
                target_path = os.path.join(target_dir, official_file)  # 保持原名
                if "/" in official_file:
                    # 文件夹内的文件，需要确保目标文件夹存在
                    os.makedirs(os.path.dirname(target_path), exist_ok=True)
                    shutil.copy2(source_path, target_path)
                elif os.path.isdir(source_path):
                    shutil.copytree(source_path, target_path, dirs_exist_ok=True)
                else:
                    shutil.copy2(source_path, target_path)

            return True

        except Exception as e:
            print(f"创建官方工作目录失败: {e}")
            return False
    
    def create_pioneer_work_dir(self, source_dir: str, target_dir: str,
                               file_analysis: Dict) -> bool:
        """
        创建先锋工作目录

        Args:
            source_dir: 源目录
            target_dir: 目标目录
            file_analysis: 文件分析结果

        Returns:
            是否成功
        """
        try:
            # 创建目标目录
            os.makedirs(target_dir, exist_ok=True)

            # 获取所有需要处理的文件夹
            folders_with_pioneer = set()
            for base_name, files in file_analysis["paired_files"].items():
                if "/" in files["pioneer"]:
                    folder_name = files["pioneer"].split("/")[0]
                    folders_with_pioneer.add(folder_name)

            # 复制通用文件
            for item in file_analysis["common_files"]:
                source_path = os.path.join(source_dir, item)
                target_path = os.path.join(target_dir, item)
                if "/" in item:
                    # 文件夹内的文件，需要确保目标文件夹存在
                    os.makedirs(os.path.dirname(target_path), exist_ok=True)
                    shutil.copy2(source_path, target_path)
                elif os.path.isdir(source_path):
                    # 检查这个文件夹是否包含先锋文件
                    if item in folders_with_pioneer:
                        # 包含先锋文件的文件夹，只创建空文件夹，不复制内容
                        os.makedirs(target_path, exist_ok=True)
                    else:
                        # 不包含先锋文件的文件夹，直接复制整个文件夹
                        shutil.copytree(source_path, target_path, dirs_exist_ok=True)
                else:
                    shutil.copy2(source_path, target_path)

            # 处理成对文件：使用先锋版本，重命名为基础名
            for base_name, files in file_analysis["paired_files"].items():
                pioneer_file = files["pioneer"]
                source_path = os.path.join(source_dir, pioneer_file)
                target_path = os.path.join(target_dir, base_name)  # 重命名去掉(先锋)
                if "/" in pioneer_file:
                    # 文件夹内的文件，需要确保目标文件夹存在
                    os.makedirs(os.path.dirname(target_path), exist_ok=True)
                    shutil.copy2(source_path, target_path)
                elif os.path.isdir(source_path):
                    shutil.copytree(source_path, target_path, dirs_exist_ok=True)
                else:
                    shutil.copy2(source_path, target_path)

            return True

        except Exception as e:
            print(f"创建先锋工作目录失败: {e}")
            return False
    
    def process_dual_mode(self, work_dir: str, official_complete_dir: str = None, 
                         pioneer_complete_dir: str = None) -> Tuple[Optional[str], Optional[str]]:
        """
        处理双模式整合
        
        Args:
            work_dir: 原始工作目录
            official_complete_dir: 官方完整目录（可选）
            pioneer_complete_dir: 先锋完整目录（可选）
            
        Returns:
            (official_work_dir, pioneer_work_dir): 创建的工作目录路径
        """
        if not os.path.exists(work_dir):
            raise ValueError(f"工作目录不存在: {work_dir}")
        
        # 分析文件
        file_analysis = self.analyze_files(work_dir)
        
        # 获取目录名
        original_dirname = os.path.basename(work_dir)
        parent_dir = os.path.dirname(work_dir)
        
        official_work_dir = None
        pioneer_work_dir = None
        
        # 创建官方工作目录
        if official_complete_dir:
            official_dirname = self.create_tagged_dirname(original_dirname, "[官方]")
            official_work_dir = os.path.join(parent_dir, official_dirname)
            
            if not self.create_official_work_dir(work_dir, official_work_dir, file_analysis):
                official_work_dir = None
        
        # 创建先锋工作目录
        if pioneer_complete_dir:
            pioneer_dirname = self.create_tagged_dirname(original_dirname, "[先锋]")
            pioneer_work_dir = os.path.join(parent_dir, pioneer_dirname)
            
            if not self.create_pioneer_work_dir(work_dir, pioneer_work_dir, file_analysis):
                pioneer_work_dir = None
        
        return official_work_dir, pioneer_work_dir
