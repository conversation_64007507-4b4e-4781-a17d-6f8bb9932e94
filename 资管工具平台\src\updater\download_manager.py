#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
下载管理器
"""

import os
import hashlib
import tempfile
import shutil
from pathlib import Path
from typing import Optional, Callable
from PyQt6.QtCore import QObject, pyqtSignal, QThread
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry


class DownloadWorker(QThread):
    """下载工作线程"""
    
    # 信号定义
    progress_updated = pyqtSignal(int, int, int)  # 已下载, 总大小, 速度(bytes/s)
    download_finished = pyqtSignal(bool, str)    # 成功, 文件路径或错误信息
    hash_verified = pyqtSignal(bool, str)        # 验证成功, 哈希值或错误信息
    
    def __init__(self, download_url: str, save_path: str, expected_hash: str = None):
        super().__init__()
        self.download_url = download_url
        self.save_path = save_path
        self.expected_hash = expected_hash
        self.is_cancelled = False
        self.chunk_size = 8192  # 8KB chunks
        
    def run(self):
        """执行下载"""
        try:
            # 创建保存目录
            os.makedirs(os.path.dirname(self.save_path), exist_ok=True)
            
            # 配置请求会话
            session = self._create_session()
            
            # 开始下载
            success = self._download_file(session)
            
            if success and not self.is_cancelled:
                # 验证文件哈希
                if self.expected_hash:
                    self._verify_file_hash()
                else:
                    self.download_finished.emit(True, self.save_path)
            elif self.is_cancelled:
                self._cleanup_partial_file()
                self.download_finished.emit(False, "下载已取消")
            
        except Exception as e:
            self._cleanup_partial_file()
            self.download_finished.emit(False, f"下载失败: {str(e)}")
    
    def cancel(self):
        """取消下载"""
        self.is_cancelled = True
    
    def _create_session(self) -> requests.Session:
        """创建HTTP会话"""
        session = requests.Session()
        
        # 配置重试策略
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # 设置请求头
        session.headers.update({
            'User-Agent': 'ResourceManagementTool-Updater/1.0',
            'Accept': '*/*',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive'
        })
        
        return session
    
    def _download_file(self, session: requests.Session) -> bool:
        """下载文件"""
        try:
            # 发送HEAD请求获取文件大小
            head_response = session.head(self.download_url, timeout=30)
            total_size = int(head_response.headers.get('content-length', 0))
            
            # 开始下载
            response = session.get(self.download_url, stream=True, timeout=30)
            response.raise_for_status()
            
            downloaded_size = 0
            start_time = self.msecsSinceEpoch()
            
            with open(self.save_path, 'wb') as file:
                for chunk in response.iter_content(chunk_size=self.chunk_size):
                    if self.is_cancelled:
                        return False
                    
                    if chunk:
                        file.write(chunk)
                        downloaded_size += len(chunk)
                        
                        # 计算下载速度
                        elapsed_time = (self.msecsSinceEpoch() - start_time) / 1000.0
                        speed = int(downloaded_size / elapsed_time) if elapsed_time > 0 else 0
                        
                        # 发送进度信号
                        self.progress_updated.emit(downloaded_size, total_size, speed)
            
            return True
            
        except requests.RequestException as e:
            raise Exception(f"网络请求失败: {str(e)}")
        except IOError as e:
            raise Exception(f"文件写入失败: {str(e)}")
    
    def _verify_file_hash(self):
        """验证文件哈希"""
        try:
            # 计算文件哈希
            calculated_hash = self._calculate_file_hash(self.save_path)
            
            # 比较哈希值
            expected_hash_clean = self.expected_hash.replace('sha256:', '').lower()
            calculated_hash_clean = calculated_hash.lower()
            
            if expected_hash_clean == calculated_hash_clean:
                self.hash_verified.emit(True, calculated_hash)
                self.download_finished.emit(True, self.save_path)
            else:
                self._cleanup_partial_file()
                self.hash_verified.emit(False, f"哈希验证失败: 期望 {expected_hash_clean}, 实际 {calculated_hash_clean}")
                self.download_finished.emit(False, "文件完整性验证失败")
                
        except Exception as e:
            self._cleanup_partial_file()
            self.hash_verified.emit(False, f"哈希计算失败: {str(e)}")
            self.download_finished.emit(False, f"文件验证失败: {str(e)}")
    
    def _calculate_file_hash(self, file_path: str) -> str:
        """计算文件SHA256哈希"""
        sha256_hash = hashlib.sha256()
        
        with open(file_path, 'rb') as file:
            for chunk in iter(lambda: file.read(4096), b""):
                if self.is_cancelled:
                    raise Exception("哈希计算被取消")
                sha256_hash.update(chunk)
        
        return sha256_hash.hexdigest()
    
    def _cleanup_partial_file(self):
        """清理部分下载的文件"""
        try:
            if os.path.exists(self.save_path):
                os.remove(self.save_path)
        except Exception:
            pass  # 忽略清理失败
    
    def msecsSinceEpoch(self) -> int:
        """获取当前时间戳（毫秒）"""
        from datetime import datetime
        return int(datetime.now().timestamp() * 1000)


class DownloadManager(QObject):
    """下载管理器"""
    
    # 信号定义
    download_started = pyqtSignal(str)           # 下载开始
    download_progress = pyqtSignal(int, int, int) # 进度更新
    download_completed = pyqtSignal(str)         # 下载完成
    download_failed = pyqtSignal(str)            # 下载失败
    hash_verification_result = pyqtSignal(bool, str)  # 哈希验证结果
    
    def __init__(self, download_dir: str = None):
        super().__init__()
        
        # 设置下载目录
        if download_dir is None:
            download_dir = os.path.join(tempfile.gettempdir(), "ResourceManagementTool", "downloads")
        
        self.download_dir = Path(download_dir)
        self.download_dir.mkdir(parents=True, exist_ok=True)
        
        self.current_worker = None
        self.is_downloading = False
    
    def download_update(self, download_url: str, filename: str = None, 
                       expected_hash: str = None) -> bool:
        """下载更新文件"""
        if self.is_downloading:
            return False
        
        try:
            # 生成文件名
            if filename is None:
                filename = self._generate_filename_from_url(download_url)
            
            save_path = str(self.download_dir / filename)
            
            # 如果文件已存在且哈希匹配，直接返回
            if os.path.exists(save_path) and expected_hash:
                if self._verify_existing_file(save_path, expected_hash):
                    self.download_completed.emit(save_path)
                    return True
            
            # 创建下载工作线程
            self.current_worker = DownloadWorker(download_url, save_path, expected_hash)
            
            # 连接信号
            self.current_worker.progress_updated.connect(self.download_progress.emit)
            self.current_worker.download_finished.connect(self._on_download_finished)
            self.current_worker.hash_verified.connect(self.hash_verification_result.emit)
            
            # 开始下载
            self.is_downloading = True
            self.download_started.emit(download_url)
            self.current_worker.start()
            
            return True
            
        except Exception as e:
            self.download_failed.emit(f"启动下载失败: {str(e)}")
            return False
    
    def cancel_download(self):
        """取消当前下载"""
        if self.current_worker and self.is_downloading:
            self.current_worker.cancel()
    
    def is_download_in_progress(self) -> bool:
        """检查是否正在下载"""
        return self.is_downloading
    
    def get_download_dir(self) -> str:
        """获取下载目录"""
        return str(self.download_dir)
    
    def cleanup_old_downloads(self, keep_days: int = 7):
        """清理旧的下载文件"""
        try:
            import time
            current_time = time.time()
            cutoff_time = current_time - (keep_days * 24 * 60 * 60)
            
            for file_path in self.download_dir.glob("*"):
                if file_path.is_file():
                    file_mtime = file_path.stat().st_mtime
                    if file_mtime < cutoff_time:
                        file_path.unlink()
                        
        except Exception as e:
            print(f"清理下载文件失败: {e}")
    
    def _generate_filename_from_url(self, url: str) -> str:
        """从URL生成文件名"""
        from urllib.parse import urlparse
        parsed_url = urlparse(url)
        filename = os.path.basename(parsed_url.path)
        
        if not filename or '.' not in filename:
            filename = f"update_{int(self.msecsSinceEpoch())}.zip"
        
        return filename
    
    def _verify_existing_file(self, file_path: str, expected_hash: str) -> bool:
        """验证已存在文件的哈希"""
        try:
            worker = DownloadWorker("", file_path, expected_hash)
            calculated_hash = worker._calculate_file_hash(file_path)
            
            expected_hash_clean = expected_hash.replace('sha256:', '').lower()
            return calculated_hash.lower() == expected_hash_clean
            
        except Exception:
            return False
    
    def _on_download_finished(self, success: bool, result: str):
        """下载完成处理"""
        self.is_downloading = False
        
        if success:
            self.download_completed.emit(result)
        else:
            self.download_failed.emit(result)
        
        # 清理工作线程
        if self.current_worker:
            self.current_worker.deleteLater()
            self.current_worker = None
    
    def msecsSinceEpoch(self) -> int:
        """获取当前时间戳（毫秒）"""
        from datetime import datetime
        return int(datetime.now().timestamp() * 1000)


def format_speed(bytes_per_second: int) -> str:
    """格式化下载速度"""
    if bytes_per_second < 1024:
        return f"{bytes_per_second} B/s"
    elif bytes_per_second < 1024 * 1024:
        return f"{bytes_per_second / 1024:.1f} KB/s"
    else:
        return f"{bytes_per_second / (1024 * 1024):.1f} MB/s"


def format_time_remaining(bytes_remaining: int, bytes_per_second: int) -> str:
    """格式化剩余时间"""
    if bytes_per_second <= 0:
        return "计算中..."
    
    seconds_remaining = bytes_remaining / bytes_per_second
    
    if seconds_remaining < 60:
        return f"{int(seconds_remaining)} 秒"
    elif seconds_remaining < 3600:
        minutes = int(seconds_remaining / 60)
        return f"{minutes} 分钟"
    else:
        hours = int(seconds_remaining / 3600)
        minutes = int((seconds_remaining % 3600) / 60)
        return f"{hours} 小时 {minutes} 分钟"
