#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SVN载包和上传界面组件
"""

import os
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
                            QLabel, QLineEdit, QPushButton, QTextEdit,
                            QProgressBar, QCheckBox, QFileDialog, QMessageBox,
                            QFrame, QSplitter, QGridLayout, QTabWidget,
                            QTableWidget, QTableWidgetItem, QHeaderView,
                            QAbstractItemView, QMenu)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer, QUrl
from PyQt6.QtGui import QFont, QDragEnterEvent, QDropEvent, QAction

from modules.svn_uploader import SvnUploader, SvnUploaderWorker, SvnManager, CompressManager
from utils.config_manager import ConfigManager


class SvnUploaderWidget(QWidget):
    """SVN载包和上传界面组件"""
    
    status_changed = pyqtSignal(str)  # 状态变更信号
    
    def __init__(self):
        super().__init__()
        self.uploader = SvnUploader()
        self.config_manager = ConfigManager()
        self.worker = None
        self.init_ui()
        self.setup_connections()
        self.load_cached_paths()
        self.setup_drag_drop()
        
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # SVN检出选项卡
        checkout_tab = self.create_checkout_tab()
        self.tab_widget.addTab(checkout_tab, "SVN检出")
        
        # 上传选项卡
        upload_tab = self.create_upload_tab()
        self.tab_widget.addTab(upload_tab, "文件上传")

        # 历史记录选项卡
        history_tab = self.create_history_tab()
        self.tab_widget.addTab(history_tab, "上传历史")
        
    def create_checkout_tab(self):
        """创建SVN检出选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Vertical)
        layout.addWidget(splitter)
        
        # 上半部分：配置面板
        config_widget = self.create_checkout_config_panel()
        splitter.addWidget(config_widget)
        
        # 下半部分：日志显示
        log_widget = self.create_log_panel("SVN检出日志")
        splitter.addWidget(log_widget)
        
        # 设置分割器比例
        splitter.setSizes([250, 350])
        
        return widget
        
    def create_checkout_config_panel(self):
        """创建SVN检出配置面板"""
        widget = QFrame()
        widget.setFrameStyle(QFrame.Shape.StyledPanel)
        layout = QVBoxLayout(widget)
        
        # SVN配置组
        svn_group = QGroupBox("SVN配置")
        svn_layout = QGridLayout(svn_group)
        
        # SVN地址
        svn_layout.addWidget(QLabel("SVN地址:"), 0, 0)
        self.svn_url_entry = QLineEdit()
        self.svn_url_entry.setPlaceholderText("输入SVN仓库地址...")
        svn_layout.addWidget(self.svn_url_entry, 0, 1)
        
        # 检出目录
        svn_layout.addWidget(QLabel("检出目录:"), 1, 0)
        self.checkout_dir_entry = QLineEdit()
        self.checkout_dir_entry.setPlaceholderText("选择检出目录...")
        svn_layout.addWidget(self.checkout_dir_entry, 1, 1)
        self.checkout_browse_btn = QPushButton("浏览")
        self.checkout_browse_btn.setMinimumWidth(80)
        svn_layout.addWidget(self.checkout_browse_btn, 1, 2)
        
        layout.addWidget(svn_group)
        
        # 选项组
        options_group = QGroupBox("选项")
        options_layout = QVBoxLayout(options_group)
        
        self.auto_process_cb = QCheckBox("自动处理检出的文件")
        self.auto_process_cb.setChecked(True)
        self.auto_process_cb.setToolTip("自动创建文件夹结构并整理文件")
        options_layout.addWidget(self.auto_process_cb)
        
        self.save_path_cb = QCheckBox("记住检出目录")
        self.save_path_cb.setChecked(True)
        options_layout.addWidget(self.save_path_cb)
        
        layout.addWidget(options_group)
        
        # 状态信息
        status_group = QGroupBox("状态信息")
        status_layout = QVBoxLayout(status_group)
        
        # SVN可用性检查
        svn_available = SvnManager.is_svn_available()
        svn_status = "✓ SVN客户端可用" if svn_available else "✗ SVN客户端不可用"
        svn_status_label = QLabel(svn_status)
        svn_status_label.setStyleSheet(
            "color: green;" if svn_available else "color: red;"
        )
        status_layout.addWidget(svn_status_label)
        
        layout.addWidget(status_group)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.checkout_btn = QPushButton("开始检出")
        self.checkout_btn.setMinimumHeight(35)
        self.checkout_btn.setEnabled(svn_available)
        button_layout.addWidget(self.checkout_btn)
        
        self.clear_checkout_log_btn = QPushButton("清空日志")
        self.clear_checkout_log_btn.setMinimumHeight(35)
        button_layout.addWidget(self.clear_checkout_log_btn)
        
        layout.addLayout(button_layout)
        
        # 进度条
        self.checkout_progress_bar = QProgressBar()
        self.checkout_progress_bar.setVisible(False)
        layout.addWidget(self.checkout_progress_bar)
        
        return widget
        
    def create_upload_tab(self):
        """创建上传选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Vertical)
        layout.addWidget(splitter)
        
        # 上半部分：配置面板
        config_widget = self.create_upload_config_panel()
        splitter.addWidget(config_widget)
        
        # 下半部分：日志显示
        log_widget = self.create_log_panel("上传日志")
        splitter.addWidget(log_widget)
        
        # 设置分割器比例
        splitter.setSizes([200, 400])
        
        return widget

    def create_history_tab(self):
        """创建历史记录选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)

        # 工具栏
        toolbar_layout = QHBoxLayout()

        self.refresh_history_btn = QPushButton("🔄 刷新")
        self.refresh_history_btn.clicked.connect(self.refresh_history)
        toolbar_layout.addWidget(self.refresh_history_btn)

        self.clear_history_btn = QPushButton("🗑️ 清空历史")
        self.clear_history_btn.clicked.connect(self.clear_history)
        toolbar_layout.addWidget(self.clear_history_btn)

        self.undo_upload_btn = QPushButton("↩️ 撤销上传")
        self.undo_upload_btn.clicked.connect(self.undo_last_upload)
        self.undo_upload_btn.setStyleSheet("QPushButton { background-color: #ff9500; color: white; }")
        toolbar_layout.addWidget(self.undo_upload_btn)

        toolbar_layout.addStretch()

        # 状态标签
        self.history_status_label = QLabel("历史记录")
        self.history_status_label.setFont(QFont("Microsoft YaHei UI", 9))
        toolbar_layout.addWidget(self.history_status_label)

        layout.addLayout(toolbar_layout)

        # 历史记录表格
        self.history_table = QTableWidget()
        self.history_table.setColumnCount(7)
        self.history_table.setHorizontalHeaderLabels([
            "ID", "源路径", "状态", "开始时间", "文件大小", "压缩大小", "操作"
        ])

        # 设置表格属性
        self.history_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.history_table.setAlternatingRowColors(True)
        self.history_table.horizontalHeader().setStretchLastSection(True)
        self.history_table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.history_table.customContextMenuRequested.connect(self.show_history_context_menu)

        # 设置列宽
        header = self.history_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)  # ID
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # 源路径
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)  # 状态
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)  # 时间
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Fixed)  # 文件大小
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Fixed)  # 压缩大小
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.Fixed)  # 操作

        self.history_table.setColumnWidth(0, 80)   # ID
        self.history_table.setColumnWidth(2, 80)   # 状态
        self.history_table.setColumnWidth(3, 150)  # 时间
        self.history_table.setColumnWidth(4, 100)  # 文件大小
        self.history_table.setColumnWidth(5, 100)  # 压缩大小
        self.history_table.setColumnWidth(6, 120)  # 操作

        layout.addWidget(self.history_table)

        return widget

    def create_upload_config_panel(self):
        """创建上传配置面板"""
        widget = QFrame()
        widget.setFrameStyle(QFrame.Shape.StyledPanel)
        layout = QVBoxLayout(widget)
        
        # 文件选择组
        file_group = QGroupBox("文件选择")
        file_layout = QGridLayout(file_group)
        
        # 文件夹路径
        file_layout.addWidget(QLabel("文件夹路径:"), 0, 0)
        self.upload_folder_entry = QLineEdit()
        self.upload_folder_entry.setPlaceholderText("选择要上传的文件夹...")
        file_layout.addWidget(self.upload_folder_entry, 0, 1)
        self.upload_browse_btn = QPushButton("浏览")
        self.upload_browse_btn.setMinimumWidth(80)
        file_layout.addWidget(self.upload_browse_btn, 0, 2)
        
        layout.addWidget(file_group)
        
        # 结果显示组
        result_group = QGroupBox("上传结果")
        result_layout = QGridLayout(result_group)
        
        result_layout.addWidget(QLabel("压缩包路径:"), 0, 0)
        self.result_path_entry = QLineEdit()
        self.result_path_entry.setReadOnly(True)
        result_layout.addWidget(self.result_path_entry, 0, 1)
        self.copy_path_btn = QPushButton("复制")
        self.copy_path_btn.setMinimumWidth(80)
        self.copy_path_btn.setEnabled(False)
        result_layout.addWidget(self.copy_path_btn, 0, 2)
        
        layout.addWidget(result_group)
        
        # 状态信息
        status_group = QGroupBox("状态信息")
        status_layout = QVBoxLayout(status_group)
        
        # 7z可用性检查
        compress_available = CompressManager.is_7z_available()
        compress_status = "✓ 7z压缩可用" if compress_available else "✗ 7z压缩不可用"
        compress_status_label = QLabel(compress_status)
        compress_status_label.setStyleSheet(
            "color: green;" if compress_available else "color: red;"
        )
        status_layout.addWidget(compress_status_label)
        
        if not compress_available:
            install_hint = QLabel("请安装: pip install py7zr")
            install_hint.setStyleSheet("color: orange; font-style: italic;")
            status_layout.addWidget(install_hint)
            
        layout.addWidget(status_group)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.upload_btn = QPushButton("压缩并上传")
        self.upload_btn.setMinimumHeight(35)
        self.upload_btn.setEnabled(compress_available)
        button_layout.addWidget(self.upload_btn)

        self.cancel_upload_btn = QPushButton("取消上传")
        self.cancel_upload_btn.setMinimumHeight(35)
        self.cancel_upload_btn.setEnabled(False)
        self.cancel_upload_btn.setStyleSheet("QPushButton { background-color: #ff6b6b; color: white; }")
        button_layout.addWidget(self.cancel_upload_btn)

        self.clear_upload_log_btn = QPushButton("清空日志")
        self.clear_upload_log_btn.setMinimumHeight(35)
        button_layout.addWidget(self.clear_upload_log_btn)
        
        layout.addLayout(button_layout)
        
        # 进度条
        self.upload_progress_bar = QProgressBar()
        self.upload_progress_bar.setVisible(False)
        layout.addWidget(self.upload_progress_bar)
        
        return widget
        
    def create_log_panel(self, title):
        """创建日志显示面板"""
        widget = QFrame()
        widget.setFrameStyle(QFrame.Shape.StyledPanel)
        layout = QVBoxLayout(widget)
        
        # 日志标题
        log_label = QLabel(title)
        log_label.setFont(QFont("Microsoft YaHei UI", 10, QFont.Weight.Bold))
        layout.addWidget(log_label)
        
        # 日志文本区域
        if title == "SVN检出日志":
            self.checkout_log_text = QTextEdit()
            self.checkout_log_text.setFont(QFont("Consolas", 9))
            self.checkout_log_text.setReadOnly(True)
            layout.addWidget(self.checkout_log_text)
        else:
            self.upload_log_text = QTextEdit()
            self.upload_log_text.setFont(QFont("Consolas", 9))
            self.upload_log_text.setReadOnly(True)
            layout.addWidget(self.upload_log_text)
            
        return widget
        
    def setup_connections(self):
        """设置信号连接"""
        # 浏览按钮
        self.checkout_browse_btn.clicked.connect(
            lambda: self.browse_directory(self.checkout_dir_entry, "选择检出目录"))
        self.upload_browse_btn.clicked.connect(
            lambda: self.browse_directory(self.upload_folder_entry, "选择上传文件夹"))
            
        # 操作按钮
        self.checkout_btn.clicked.connect(self.start_checkout)
        self.upload_btn.clicked.connect(self.start_upload)
        self.cancel_upload_btn.clicked.connect(self.cancel_upload)

        # 清空日志按钮
        self.clear_checkout_log_btn.clicked.connect(self.clear_checkout_log)
        self.clear_upload_log_btn.clicked.connect(self.clear_upload_log)
        
        # 复制路径按钮
        self.copy_path_btn.clicked.connect(self.copy_result_path)
        
        # 路径变更
        self.checkout_dir_entry.textChanged.connect(self.on_checkout_path_changed)
        self.svn_url_entry.textChanged.connect(self.on_checkout_path_changed)  # SVN URL变化时也要检查
        self.upload_folder_entry.textChanged.connect(self.on_upload_path_changed)
        
        # 连接上传器信号
        self.uploader.log_message.connect(self.add_current_log_message)
        self.uploader.processing_finished.connect(self.on_processing_finished)

        # 初始化历史记录
        QTimer.singleShot(100, self.refresh_history)
        
    def browse_directory(self, entry_widget, title):
        """浏览目录"""
        directory = QFileDialog.getExistingDirectory(
            self, title, entry_widget.text())
        if directory:
            entry_widget.setText(directory)
            
    def on_checkout_path_changed(self):
        """检出路径变更时的处理"""
        has_path = bool(self.checkout_dir_entry.text().strip())
        has_url = bool(self.svn_url_entry.text().strip())
        self.checkout_btn.setEnabled(has_path and has_url and SvnManager.is_svn_available())
        
    def on_upload_path_changed(self):
        """上传路径变更时的处理"""
        has_path = bool(self.upload_folder_entry.text().strip())
        self.upload_btn.setEnabled(has_path and CompressManager.is_7z_available())
        
    def load_cached_paths(self):
        """加载缓存的路径"""
        last_checkout_dir = self.config_manager.get_config("paths.last_checkout_dir", "")
        if last_checkout_dir and os.path.exists(last_checkout_dir):
            self.checkout_dir_entry.setText(last_checkout_dir)
            
    def save_cached_paths(self):
        """保存缓存的路径"""
        if self.save_path_cb.isChecked():
            checkout_dir = self.checkout_dir_entry.text().strip()
            if checkout_dir:
                self.config_manager.set_config("paths.last_checkout_dir", checkout_dir)
                self.config_manager.save_user_config()
                
    def start_checkout(self):
        """开始SVN检出"""
        svn_url = self.svn_url_entry.text().strip()
        checkout_dir = self.checkout_dir_entry.text().strip()
        
        if not svn_url or not checkout_dir:
            QMessageBox.warning(self, "警告", "请填写SVN地址和检出目录")
            return
            
        # 保存路径缓存
        self.save_cached_paths()
        
        # 禁用按钮
        self.set_checkout_buttons_enabled(False)
        self.checkout_progress_bar.setVisible(True)
        self.checkout_progress_bar.setRange(0, 100)
        self.checkout_progress_bar.setValue(0)
        
        # 创建工作线程
        self.worker = SvnUploaderWorker("checkout", svn_url=svn_url, checkout_dir=checkout_dir)
        self.worker.progress_updated.connect(self.checkout_progress_bar.setValue)
        self.worker.log_message.connect(self.add_checkout_log_message)
        self.worker.processing_finished.connect(self.on_checkout_finished)
        self.worker.start()
        
        self.status_changed.emit("正在执行SVN检出...")
        
    def start_upload(self):
        """开始上传"""
        folder_path = self.upload_folder_entry.text().strip()

        if not folder_path:
            QMessageBox.warning(self, "警告", "请选择要上传的文件夹")
            return

        if not os.path.exists(folder_path):
            QMessageBox.warning(self, "警告", "文件夹不存在")
            return

        # 更新按钮状态
        self.set_upload_buttons_enabled(False)
        self.cancel_upload_btn.setEnabled(True)
        self.upload_progress_bar.setVisible(True)
        self.upload_progress_bar.setRange(0, 100)  # 确定进度

        # 创建工作线程
        self.worker = SvnUploaderWorker("upload", folder_path=folder_path)
        self.worker.log_message.connect(self.add_upload_log_message)
        self.worker.progress_updated.connect(self.upload_progress_bar.setValue)
        self.worker.processing_finished.connect(self.on_upload_finished)
        self.worker.start()

        self.status_changed.emit("正在压缩并上传文件...")

    def cancel_upload(self):
        """取消上传"""
        reply = QMessageBox.question(
            self, "确认", "确定要取消当前上传吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            if self.worker:
                self.worker.cancel()
            self.uploader.cancel_current_upload()
            self.set_upload_buttons_enabled(True)
            self.cancel_upload_btn.setEnabled(False)
            self.upload_progress_bar.setVisible(False)
            self.status_changed.emit("上传已取消")
        
    def on_checkout_finished(self, success, message):
        """SVN检出完成回调"""
        self.checkout_progress_bar.setVisible(False)
        self.set_checkout_buttons_enabled(True)
        
        if success:
            self.status_changed.emit("SVN检出完成")
            QMessageBox.information(self, "完成", "SVN检出完成")
        else:
            self.status_changed.emit("SVN检出失败")
            QMessageBox.critical(self, "错误", message)
            
        self._cleanup_worker()
        
    def on_upload_finished(self, success, result):
        """上传完成回调"""
        self.upload_progress_bar.setVisible(False)
        self.set_upload_buttons_enabled(True)
        self.cancel_upload_btn.setEnabled(False)

        if success:
            self.result_path_entry.setText(result)
            self.copy_path_btn.setEnabled(True)
            self.status_changed.emit("上传完成")

            # 延迟刷新历史记录，确保数据已保存
            QTimer.singleShot(500, self.refresh_history)

            QMessageBox.information(self, "完成", "文件上传完成")
        else:
            self.status_changed.emit("上传失败")
            QMessageBox.critical(self, "错误", result)

            # 失败时也刷新历史记录
            QTimer.singleShot(500, self.refresh_history)

        self._cleanup_worker()
        
    def on_processing_finished(self, success, message):
        """通用处理完成回调"""
        # 这个方法用于处理来自uploader的信号
        pass
        
    def _cleanup_worker(self):
        """清理工作线程"""
        if self.worker:
            self.worker.deleteLater()
            self.worker = None

    def is_operation_running(self):
        """检查是否有操作正在进行"""
        return self.worker is not None and self.worker.isRunning()

    def get_current_operation(self):
        """获取当前操作类型"""
        if self.is_operation_running():
            return getattr(self.worker, 'operation', '未知操作')
        return None
            
    def set_checkout_buttons_enabled(self, enabled):
        """设置检出按钮启用状态"""
        self.checkout_btn.setEnabled(enabled)
        self.checkout_browse_btn.setEnabled(enabled)
        
    def set_upload_buttons_enabled(self, enabled):
        """设置上传按钮启用状态"""
        self.upload_btn.setEnabled(enabled)
        self.upload_browse_btn.setEnabled(enabled)
        
    def add_current_log_message(self, message):
        """添加日志消息到当前选项卡"""
        current_index = self.tab_widget.currentIndex()
        if current_index == 0:  # SVN检出选项卡
            self.add_checkout_log_message(message)
        else:  # 上传选项卡
            self.add_upload_log_message(message)
            
    def add_checkout_log_message(self, message):
        """添加检出日志消息"""
        self.checkout_log_text.append(message)
        cursor = self.checkout_log_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.checkout_log_text.setTextCursor(cursor)
        
    def add_upload_log_message(self, message):
        """添加上传日志消息"""
        self.upload_log_text.append(message)
        cursor = self.upload_log_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.upload_log_text.setTextCursor(cursor)
        
    def clear_checkout_log(self):
        """清空检出日志"""
        self.checkout_log_text.clear()
        
    def clear_upload_log(self):
        """清空上传日志"""
        self.upload_log_text.clear()

    def refresh_history(self):
        """刷新历史记录"""
        try:
            # 强制重新加载历史数据
            self.uploader.history_manager.history_data = self.uploader.history_manager._load_history()

            history = self.uploader.get_upload_history(50)
            self.update_history_table(history)
            self.history_status_label.setText(f"共 {len(history)} 条记录")

            # 添加调试信息
            print(f"[DEBUG] 刷新历史记录: {len(history)} 条")
            for i, record in enumerate(history[:3]):
                print(f"[DEBUG] 记录 {i+1}: {record.get('source_path', 'N/A')} - {record.get('status', 'N/A')}")

        except Exception as e:
            print(f"[DEBUG] 刷新历史记录失败: {e}")
            QMessageBox.warning(self, "错误", f"刷新历史记录失败: {e}")

    def update_history_table(self, history):
        """更新历史记录表格"""
        self.history_table.setRowCount(len(history))

        for row, record in enumerate(history):
            # ID
            self.history_table.setItem(row, 0, QTableWidgetItem(record.get("id", "")))

            # 源路径
            source_path = record.get("source_path", "")
            source_item = QTableWidgetItem(os.path.basename(source_path))
            source_item.setToolTip(source_path)
            self.history_table.setItem(row, 1, source_item)

            # 状态
            status = record.get("status", "")
            status_text = {
                "uploading": "上传中",
                "completed": "已完成",
                "failed": "失败",
                "cancelled": "已取消",
                "undone": "已撤销"
            }.get(status, status)

            status_item = QTableWidgetItem(status_text)
            if status == "completed":
                status_item.setBackground(Qt.GlobalColor.green)
            elif status == "failed":
                status_item.setBackground(Qt.GlobalColor.red)
            elif status == "cancelled":
                status_item.setBackground(Qt.GlobalColor.yellow)
            elif status == "undone":
                status_item.setBackground(Qt.GlobalColor.gray)

            self.history_table.setItem(row, 2, status_item)

            # 开始时间
            start_time = record.get("start_time", "")
            if start_time:
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                    time_str = dt.strftime("%m-%d %H:%M")
                except:
                    time_str = start_time[:16]
            else:
                time_str = ""
            self.history_table.setItem(row, 3, QTableWidgetItem(time_str))

            # 文件大小
            file_size = record.get("file_size", 0)
            try:
                if file_size is not None and isinstance(file_size, (int, float)) and file_size > 0:
                    size_str = f"{file_size / (1024*1024):.1f} MB"
                else:
                    size_str = ""
            except (TypeError, ValueError):
                size_str = ""
            self.history_table.setItem(row, 4, QTableWidgetItem(size_str))

            # 压缩大小
            compressed_size = record.get("compressed_size", 0)
            try:
                if compressed_size is not None and isinstance(compressed_size, (int, float)) and compressed_size > 0:
                    compressed_str = f"{compressed_size / (1024*1024):.1f} MB"
                else:
                    compressed_str = ""
            except (TypeError, ValueError):
                compressed_str = ""
            self.history_table.setItem(row, 5, QTableWidgetItem(compressed_str))

            # 操作按钮
            action_widget = QWidget()
            action_layout = QHBoxLayout(action_widget)
            action_layout.setContentsMargins(2, 2, 2, 2)

            if status == "failed":
                retry_btn = QPushButton("重试")
                retry_btn.setMaximumWidth(50)
                retry_btn.clicked.connect(lambda checked, rid=record["id"]: self.retry_upload(rid))
                action_layout.addWidget(retry_btn)

            delete_btn = QPushButton("删除")
            delete_btn.setMaximumWidth(50)
            delete_btn.clicked.connect(lambda checked, rid=record["id"]: self.delete_record(rid))
            action_layout.addWidget(delete_btn)

            self.history_table.setCellWidget(row, 6, action_widget)

    def show_history_context_menu(self, position):
        """显示历史记录右键菜单"""
        item = self.history_table.itemAt(position)
        if item is None:
            return

        row = item.row()
        record_id = self.history_table.item(row, 0).text()
        status = self.history_table.item(row, 2).text()

        menu = QMenu(self)

        # 撤销上传选项（仅对已完成的记录）
        if status == "已完成":
            undo_action = QAction("撤销此上传", self)
            undo_action.triggered.connect(lambda: self.undo_specific_upload(record_id))
            menu.addAction(undo_action)
            menu.addSeparator()

        refresh_action = QAction("刷新", self)
        refresh_action.triggered.connect(self.refresh_history)
        menu.addAction(refresh_action)

        menu.addSeparator()

        clear_action = QAction("清空历史", self)
        clear_action.triggered.connect(self.clear_history)
        menu.addAction(clear_action)

        menu.exec(self.history_table.mapToGlobal(position))

    def clear_history(self):
        """清空历史记录"""
        reply = QMessageBox.question(
            self, "确认", "确定要清空所有历史记录吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.uploader.clear_upload_history()
            self.refresh_history()

    def retry_upload(self, record_id):
        """重试上传"""
        try:
            success = self.uploader.retry_upload(record_id)
            if success:
                QMessageBox.information(self, "提示", "重试上传已开始")
                self.refresh_history()
            else:
                QMessageBox.warning(self, "错误", "重试上传失败")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"重试上传失败: {e}")

    def delete_record(self, record_id):
        """删除记录"""
        reply = QMessageBox.question(
            self, "确认", "确定要删除这条记录吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            success = self.uploader.delete_upload_record(record_id)
            if success:
                self.refresh_history()
            else:
                QMessageBox.warning(self, "错误", "删除记录失败")

    def undo_last_upload(self):
        """撤销最后一次上传"""
        try:
            # 获取撤销信息
            success, result = self.uploader.undo_last_upload()

            if not success:
                QMessageBox.warning(self, "无法撤销", result)
                return

            # 解析结果
            if isinstance(result, dict):
                record_id = result["record_id"]
                source_path = result["source_path"]
                files_to_delete = result["files_to_delete"]
                search_info = result["search_info"]

                # 显示撤销确认对话框
                self.show_undo_confirmation_dialog(
                    record_id, source_path, files_to_delete, search_info
                )
            else:
                QMessageBox.warning(self, "错误", "撤销信息格式错误")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"撤销上传失败: {e}")

    def show_undo_confirmation_dialog(self, record_id, source_path, files_to_delete, search_info):
        """显示撤销确认对话框"""
        from PyQt6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QListWidget, QDialogButtonBox

        dialog = QDialog(self)
        dialog.setWindowTitle("确认撤销上传")
        dialog.setMinimumSize(600, 400)

        layout = QVBoxLayout(dialog)

        # 标题
        title_label = QLabel(f"确认撤销上传: {os.path.basename(source_path)}")
        title_label.setFont(QFont("Microsoft YaHei UI", 12, QFont.Weight.Bold))
        layout.addWidget(title_label)

        # 搜索信息
        info_label = QLabel("搜索信息:")
        info_label.setFont(QFont("Microsoft YaHei UI", 9, QFont.Weight.Bold))
        layout.addWidget(info_label)

        search_text = QTextEdit()
        search_text.setPlainText(search_info)
        search_text.setMaximumHeight(100)
        search_text.setReadOnly(True)
        layout.addWidget(search_text)

        # 文件列表
        files_label = QLabel(f"将要删除的文件 ({len(files_to_delete)} 个):")
        files_label.setFont(QFont("Microsoft YaHei UI", 9, QFont.Weight.Bold))
        layout.addWidget(files_label)

        files_list = QListWidget()
        for file_path in files_to_delete:
            # 获取文件信息
            file_info = self.uploader.undo_upload_manager.get_file_info(file_path)

            if file_info["exists"]:
                size_mb = file_info["size"] / (1024 * 1024)
                item_text = f"{file_info['name']} ({size_mb:.1f} MB)"
                files_list.addItem(item_text)
            else:
                item_text = f"{file_info['name']} (文件不存在)"
                files_list.addItem(item_text)

        layout.addWidget(files_list)

        # 警告信息
        warning_label = QLabel("⚠️ 警告: 此操作将永久删除上述文件，无法恢复！")
        warning_label.setStyleSheet("color: red; font-weight: bold;")
        layout.addWidget(warning_label)

        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.button(QDialogButtonBox.StandardButton.Ok).setText("确认删除")
        button_box.button(QDialogButtonBox.StandardButton.Cancel).setText("取消")

        button_box.accepted.connect(lambda: self.execute_undo_upload(dialog, record_id, files_to_delete))
        button_box.rejected.connect(dialog.reject)

        layout.addWidget(button_box)

        dialog.exec()

    def execute_undo_upload(self, dialog, record_id, files_to_delete):
        """执行撤销上传"""
        try:
            # 执行删除
            success, message = self.uploader.confirm_undo_upload(files_to_delete, record_id)

            dialog.accept()

            if success:
                QMessageBox.information(self, "撤销成功", message)
                # 刷新历史记录
                self.refresh_history()
            else:
                QMessageBox.warning(self, "撤销失败", message)

        except Exception as e:
            dialog.accept()
            QMessageBox.critical(self, "错误", f"执行撤销时发生错误: {e}")

    def undo_specific_upload(self, record_id):
        """撤销指定的上传记录"""
        try:
            # 获取记录信息
            record = self.uploader.history_manager.get_record_by_id(record_id)
            if not record:
                QMessageBox.warning(self, "错误", "找不到指定的上传记录")
                return

            if record.get("status") != "completed":
                QMessageBox.warning(self, "错误", "只能撤销已完成的上传")
                return

            source_path = record.get("source_path", "")
            if not source_path:
                QMessageBox.warning(self, "错误", "记录中缺少源路径信息")
                return

            # 获取源文件夹名称
            source_folder_name = os.path.basename(source_path)

            # 获取上传目录
            upload_dirs = self.uploader.upload_manager.get_upload_directories()

            # 查找要删除的文件
            files_to_delete, search_info = self.uploader.undo_upload_manager.confirm_undo_upload(
                source_folder_name, upload_dirs
            )

            if not files_to_delete:
                QMessageBox.warning(self, "无法撤销", f"没有找到要撤销的文件\n\n搜索信息:\n{search_info}")
                return

            # 显示撤销确认对话框
            self.show_undo_confirmation_dialog(
                record_id, source_path, files_to_delete, search_info
            )

        except Exception as e:
            QMessageBox.critical(self, "错误", f"撤销指定上传失败: {e}")

    def copy_result_path(self):
        """复制结果路径到剪贴板"""
        result_path = self.result_path_entry.text()
        if result_path:
            from PyQt6.QtWidgets import QApplication
            QApplication.clipboard().setText(result_path)
            QMessageBox.information(self, "信息", "路径已复制到剪贴板")

    def setup_drag_drop(self):
        """设置拖拽支持"""
        self.setAcceptDrops(True)

    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            # 检查是否包含文件夹
            urls = event.mimeData().urls()
            for url in urls:
                if url.isLocalFile():
                    path = url.toLocalFile()
                    if os.path.isdir(path):
                        event.acceptProposedAction()
                        return
        event.ignore()

    def dropEvent(self, event: QDropEvent):
        """拖拽放下事件"""
        if event.mimeData().hasUrls():
            urls = event.mimeData().urls()
            for url in urls:
                if url.isLocalFile():
                    path = url.toLocalFile()
                    if os.path.isdir(path):
                        # 设置检出目录
                        self.checkout_dir_entry.setText(path)
                        self.status_changed.emit(f"已设置检出目录: {path}")
                        event.acceptProposedAction()
                        return
        event.ignore()
