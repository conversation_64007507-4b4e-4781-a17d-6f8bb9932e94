#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
客户端激活码验证器
"""

import requests
import json
import hashlib
import platform
import uuid
import os
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Tuple
import logging

logger = logging.getLogger(__name__)

class LicenseValidator:
    """激活码验证器"""
    
    def __init__(self, server_url: str, api_key: str = None):
        """
        初始化验证器
        
        Args:
            server_url: 服务器地址
            api_key: API密钥
        """
        self.server_url = server_url.rstrip('/')
        self.api_key = api_key
        self.machine_id = self._get_machine_id()
        self.license_file = os.path.join(os.path.expanduser('~'), '.resource_management_license')
        
    def _get_machine_id(self) -> str:
        """获取机器唯一标识"""
        try:
            # 获取机器信息
            machine_info = {
                'platform': platform.platform(),
                'processor': platform.processor(),
                'machine': platform.machine(),
                'node': platform.node(),
            }
            
            # 尝试获取MAC地址
            try:
                mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                               for elements in range(0, 2*6, 2)][::-1])
                machine_info['mac'] = mac
            except:
                pass
            
            # 生成机器ID
            machine_str = json.dumps(machine_info, sort_keys=True)
            machine_id = hashlib.sha256(machine_str.encode()).hexdigest()[:16]
            
            return machine_id.upper()
            
        except Exception as e:
            logger.error(f"获取机器ID失败: {e}")
            # 使用随机UUID作为备选
            return str(uuid.uuid4()).replace('-', '')[:16].upper()
    
    def validate_activation_code(self, activation_code: str) -> Tuple[bool, Dict[str, Any]]:
        """
        验证激活码
        
        Args:
            activation_code: 激活码
            
        Returns:
            (是否成功, 结果信息)
        """
        try:
            # 准备请求数据
            data = {
                'activation_code': activation_code,
                'machine_id': self.machine_id,
                'machine_info': self._get_machine_info()
            }
            
            headers = {
                'Content-Type': 'application/json'
            }
            
            if self.api_key:
                headers['X-API-Key'] = self.api_key
            
            # 发送验证请求
            response = requests.post(
                f"{self.server_url}/api/license/validate",
                json=data,
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    # 保存许可证信息
                    self._save_license_info(activation_code, result.get('license_info', {}))
                    return True, result
                else:
                    return False, result
            else:
                return False, {
                    'error': f'服务器错误: {response.status_code}',
                    'message': '无法连接到许可证服务器'
                }
                
        except requests.exceptions.RequestException as e:
            logger.error(f"网络请求失败: {e}")
            return False, {
                'error': 'network_error',
                'message': f'网络连接失败: {str(e)}'
            }
        except Exception as e:
            logger.error(f"验证激活码失败: {e}")
            return False, {
                'error': 'validation_error',
                'message': f'验证过程出错: {str(e)}'
            }
    
    def check_license_status(self) -> Tuple[bool, Dict[str, Any]]:
        """
        检查当前许可证状态
        
        Returns:
            (是否有效, 许可证信息)
        """
        try:
            # 读取本地许可证信息
            license_info = self._load_license_info()
            if not license_info:
                return False, {'error': 'no_license', 'message': '未找到许可证信息'}
            
            activation_code = license_info.get('activation_code')
            if not activation_code:
                return False, {'error': 'invalid_license', 'message': '许可证信息无效'}
            
            # 检查本地过期时间
            expires_at = license_info.get('expires_at')
            if expires_at:
                try:
                    expire_time = datetime.fromisoformat(expires_at.replace('Z', '+00:00'))
                    if datetime.now() > expire_time:
                        return False, {'error': 'expired', 'message': '许可证已过期'}
                except:
                    pass
            
            # 向服务器验证状态
            data = {
                'activation_code': activation_code,
                'machine_id': self.machine_id
            }
            
            headers = {
                'Content-Type': 'application/json'
            }
            
            if self.api_key:
                headers['X-API-Key'] = self.api_key
            
            response = requests.post(
                f"{self.server_url}/api/license/status",
                json=data,
                headers=headers,
                timeout=15
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    # 更新本地许可证信息
                    updated_info = result.get('license_info', {})
                    license_info.update(updated_info)
                    self._save_license_info(activation_code, license_info)
                    return True, result
                else:
                    return False, result
            else:
                # 服务器无法访问时，使用本地缓存的信息
                logger.warning(f"无法连接服务器，使用本地许可证信息")
                return True, {
                    'success': True,
                    'message': '使用本地许可证信息（离线模式）',
                    'license_info': license_info,
                    'offline_mode': True
                }
                
        except requests.exceptions.RequestException as e:
            # 网络错误时使用本地缓存
            logger.warning(f"网络连接失败，使用本地许可证: {e}")
            license_info = self._load_license_info()
            if license_info:
                return True, {
                    'success': True,
                    'message': '网络连接失败，使用本地许可证信息',
                    'license_info': license_info,
                    'offline_mode': True
                }
            else:
                return False, {'error': 'no_license', 'message': '无许可证且无法连接服务器'}
        except Exception as e:
            logger.error(f"检查许可证状态失败: {e}")
            return False, {'error': 'check_error', 'message': f'检查过程出错: {str(e)}'}
    
    def deactivate_license(self) -> Tuple[bool, Dict[str, Any]]:
        """
        停用当前许可证
        
        Returns:
            (是否成功, 结果信息)
        """
        try:
            license_info = self._load_license_info()
            if not license_info:
                return False, {'error': 'no_license', 'message': '未找到许可证信息'}
            
            activation_code = license_info.get('activation_code')
            
            data = {
                'activation_code': activation_code,
                'machine_id': self.machine_id
            }
            
            headers = {
                'Content-Type': 'application/json'
            }
            
            if self.api_key:
                headers['X-API-Key'] = self.api_key
            
            response = requests.post(
                f"{self.server_url}/api/license/deactivate",
                json=data,
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    # 删除本地许可证文件
                    self._remove_license_info()
                    return True, result
                else:
                    return False, result
            else:
                return False, {'error': 'server_error', 'message': f'服务器错误: {response.status_code}'}
                
        except Exception as e:
            logger.error(f"停用许可证失败: {e}")
            return False, {'error': 'deactivate_error', 'message': f'停用过程出错: {str(e)}'}
    
    def _get_machine_info(self) -> Dict[str, Any]:
        """获取机器信息"""
        return {
            'platform': platform.platform(),
            'system': platform.system(),
            'release': platform.release(),
            'version': platform.version(),
            'machine': platform.machine(),
            'processor': platform.processor(),
            'node': platform.node(),
            'python_version': platform.python_version()
        }
    
    def _save_license_info(self, activation_code: str, license_info: Dict[str, Any]):
        """保存许可证信息到本地"""
        try:
            data = {
                'activation_code': activation_code,
                'machine_id': self.machine_id,
                'saved_at': datetime.now().isoformat(),
                **license_info
            }
            
            with open(self.license_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"保存许可证信息失败: {e}")
    
    def _load_license_info(self) -> Optional[Dict[str, Any]]:
        """从本地加载许可证信息"""
        try:
            if os.path.exists(self.license_file):
                with open(self.license_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"加载许可证信息失败: {e}")
        return None
    
    def _remove_license_info(self):
        """删除本地许可证信息"""
        try:
            if os.path.exists(self.license_file):
                os.remove(self.license_file)
        except Exception as e:
            logger.error(f"删除许可证信息失败: {e}")
    
    def get_local_license_info(self) -> Optional[Dict[str, Any]]:
        """获取本地许可证信息"""
        return self._load_license_info()
