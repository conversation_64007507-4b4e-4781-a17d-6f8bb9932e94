#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
整包工具界面组件
"""

import os
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
                            QLabel, QLineEdit, QPushButton, QTextEdit,
                            QProgressBar, QCheckBox, QFileDialog, QMessageBox,
                            QComboBox, QTabWidget, QFrame, QSplitter,
                            QGridLayout, QSpacerItem, QSizePolicy)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer, QUrl
from PyQt6.QtGui import QFont, QDragEnterEvent, QDropEvent, QColor

from modules.pioneer_processor import PioneerProcessor

from modules.package_merger_original import PackageMergerWorker
from utils.config_manager import ConfigManager


class PackageMergerWidget(QWidget):
    """整包工具界面组件"""
    
    status_changed = pyqtSignal(str)  # 状态变更信号
    
    def __init__(self):
        super().__init__()
        self.worker = None
        self.config_manager = ConfigManager()
        self.init_ui()
        self.setup_connections()
        self.setup_drag_drop()
        self.load_settings()
        
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 创建主分割器
        splitter = QSplitter(Qt.Orientation.Vertical)
        layout.addWidget(splitter)
        
        # 上半部分：配置面板
        config_widget = self.create_config_panel()
        splitter.addWidget(config_widget)
        
        # 下半部分：日志显示
        log_widget = self.create_log_panel()
        splitter.addWidget(log_widget)
        
        # 设置分割器比例
        splitter.setSizes([400, 300])
        
    def create_config_panel(self):
        """创建配置面板"""
        widget = QFrame()
        widget.setFrameStyle(QFrame.Shape.StyledPanel)
        layout = QVBoxLayout(widget)
        
        # 路径配置组 - 简化为两个主要路径
        paths_group = QGroupBox("路径配置")
        paths_layout = QGridLayout(paths_group)

        # 工作目录 (game_root)
        paths_layout.addWidget(QLabel("工作目录:"), 0, 0)
        self.game_root_entry = QLineEdit()
        self.game_root_entry.setPlaceholderText("选择工作目录（包含+ini、+ani等文件夹）...")
        paths_layout.addWidget(self.game_root_entry, 0, 1)
        self.game_root_browse_btn = QPushButton("浏览")
        self.game_root_browse_btn.setMinimumWidth(80)
        paths_layout.addWidget(self.game_root_browse_btn, 0, 2)


        
        layout.addWidget(paths_group)

        # 整合模式组
        mode_group = QGroupBox("整合模式")
        mode_layout = QVBoxLayout(mode_group)

        # 官方整合
        official_layout = QHBoxLayout()
        self.official_cb = QCheckBox("官方整合")
        self.official_cb.setChecked(True)  # 默认勾选
        official_layout.addWidget(self.official_cb)
        official_layout.addStretch()
        mode_layout.addLayout(official_layout)

        official_dir_layout = QGridLayout()
        official_dir_layout.addWidget(QLabel("官方完整目录:"), 0, 0)
        self.official_complete_dir_entry = QLineEdit()
        self.official_complete_dir_entry.setPlaceholderText("选择官方完整目录...")
        official_dir_layout.addWidget(self.official_complete_dir_entry, 0, 1)
        self.official_complete_dir_btn = QPushButton("浏览")
        self.official_complete_dir_btn.setMinimumWidth(80)
        official_dir_layout.addWidget(self.official_complete_dir_btn, 0, 2)
        mode_layout.addLayout(official_dir_layout)

        # 先锋整合
        pioneer_layout = QHBoxLayout()
        self.pioneer_cb = QCheckBox("先锋整合")
        pioneer_layout.addWidget(self.pioneer_cb)
        pioneer_layout.addStretch()
        mode_layout.addLayout(pioneer_layout)

        pioneer_dir_layout = QGridLayout()
        pioneer_dir_layout.addWidget(QLabel("先锋完整目录:"), 0, 0)
        self.pioneer_complete_dir_entry = QLineEdit()
        self.pioneer_complete_dir_entry.setPlaceholderText("选择先锋完整目录...")
        pioneer_dir_layout.addWidget(self.pioneer_complete_dir_entry, 0, 1)
        self.pioneer_complete_dir_btn = QPushButton("浏览")
        self.pioneer_complete_dir_btn.setMinimumWidth(80)
        pioneer_dir_layout.addWidget(self.pioneer_complete_dir_btn, 0, 2)
        mode_layout.addLayout(pioneer_dir_layout)

        layout.addWidget(mode_group)

        # 处理选项组 - 简化选项
        options_group = QGroupBox("处理选项")
        options_layout = QVBoxLayout(options_group)

        # 处理说明
        info_label = QLabel("直接调用原整包工具，自动处理所有+文件夹和-文件夹")
        info_label.setStyleSheet("color: #666666; font-style: italic;")
        options_layout.addWidget(info_label)
        
        layout.addWidget(options_group)
        
        # 操作按钮组
        button_layout = QHBoxLayout()

        self.validate_btn = QPushButton("验证配置")
        self.validate_btn.setMinimumHeight(35)
        button_layout.addWidget(self.validate_btn)

        self.update_clean_btn = QPushButton("更新清理")
        self.update_clean_btn.setMinimumHeight(35)
        self.update_clean_btn.setToolTip("对勾选的完整目录执行SVN清理和更新")
        button_layout.addWidget(self.update_clean_btn)

        self.merge_btn = QPushButton("开始整包")
        self.merge_btn.setMinimumHeight(35)
        self.merge_btn.setEnabled(False)
        button_layout.addWidget(self.merge_btn)

        self.cancel_btn = QPushButton("取消操作")
        self.cancel_btn.setMinimumHeight(35)
        self.cancel_btn.setEnabled(False)
        self.cancel_btn.setStyleSheet("QPushButton { background-color: #ff6b6b; color: white; }")
        button_layout.addWidget(self.cancel_btn)

        self.clear_btn = QPushButton("清空日志")
        self.clear_btn.setMinimumHeight(35)
        button_layout.addWidget(self.clear_btn)
        
        layout.addLayout(button_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        return widget
        
    def create_log_panel(self):
        """创建日志显示面板"""
        widget = QFrame()
        widget.setFrameStyle(QFrame.Shape.StyledPanel)
        layout = QVBoxLayout(widget)
        
        # 日志标题
        log_label = QLabel("处理日志")
        log_label.setFont(QFont("Microsoft YaHei UI", 10, QFont.Weight.Bold))
        layout.addWidget(log_label)
        
        # 日志文本区域
        self.log_text = QTextEdit()
        self.log_text.setFont(QFont("Consolas", 9))
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        
        return widget
        
    def setup_connections(self):
        """设置信号连接"""
        self.game_root_browse_btn.clicked.connect(lambda: self.browse_directory(self.game_root_entry, "选择工作目录"))
        self.official_complete_dir_btn.clicked.connect(self.browse_official_complete_dir)
        self.pioneer_complete_dir_btn.clicked.connect(self.browse_pioneer_complete_dir)

        self.validate_btn.clicked.connect(self.validate_config)
        self.update_clean_btn.clicked.connect(self.start_update_clean)
        self.merge_btn.clicked.connect(self.start_merge)
        self.cancel_btn.clicked.connect(self.cancel_operation)
        self.clear_btn.clicked.connect(self.clear_log)

        # 注意：复选框和路径变更的信号连接在 load_settings() 中进行
        # 这样可以避免在加载设置时触发保存
        
    def browse_directory(self, entry_widget, title):
        """浏览目录"""
        directory = QFileDialog.getExistingDirectory(
            self, title, entry_widget.text())
        if directory:
            entry_widget.setText(directory)

    def browse_official_complete_dir(self):
        """浏览官方完整目录"""
        self.browse_directory(self.official_complete_dir_entry, "选择官方完整目录")

    def browse_pioneer_complete_dir(self):
        """浏览先锋完整目录"""
        self.browse_directory(self.pioneer_complete_dir_entry, "选择先锋完整目录")

    def on_mode_changed(self):
        """模式变更处理"""
        # 至少需要勾选一个模式
        if not self.official_cb.isChecked() and not self.pioneer_cb.isChecked():
            # 如果都没勾选，自动勾选官方
            self.official_cb.setChecked(True)

        # 启用/禁用对应的输入框
        self.official_complete_dir_entry.setEnabled(self.official_cb.isChecked())
        self.official_complete_dir_btn.setEnabled(self.official_cb.isChecked())
        self.pioneer_complete_dir_entry.setEnabled(self.pioneer_cb.isChecked())
        self.pioneer_complete_dir_btn.setEnabled(self.pioneer_cb.isChecked())

        # 重新验证配置
        self.on_config_changed()

    def on_config_changed(self):
        """配置变更时的处理"""
        self.merge_btn.setEnabled(False)
        # 延迟保存设置，避免频繁保存
        if hasattr(self, '_save_timer'):
            self._save_timer.stop()
        else:
            self._save_timer = QTimer()
            self._save_timer.setSingleShot(True)
            self._save_timer.timeout.connect(self.save_settings)
        self._save_timer.start(1000)  # 1秒后保存
        
    def validate_config(self):
        """验证配置"""
        game_root = self.game_root_entry.text().strip()
        official_complete_dir = self.official_complete_dir_entry.text().strip()
        pioneer_complete_dir = self.pioneer_complete_dir_entry.text().strip()

        errors = []

        # 检查必需的路径
        if not game_root:
            errors.append("请选择工作目录")
        elif not os.path.exists(game_root):
            errors.append("工作目录不存在")

        # 检查勾选的模式对应的完整目录
        if self.official_cb.isChecked():
            if not official_complete_dir:
                errors.append("请选择官方完整目录")
            elif not os.path.exists(official_complete_dir):
                errors.append("官方完整目录不存在")

        if self.pioneer_cb.isChecked():
            if not pioneer_complete_dir:
                errors.append("请选择先锋完整目录")
            elif not os.path.exists(pioneer_complete_dir):
                errors.append("先锋完整目录不存在")

        # 检查两个完整目录是否相同
        if (self.official_cb.isChecked() and self.pioneer_cb.isChecked() and
            official_complete_dir and pioneer_complete_dir and
            os.path.normpath(official_complete_dir) == os.path.normpath(pioneer_complete_dir)):

            reply = QMessageBox.question(
                self, "目录相同警告",
                "官方完整目录和先锋完整目录相同，这可能导致文件冲突。\n\n是否继续？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            if reply == QMessageBox.StandardButton.No:
                errors.append("请选择不同的完整目录")

        if errors:
            QMessageBox.warning(self, "配置错误", "\n".join(errors))
            self.status_changed.emit("配置验证失败")
            return False

        # 检查工作目录中是否包含任何+文件夹或-文件夹
        plus_folders = []
        minus_folders = []
        for item in os.listdir(game_root):
            if item.startswith('+') and os.path.isdir(os.path.join(game_root, item)):
                plus_folders.append(item)
            elif item.startswith('-') and os.path.isdir(os.path.join(game_root, item)):
                minus_folders.append(item)

        if not plus_folders and not minus_folders:
            QMessageBox.warning(self, "配置错误", "工作目录中未发现任何+文件夹（如+ini、+ani等）")
            self.status_changed.emit("配置验证失败")
            return False

        # 显示找到的文件夹
        if plus_folders:
            self.add_log_message(f"找到以下+文件夹: {', '.join(plus_folders)}")
        if minus_folders:
            self.add_log_message(f"找到以下-文件夹: {', '.join(minus_folders)}")

        self.add_log_message("配置验证通过")
        self.merge_btn.setEnabled(True)
        self.status_changed.emit("配置验证通过，可以开始合并")
        return True
        
    def start_merge(self):
        """开始合并"""
        if not self.validate_config():
            return

        # 清空日志
        self.log_text.clear()

        # 获取配置
        game_root = self.game_root_entry.text().strip()
        official_complete_dir = self.official_complete_dir_entry.text().strip() if self.official_cb.isChecked() else None
        pioneer_complete_dir = self.pioneer_complete_dir_entry.text().strip() if self.pioneer_cb.isChecked() else None

        # 显示简化的开始信息
        if official_complete_dir and pioneer_complete_dir:
            mode_text = "双模式（官方 + 先锋）"
        elif official_complete_dir:
            mode_text = "单官方模式"
        elif pioneer_complete_dir:
            mode_text = "单先锋模式"
        else:
            mode_text = "未知模式"

        self.add_log_message(f"� 开始整包处理 - {mode_text}")
        self.add_log_message("")

        # 禁用按钮
        self.set_buttons_enabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)

        # 创建双模式工作线程
        self.worker = DualModePackageMergerWorker(
            game_root,
            official_complete_dir,
            pioneer_complete_dir
        )
        self.worker.progress_updated.connect(self.progress_bar.setValue)
        self.worker.log_message.connect(self.add_log_message)
        self.worker.processing_finished.connect(self.on_processing_finished)
        self.worker.start()

        self.status_changed.emit("正在执行双模式整包处理...")
        
    def on_processing_finished(self, success, message):
        """处理完成回调"""
        self.progress_bar.setVisible(False)
        self.set_buttons_enabled(True)
        
        if success:
            self.status_changed.emit("整包处理完成")
            QMessageBox.information(self, "完成", "整包处理完成")
        else:
            self.status_changed.emit("整包处理失败")
            QMessageBox.critical(self, "错误", message)
            
        # 清理工作线程
        if self.worker:
            self.worker.deleteLater()
            self.worker = None
            
    def set_buttons_enabled(self, enabled):
        """设置按钮启用状态"""
        self.game_root_browse_btn.setEnabled(enabled)
        self.official_complete_dir_btn.setEnabled(enabled and self.official_cb.isChecked())
        self.pioneer_complete_dir_btn.setEnabled(enabled and self.pioneer_cb.isChecked())
        self.validate_btn.setEnabled(enabled)
        self.update_clean_btn.setEnabled(enabled)
        self.merge_btn.setEnabled(enabled)
        self.cancel_btn.setEnabled(not enabled)  # 取消按钮与其他按钮状态相反
        
    def add_log_message(self, message):
        """添加日志消息"""
        # 检查是否为成功消息（优先级最高，即使包含错误关键词也显示为绿色）
        success_indicators = ['✅', '🎉', '处理完成', '成功处理', '所有文件处理完成', '所有资源文件都已找到']
        is_success = any(indicator in message for indicator in success_indicators)

        if is_success:
            # 成功消息用绿色显示
            self.log_text.setTextColor(QColor(40, 167, 69))  # Bootstrap 绿色
            self.log_text.append(message)
            self.log_text.setTextColor(QColor(33, 37, 41))   # 恢复默认颜色
        else:
            # 检查是否为错误消息
            error_indicators = ['[错误]', '[警告]', '❌', '错误', 'error', '失败', 'failed', '异常', 'exception', '警告', 'warning', '不存在', 'not found']
            is_error = any(indicator in message.lower() for indicator in error_indicators)

            if is_error:
                # 错误消息用红色显示
                self.log_text.setTextColor(QColor(220, 53, 69))  # Bootstrap 红色
                self.log_text.append(message)
                self.log_text.setTextColor(QColor(33, 37, 41))   # 恢复默认颜色
            else:
                # 普通消息用默认颜色
                self.log_text.append(message)

        # 自动滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.log_text.setTextCursor(cursor)
        
    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
        self.status_changed.emit("日志已清空")

    def setup_drag_drop(self):
        """设置拖拽支持"""
        self.setAcceptDrops(True)

    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            # 检查是否包含文件夹
            urls = event.mimeData().urls()
            for url in urls:
                if url.isLocalFile():
                    path = url.toLocalFile()
                    if os.path.isdir(path):
                        event.acceptProposedAction()
                        return
        event.ignore()

    def dropEvent(self, event: QDropEvent):
        """拖拽放下事件"""
        if event.mimeData().hasUrls():
            urls = event.mimeData().urls()
            for url in urls:
                if url.isLocalFile():
                    path = url.toLocalFile()
                    if os.path.isdir(path):
                        # 根据拖拽位置决定设置哪个路径
                        pos = event.position().toPoint()
                        widget_height = self.height()

                        # 如果拖拽到上半部分，设置游戏根目录
                        if pos.y() < widget_height / 2:
                            self.game_root_entry.setText(path)
                            self.status_changed.emit(f"已设置游戏根目录: {path}")
                        else:
                            # 否则设置完整目录
                            self.complete_dir_entry.setText(path)
                            self.status_changed.emit(f"已设置完整目录: {path}")

                        event.acceptProposedAction()
                        return
        event.ignore()

    def load_settings(self):
        """加载设置"""
        try:
            # 临时断开信号连接，避免在加载时触发保存
            try:
                self.official_cb.toggled.disconnect()
                self.pioneer_cb.toggled.disconnect()
                self.game_root_entry.textChanged.disconnect()
                self.official_complete_dir_entry.textChanged.disconnect()
                self.pioneer_complete_dir_entry.textChanged.disconnect()
            except:
                pass  # 如果信号未连接，忽略错误

            # 加载上次使用的路径
            last_game_root = self.config_manager.get_config("paths.last_game_root", "")
            if last_game_root:
                if os.path.exists(last_game_root):
                    self.game_root_entry.setText(last_game_root)
                    print(f"整包工具 - 加载工作目录: {last_game_root}")
                else:
                    print(f"整包工具 - 工作目录不存在，已跳过: {last_game_root}")
                    # 尝试加载父目录
                    parent_dir = os.path.dirname(last_game_root)
                    if parent_dir and os.path.exists(parent_dir):
                        self.game_root_entry.setPlaceholderText(f"上次路径: {os.path.basename(last_game_root)} (已不存在)")

            # 加载官方完整目录
            last_official_complete_dir = self.config_manager.get_config("paths.last_official_complete_dir", "")
            if last_official_complete_dir:
                if os.path.exists(last_official_complete_dir):
                    self.official_complete_dir_entry.setText(last_official_complete_dir)
                    print(f"整包工具 - 加载官方完整目录: {last_official_complete_dir}")
                else:
                    print(f"整包工具 - 官方完整目录不存在，已跳过: {last_official_complete_dir}")
                    self.official_complete_dir_entry.setPlaceholderText(f"上次路径: {os.path.basename(last_official_complete_dir)} (已不存在)")

            # 加载先锋完整目录
            last_pioneer_complete_dir = self.config_manager.get_config("paths.last_pioneer_complete_dir", "")
            if last_pioneer_complete_dir:
                if os.path.exists(last_pioneer_complete_dir):
                    self.pioneer_complete_dir_entry.setText(last_pioneer_complete_dir)
                    print(f"整包工具 - 加载先锋完整目录: {last_pioneer_complete_dir}")
                else:
                    print(f"整包工具 - 先锋完整目录不存在，已跳过: {last_pioneer_complete_dir}")
                    self.pioneer_complete_dir_entry.setPlaceholderText(f"上次路径: {os.path.basename(last_pioneer_complete_dir)} (已不存在)")

            # 加载模式选择
            official_checked = self.config_manager.get_config("package_merger.official_mode", True)
            pioneer_checked = self.config_manager.get_config("package_merger.pioneer_mode", False)

            self.official_cb.setChecked(official_checked)
            self.pioneer_cb.setChecked(pioneer_checked)

            # 重新连接信号
            self.official_cb.toggled.connect(self.on_mode_changed)
            self.pioneer_cb.toggled.connect(self.on_mode_changed)
            self.game_root_entry.textChanged.connect(self.on_config_changed)
            self.official_complete_dir_entry.textChanged.connect(self.on_config_changed)
            self.pioneer_complete_dir_entry.textChanged.connect(self.on_config_changed)

            # 触发模式变更处理（但不会触发保存，因为这是程序化调用）
            self.on_mode_changed()

        except Exception as e:
            print(f"加载整包工具设置失败: {e}")

    def save_settings(self):
        """保存设置"""
        try:
            # 保存当前路径
            game_root = self.game_root_entry.text().strip()
            if game_root:
                self.config_manager.set_config("paths.last_game_root", game_root)

            official_complete_dir = self.official_complete_dir_entry.text().strip()
            if official_complete_dir:
                self.config_manager.set_config("paths.last_official_complete_dir", official_complete_dir)

            pioneer_complete_dir = self.pioneer_complete_dir_entry.text().strip()
            if pioneer_complete_dir:
                self.config_manager.set_config("paths.last_pioneer_complete_dir", pioneer_complete_dir)

            # 保存模式选择
            self.config_manager.set_config("package_merger.official_mode", self.official_cb.isChecked())
            self.config_manager.set_config("package_merger.pioneer_mode", self.pioneer_cb.isChecked())

            # 保存到文件
            self.config_manager.save_user_config()

        except Exception as e:
            print(f"保存整包工具设置失败: {e}")

    def start_update_clean(self):
        """开始SVN更新清理"""
        # 获取勾选的完整目录
        directories_to_update = []

        if self.official_cb.isChecked():
            official_dir = self.official_complete_dir_entry.text().strip()
            if official_dir:
                if os.path.exists(official_dir):
                    directories_to_update.append(("官方完整目录", official_dir))
                else:
                    QMessageBox.warning(self, "警告", f"官方完整目录不存在: {official_dir}")
                    return
            else:
                QMessageBox.warning(self, "警告", "请先设置官方完整目录")
                return

        if self.pioneer_cb.isChecked():
            pioneer_dir = self.pioneer_complete_dir_entry.text().strip()
            if pioneer_dir:
                if os.path.exists(pioneer_dir):
                    directories_to_update.append(("先锋完整目录", pioneer_dir))
                else:
                    QMessageBox.warning(self, "警告", f"先锋完整目录不存在: {pioneer_dir}")
                    return
            else:
                QMessageBox.warning(self, "警告", "请先设置先锋完整目录")
                return

        if not directories_to_update:
            QMessageBox.warning(self, "警告", "请至少勾选一个完整目录")
            return

        # 确认操作
        dir_names = [name for name, _ in directories_to_update]
        reply = QMessageBox.question(
            self, "确认操作",
            f"确定要对以下目录执行SVN清理和更新吗？\n\n" + "\n".join(dir_names),
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply != QMessageBox.StandardButton.Yes:
            return

        # 禁用按钮
        self.set_buttons_enabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)

        # 创建SVN更新清理工作线程
        self.svn_worker = SVNUpdateCleanWorker(directories_to_update)
        self.svn_worker.progress_updated.connect(self.progress_bar.setValue)
        self.svn_worker.log_message.connect(self.add_log_message)
        self.svn_worker.processing_finished.connect(self.on_svn_update_finished)
        self.svn_worker.start()

        self.status_changed.emit("正在执行SVN更新清理...")

    def on_svn_update_finished(self, success, message):
        """SVN更新清理完成回调"""
        self.progress_bar.setVisible(False)
        self.set_buttons_enabled(True)

        if success:
            self.status_changed.emit("SVN更新清理完成")
            QMessageBox.information(self, "完成", "SVN更新清理完成")
        else:
            self.status_changed.emit("SVN更新清理失败")
            QMessageBox.critical(self, "错误", message)

        # 清理工作线程
        if hasattr(self, 'svn_worker') and self.svn_worker:
            self.svn_worker.deleteLater()
            self.svn_worker = None

    def cancel_operation(self):
        """取消当前操作"""
        reply = QMessageBox.question(
            self, "确认取消",
            "确定要取消当前操作吗？\n\n注意：SVN操作可能无法立即停止。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 取消整包操作
            if hasattr(self, 'worker') and self.worker:
                if hasattr(self.worker, 'cancel'):
                    self.worker.cancel()
                self.worker.terminate()
                self.worker.wait(3000)  # 等待3秒
                self.worker.deleteLater()
                self.worker = None
                self.add_log_message("❌ 整包操作已取消")

            # 取消SVN更新清理操作
            if hasattr(self, 'svn_worker') and self.svn_worker:
                if hasattr(self.svn_worker, 'cancel'):
                    self.svn_worker.cancel()
                self.svn_worker.terminate()
                self.svn_worker.wait(3000)  # 等待3秒
                self.svn_worker.deleteLater()
                self.svn_worker = None
                self.add_log_message("❌ SVN更新清理已取消")

            # 恢复界面状态
            self.progress_bar.setVisible(False)
            self.set_buttons_enabled(True)
            self.status_changed.emit("操作已取消")

    def is_operation_running(self):
        """检查是否有操作正在进行"""
        worker_running = hasattr(self, 'worker') and self.worker and self.worker.isRunning()
        svn_worker_running = hasattr(self, 'svn_worker') and self.svn_worker and self.svn_worker.isRunning()
        return worker_running or svn_worker_running

    def get_current_operation(self):
        """获取当前操作类型"""
        if hasattr(self, 'worker') and self.worker and self.worker.isRunning():
            return "整包处理"
        elif hasattr(self, 'svn_worker') and self.svn_worker and self.svn_worker.isRunning():
            return "SVN更新清理"
        return None


from PyQt6.QtCore import QThread, pyqtSignal
from modules.package_merger_original import PackageMergerWorker


class DualModePackageMergerWorker(QThread):
    """双模式整包处理工作线程"""

    progress_updated = pyqtSignal(int)
    log_message = pyqtSignal(str)
    processing_finished = pyqtSignal(bool, str)

    def __init__(self, work_dir, official_complete_dir=None, pioneer_complete_dir=None):
        super().__init__()
        self.work_dir = work_dir
        self.official_complete_dir = official_complete_dir
        self.pioneer_complete_dir = pioneer_complete_dir
        self.pioneer_processor = PioneerProcessor()

    def run(self):
        """执行双模式处理"""
        try:
            # 确定处理模式
            if self.official_complete_dir and self.pioneer_complete_dir:
                mode_text = "双模式（官方 + 先锋）"
            elif self.official_complete_dir:
                mode_text = "单官方模式"
            elif self.pioneer_complete_dir:
                mode_text = "单先锋模式"
            else:
                mode_text = "未知模式"

            self.log_message.emit(f"� 开始整包处理 - {mode_text}")
            self.progress_updated.emit(10)

            # 检查是否需要创建工作副本
            if self.official_complete_dir and self.pioneer_complete_dir:
                # 双模式：需要创建两个工作目录
                official_work_dir, pioneer_work_dir = self.pioneer_processor.process_dual_mode(
                    self.work_dir,
                    self.official_complete_dir,
                    self.pioneer_complete_dir
                )
                self.progress_updated.emit(30)

                # 分别处理两个目录
                success_count = 0
                total_count = 0

                if official_work_dir and self.official_complete_dir:
                    total_count += 1
                    if self._process_single_mode(official_work_dir, self.official_complete_dir):
                        success_count += 1
                        self.log_message.emit("✅ 官方版本处理完成")
                    else:
                        self.log_message.emit("❌ 官方版本处理失败")
                    self.progress_updated.emit(60)

                if pioneer_work_dir and self.pioneer_complete_dir:
                    total_count += 1
                    if self._process_single_mode(pioneer_work_dir, self.pioneer_complete_dir):
                        success_count += 1
                        self.log_message.emit("✅ 先锋版本处理完成")
                    else:
                        self.log_message.emit("❌ 先锋版本处理失败")
                    self.progress_updated.emit(90)

                # 检查结果
                if success_count == total_count and total_count > 0:
                    self.progress_updated.emit(100)
                    self.log_message.emit(f"🎉 双模式处理完成！成功处理 {success_count}/{total_count} 个版本")
                    self.processing_finished.emit(True, f"双模式处理完成，成功处理 {success_count}/{total_count} 个版本")
                else:
                    self.log_message.emit(f"⚠️ 部分处理失败，成功 {success_count}/{total_count} 个版本")
                    self.processing_finished.emit(False, f"部分处理失败，成功 {success_count}/{total_count} 个版本")

            elif self.official_complete_dir:
                # 单官方模式
                if self._process_single_mode(self.work_dir, self.official_complete_dir):
                    self.progress_updated.emit(100)
                    self.log_message.emit("🎉 官方版本处理完成")
                    self.processing_finished.emit(True, "官方版本处理完成")
                else:
                    self.log_message.emit("❌ 官方版本处理失败")
                    self.processing_finished.emit(False, "官方版本处理失败")

            elif self.pioneer_complete_dir:
                # 单先锋模式
                # 先创建先锋工作目录
                _, pioneer_work_dir = self.pioneer_processor.process_dual_mode(
                    self.work_dir, None, self.pioneer_complete_dir
                )
                if pioneer_work_dir:
                    if self._process_single_mode(pioneer_work_dir, self.pioneer_complete_dir):
                        self.progress_updated.emit(100)
                        self.log_message.emit("🎉 先锋版本处理完成")
                        self.processing_finished.emit(True, "先锋版本处理完成")
                    else:
                        self.log_message.emit("❌ 先锋版本处理失败")
                        self.processing_finished.emit(False, "先锋版本处理失败")
                else:
                    self.log_message.emit("❌ 先锋工作目录创建失败")
                    self.processing_finished.emit(False, "先锋工作目录创建失败")
            else:
                self.log_message.emit("❌ 未选择任何处理模式")
                self.processing_finished.emit(False, "未选择任何处理模式")

        except Exception as e:
            self.log_message.emit(f"处理过程中发生错误: {str(e)}")
            self.processing_finished.emit(False, f"处理失败: {str(e)}")

    def _process_single_mode(self, work_dir, complete_dir):
        """处理单个模式"""
        try:
            # 创建原始的工作线程来处理
            worker = PackageMergerWorker(work_dir, complete_dir)

            # 连接信号
            worker.log_message.connect(self.log_message.emit)

            # 同步执行
            worker.run()

            return True

        except Exception as e:
            self.log_message.emit(f"❌ 处理失败: {str(e)}")
            return False


class SVNUpdateCleanWorker(QThread):
    """SVN更新清理工作线程"""

    progress_updated = pyqtSignal(int)
    log_message = pyqtSignal(str)
    processing_finished = pyqtSignal(bool, str)

    def __init__(self, directories):
        super().__init__()
        self.directories = directories  # [(name, path), ...]
        self.is_cancelled = False

    def cancel(self):
        """取消操作"""
        self.is_cancelled = True

    def run(self):
        """执行SVN更新清理"""
        try:
            import subprocess

            self.log_message.emit("开始SVN更新清理...")
            total_dirs = len(self.directories)

            for i, (name, directory) in enumerate(self.directories):
                # 检查是否被取消
                if self.is_cancelled:
                    self.log_message.emit("❌ 操作已被取消")
                    self.processing_finished.emit(False, "操作已被取消")
                    return

                self.log_message.emit(f"\n处理 {name}: {directory}")

                # 检查是否为SVN工作目录
                svn_dir = os.path.join(directory, '.svn')
                if not os.path.exists(svn_dir):
                    self.log_message.emit(f"  ⚠️ 警告: {directory} 不是SVN工作目录")
                    continue

                # 更新进度
                base_progress = int((i / total_dirs) * 100)
                self.progress_updated.emit(base_progress)

                try:
                    # 执行SVN清理
                    self.log_message.emit("  🧹 执行SVN清理...")
                    cleanup_result = subprocess.run(
                        ['svn', 'cleanup', directory],
                        capture_output=True,
                        text=True,
                        cwd=directory,
                        timeout=300  # 5分钟超时
                    )

                    if cleanup_result.returncode == 0:
                        self.log_message.emit("  ✅ SVN清理完成")
                    else:
                        self.log_message.emit(f"  ❌ SVN清理失败: {cleanup_result.stderr}")
                        continue

                    # 更新进度
                    self.progress_updated.emit(base_progress + 25)

                    # 执行SVN更新
                    self.log_message.emit("  📥 执行SVN更新...")
                    update_result = subprocess.run(
                        ['svn', 'update', directory],
                        capture_output=True,
                        text=True,
                        cwd=directory,
                        timeout=600  # 10分钟超时
                    )

                    if update_result.returncode == 0:
                        # 解析更新结果
                        output_lines = update_result.stdout.strip().split('\n')
                        if output_lines:
                            last_line = output_lines[-1]
                            if 'At revision' in last_line or '版本' in last_line:
                                self.log_message.emit(f"  ✅ SVN更新完成: {last_line}")
                            else:
                                self.log_message.emit("  ✅ SVN更新完成")
                                if update_result.stdout.strip():
                                    self.log_message.emit(f"  更新详情:\n{update_result.stdout}")
                        else:
                            self.log_message.emit("  ✅ SVN更新完成")
                    else:
                        self.log_message.emit(f"  ❌ SVN更新失败: {update_result.stderr}")
                        continue

                except subprocess.TimeoutExpired:
                    self.log_message.emit(f"  ❌ 操作超时: {name}")
                    continue
                except FileNotFoundError:
                    self.log_message.emit("  ❌ 未找到SVN命令，请确保已安装SVN客户端")
                    self.processing_finished.emit(False, "未找到SVN命令，请确保已安装SVN客户端")
                    return
                except Exception as e:
                    self.log_message.emit(f"  ❌ 处理失败: {str(e)}")
                    continue

                # 更新进度
                self.progress_updated.emit(int(((i + 1) / total_dirs) * 100))

            self.progress_updated.emit(100)
            self.log_message.emit("\n🎉 SVN更新清理全部完成！")
            self.processing_finished.emit(True, "SVN更新清理完成")

        except Exception as e:
            self.log_message.emit(f"\n❌ SVN更新清理过程中发生错误: {str(e)}")
            self.processing_finished.emit(False, f"SVN更新清理失败: {str(e)}")
