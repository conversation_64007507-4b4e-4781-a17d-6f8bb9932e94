# Python
__pycache__/
*.py[cod]
*$py.class
*.so

# Flask
instance/
.webassets-cache

# Environment variables
.env
.flaskenv

# Database
*.db
*.sqlite
*.sqlite3

# Logs
logs/
*.log

# Uploads
data/uploads/*
!data/uploads/.gitkeep
data/uploads/updates/*
!data/uploads/updates/.gitkeep

# Database files
data/database/*
!data/database/.gitkeep

# Temporary files
*.tmp
*.temp
*.bak
*.backup

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Docker
.dockerignore

# Release files
server_release/
*.zip
*.tar.gz
