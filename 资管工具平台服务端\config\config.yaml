# 资管工具平台服务端配置文件

# 应用配置
app:
  name: "资管工具平台服务端"
  version: "1.0.0"
  debug: false
  host: "0.0.0.0"
  port: 5000
  secret_key: "your-secret-key-change-in-production"

# 数据库配置
database:
  url: "sqlite:///data/database/app.db"
  echo: false
  pool_size: 10
  max_overflow: 20
  pool_timeout: 30
  pool_recycle: 3600

# Redis配置
redis:
  host: "redis"
  port: 6379
  db: 0
  password: null
  decode_responses: true

# JWT配置
jwt:
  secret_key: "your-jwt-secret-change-in-production"
  algorithm: "HS256"
  access_token_expires: 3600  # 1小时
  refresh_token_expires: 604800  # 7天

# 激活码服务配置
license:
  master_secret: "ResourceManagementTool2024"
  validation_cache_ttl: 300  # 5分钟
  max_devices_per_license: 5
  trial_duration_days: 30
  grace_period_days: 7
  
  # 许可证类型配置
  types:
    free:
      features: ["basic_features"]
      max_devices: 1
    trial:
      features: ["all_features"]
      max_devices: 1
      duration_days: 30
    professional:
      features: ["all_features"]
      max_devices: 3
    enterprise:
      features: ["all_features", "enterprise_features"]
      max_devices: 10

# 更新服务配置
update:
  version_check_cache_ttl: 3600  # 1小时
  download_rate_limit: "10/minute"
  max_file_size: 104857600  # 100MB
  allowed_extensions: [".zip", ".7z", ".exe", ".msi"]
  
  # 版本管理
  current_version: "1.0.0"
  minimum_version: "0.9.0"
  force_update_below: "0.8.0"
  
  # 文件存储
  upload_folder: "data/uploads"
  temp_folder: "data/temp"

# 安全配置
security:
  # API密钥认证
  api_keys:
    admin: "admin-api-key-change-in-production"
    client: "client-api-key-change-in-production"
  
  # 请求限制
  rate_limits:
    default: "100/hour"
    license_validate: "60/hour"
    update_check: "30/hour"
    download: "10/hour"
  
  # CORS配置
  cors:
    origins: ["*"]
    methods: ["GET", "POST", "PUT", "DELETE"]
    headers: ["Content-Type", "Authorization", "X-API-Key"]

# 日志配置
logging:
  level: "INFO"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
  
  # 日志文件
  files:
    app: "data/logs/app.log"
    access: "data/logs/access.log"
    error: "data/logs/error.log"
    license: "data/logs/license.log"
    update: "data/logs/update.log"
  
  # 日志轮转
  rotation: "1 day"
  retention: "30 days"
  compression: "gz"

# 监控配置
monitoring:
  enabled: true
  metrics_endpoint: "/metrics"
  health_endpoint: "/health"
  
  # Prometheus配置
  prometheus:
    enabled: true
    port: 9090
  
  # 健康检查
  health_checks:
    database: true
    redis: true
    disk_space: true
    memory: true

# 邮件配置（可选）
email:
  enabled: false
  smtp_server: "smtp.example.com"
  smtp_port: 587
  username: "<EMAIL>"
  password: "your-email-password"
  use_tls: true
  
  # 邮件模板
  templates:
    license_activated: "templates/license_activated.html"
    license_expired: "templates/license_expired.html"
    update_available: "templates/update_available.html"

# 备份配置
backup:
  enabled: true
  schedule: "0 2 * * *"  # 每天凌晨2点
  retention_days: 30
  backup_folder: "backups"
  
  # 备份内容
  include:
    - "data/database"
    - "config"
    - "data/logs"
  
  exclude:
    - "data/temp"
    - "data/cache"

# 开发配置
development:
  auto_reload: true
  debug_toolbar: true
  profiler: false
  
  # 测试数据
  create_test_data: true
  test_license_codes: 10
  test_update_files: true

# 生产配置
production:
  workers: 4
  worker_class: "gevent"
  worker_connections: 1000
  max_requests: 1000
  max_requests_jitter: 100
  timeout: 30
  keepalive: 2
  
  # SSL配置
  ssl:
    enabled: true
    cert_file: "config/ssl/cert.pem"
    key_file: "config/ssl/key.pem"
    ca_file: "config/ssl/ca.pem"
