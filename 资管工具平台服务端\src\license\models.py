#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
激活码服务数据模型
"""

from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, ForeignKey
from sqlalchemy.orm import relationship
from src.database import db


class LicenseRecord(db.Model):
    """许可证记录"""
    __tablename__ = 'license_records'
    
    id = Column(Integer, primary_key=True)
    activation_code = Column(String(50), unique=True, nullable=False, index=True)
    license_type = Column(String(20), nullable=False)
    user_name = Column(String(100), nullable=False)
    email = Column(String(200), nullable=False)
    company_name = Column(String(200), default='')
    hardware_id = Column(String(64), default='')
    
    created_date = Column(DateTime, default=datetime.utcnow, nullable=False)
    expire_date = Column(DateTime, nullable=True)
    last_validation = Column(DateTime, nullable=True)
    
    validation_count = Column(Integer, default=0)
    max_activations = Column(Integer, default=1)
    is_active = Column(Boolean, default=True)
    
    # 关联验证记录
    validations = relationship("LicenseValidation", back_populates="license_record")
    
    def __repr__(self):
        return f'<LicenseRecord {self.activation_code[:8]}...>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'activation_code': self.activation_code,
            'license_type': self.license_type,
            'user_name': self.user_name,
            'email': self.email,
            'company_name': self.company_name,
            'hardware_id': self.hardware_id,
            'created_date': self.created_date.isoformat() if self.created_date else None,
            'expire_date': self.expire_date.isoformat() if self.expire_date else None,
            'last_validation': self.last_validation.isoformat() if self.last_validation else None,
            'validation_count': self.validation_count,
            'max_activations': self.max_activations,
            'is_active': self.is_active
        }


class LicenseValidation(db.Model):
    """许可证验证记录"""
    __tablename__ = 'license_validations'
    
    id = Column(Integer, primary_key=True)
    license_record_id = Column(Integer, ForeignKey('license_records.id'), nullable=True)
    activation_code = Column(String(50), nullable=False, index=True)
    hardware_id = Column(String(64), nullable=False)
    
    validation_time = Column(DateTime, default=datetime.utcnow, nullable=False)
    is_valid = Column(Boolean, nullable=False)
    error_message = Column(Text, nullable=True)
    
    client_ip = Column(String(45), nullable=True)  # 支持IPv6
    user_agent = Column(Text, nullable=True)
    
    # 关联许可证记录
    license_record = relationship("LicenseRecord", back_populates="validations")
    
    def __repr__(self):
        return f'<LicenseValidation {self.activation_code[:8]}... - {self.is_valid}>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'license_record_id': self.license_record_id,
            'activation_code': self.activation_code,
            'hardware_id': self.hardware_id,
            'validation_time': self.validation_time.isoformat() if self.validation_time else None,
            'is_valid': self.is_valid,
            'error_message': self.error_message,
            'client_ip': self.client_ip,
            'user_agent': self.user_agent
        }


class LicenseFeature(db.Model):
    """许可证功能"""
    __tablename__ = 'license_features'
    
    id = Column(Integer, primary_key=True)
    license_record_id = Column(Integer, ForeignKey('license_records.id'), nullable=False)
    feature_name = Column(String(50), nullable=False)
    is_enabled = Column(Boolean, default=True)
    
    def __repr__(self):
        return f'<LicenseFeature {self.feature_name} - {self.is_enabled}>'


class LicenseUsage(db.Model):
    """许可证使用统计"""
    __tablename__ = 'license_usage'
    
    id = Column(Integer, primary_key=True)
    license_record_id = Column(Integer, ForeignKey('license_records.id'), nullable=False)
    date = Column(DateTime, default=datetime.utcnow, nullable=False)
    usage_count = Column(Integer, default=0)
    feature_usage = Column(Text, nullable=True)  # JSON格式存储功能使用情况
    
    def __repr__(self):
        return f'<LicenseUsage {self.date} - {self.usage_count}>'
