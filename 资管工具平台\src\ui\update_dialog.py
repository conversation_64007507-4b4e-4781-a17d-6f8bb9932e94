#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新对话框
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTextEdit, QProgressBar, QGroupBox,
                            QCheckBox, QFrame, QTabWidget, QWidget, QGridLayout,
                            QComboBox, QSpinBox, QMessageBox)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QIcon, QPixmap

from updater.update_manager import UpdateManager
from updater.version_checker import VersionInfo, UpdateCheckResult, format_changelog
from updater.download_manager import format_speed, format_time_remaining


class UpdateDialog(QDialog):
    """更新对话框"""
    
    # 信号定义
    update_accepted = pyqtSignal()  # 用户接受更新
    update_rejected = pyqtSignal()  # 用户拒绝更新
    
    def __init__(self, update_manager: UpdateManager, version_info: VersionInfo = None, parent=None):
        super().__init__(parent)
        self.update_manager = update_manager
        self.version_info = version_info
        self.is_downloading = False
        self.is_ready_to_install = False
        
        self.init_ui()
        self.setup_connections()
        
        if version_info:
            self.show_update_info(version_info)
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("软件更新")
        self.setModal(True)
        self.setFixedSize(750, 650)  # 增加窗口大小
        
        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(30, 30, 30, 30)  # 增加边距
        layout.setSpacing(20)  # 增加间距
        
        # 创建标题区域
        self.create_header(layout)
        
        # 创建标签页
        self.create_tabs(layout)
        
        # 创建进度区域
        self.create_progress_area(layout)
        
        # 创建按钮区域
        self.create_buttons(layout)
    
    def create_header(self, layout):
        """创建标题区域"""
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(15, 15, 15, 15)
        
        # 更新图标
        icon_label = QLabel()
        try:
            icon_label.setPixmap(QIcon("resources/icons/app_icon.png").pixmap(48, 48))
        except:
            icon_label.setText("🔄")
            icon_label.setFont(QFont("Arial", 24))
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(icon_label)
        
        # 标题信息
        title_layout = QVBoxLayout()
        
        self.title_label = QLabel("发现新版本")
        self.title_label.setFont(QFont("Microsoft YaHei UI", 20, QFont.Weight.Bold))  # 增加字体大小
        self.title_label.setStyleSheet("margin: 5px 0px;")  # 增加边距
        title_layout.addWidget(self.title_label)

        self.subtitle_label = QLabel("有新版本可用，建议您及时更新")
        self.subtitle_label.setFont(QFont("Microsoft YaHei UI", 12))  # 增加字体大小
        self.subtitle_label.setStyleSheet("color: #666666; margin-bottom: 10px;")  # 增加下边距
        title_layout.addWidget(self.subtitle_label)
        
        header_layout.addLayout(title_layout)
        header_layout.addStretch()
        
        layout.addWidget(header_frame)
    
    def create_tabs(self, layout):
        """创建标签页"""
        self.tab_widget = QTabWidget()
        self.tab_widget.setFont(QFont("Microsoft YaHei UI", 11))  # 增加标签页字体大小

        # 更新信息标签页
        self.create_update_info_tab()

        # 更新设置标签页
        self.create_settings_tab()

        layout.addWidget(self.tab_widget)
    
    def create_update_info_tab(self):
        """创建更新信息标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(20)  # 增加间距

        # 版本信息组
        version_group = QGroupBox("版本信息")
        version_group.setFont(QFont("Microsoft YaHei UI", 12))  # 增加组标题字体
        version_layout = QGridLayout(version_group)
        version_layout.setSpacing(12)  # 增加间距

        # 创建标签并设置字体
        current_label = QLabel("当前版本:")
        current_label.setFont(QFont("Microsoft YaHei UI", 11))
        version_layout.addWidget(current_label, 0, 0)
        self.current_version_label = QLabel("1.0.0")
        self.current_version_label.setFont(QFont("Consolas", 12))  # 增加字体大小
        version_layout.addWidget(self.current_version_label, 0, 1)

        latest_label = QLabel("最新版本:")
        latest_label.setFont(QFont("Microsoft YaHei UI", 11))
        version_layout.addWidget(latest_label, 1, 0)
        self.latest_version_label = QLabel("1.1.0")
        self.latest_version_label.setFont(QFont("Consolas", 12, QFont.Weight.Bold))  # 增加字体大小
        self.latest_version_label.setStyleSheet("color: #4CAF50;")
        version_layout.addWidget(self.latest_version_label, 1, 1)

        date_label = QLabel("发布日期:")
        date_label.setFont(QFont("Microsoft YaHei UI", 11))
        version_layout.addWidget(date_label, 2, 0)
        self.release_date_label = QLabel("2024-08-15")
        self.release_date_label.setFont(QFont("Microsoft YaHei UI", 11))
        version_layout.addWidget(self.release_date_label, 2, 1)

        size_label = QLabel("更新大小:")
        size_label.setFont(QFont("Microsoft YaHei UI", 11))
        version_layout.addWidget(size_label, 3, 0)
        self.file_size_label = QLabel("50.0 MB")
        self.file_size_label.setFont(QFont("Microsoft YaHei UI", 11))
        version_layout.addWidget(self.file_size_label, 3, 1)
        
        layout.addWidget(version_group)
        
        # 更新日志组
        changelog_group = QGroupBox("更新内容")
        changelog_layout = QVBoxLayout(changelog_group)
        
        self.changelog_text = QTextEdit()
        self.changelog_text.setReadOnly(True)
        self.changelog_text.setMaximumHeight(200)
        changelog_layout.addWidget(self.changelog_text)
        
        layout.addWidget(changelog_group)
        
        self.tab_widget.addTab(widget, "更新信息")
    
    def create_settings_tab(self):
        """创建设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        
        # 自动更新设置组
        auto_update_group = QGroupBox("自动更新设置")
        auto_update_layout = QVBoxLayout(auto_update_group)
        
        self.auto_check_cb = QCheckBox("启用自动检查更新")
        auto_update_layout.addWidget(self.auto_check_cb)
        
        # 检查间隔
        interval_layout = QHBoxLayout()
        interval_layout.addWidget(QLabel("检查间隔:"))
        self.check_interval_spin = QSpinBox()
        self.check_interval_spin.setRange(1, 168)  # 1小时到1周
        self.check_interval_spin.setValue(24)
        self.check_interval_spin.setSuffix(" 小时")
        interval_layout.addWidget(self.check_interval_spin)
        interval_layout.addStretch()
        auto_update_layout.addLayout(interval_layout)
        
        # 更新渠道
        channel_layout = QHBoxLayout()
        channel_layout.addWidget(QLabel("更新渠道:"))
        self.update_channel_combo = QComboBox()
        self.update_channel_combo.addItems(["稳定版", "测试版", "开发版"])
        channel_layout.addWidget(self.update_channel_combo)
        channel_layout.addStretch()
        auto_update_layout.addLayout(channel_layout)
        
        self.auto_download_cb = QCheckBox("自动下载更新")
        auto_update_layout.addWidget(self.auto_download_cb)
        
        self.auto_install_cb = QCheckBox("自动安装更新（重启后生效）")
        auto_update_layout.addWidget(self.auto_install_cb)
        
        layout.addWidget(auto_update_group)
        
        # 通知设置组
        notification_group = QGroupBox("通知设置")
        notification_layout = QVBoxLayout(notification_group)
        
        self.notify_available_cb = QCheckBox("发现新版本时通知")
        notification_layout.addWidget(self.notify_available_cb)
        
        self.notify_download_cb = QCheckBox("下载完成时通知")
        notification_layout.addWidget(self.notify_download_cb)
        
        layout.addWidget(notification_group)
        
        layout.addStretch()
        self.tab_widget.addTab(widget, "设置")
    
    def create_progress_area(self, layout):
        """创建进度区域"""
        self.progress_frame = QFrame()
        self.progress_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        self.progress_frame.setVisible(False)
        
        progress_layout = QVBoxLayout(self.progress_frame)
        progress_layout.setContentsMargins(15, 15, 15, 15)
        
        # 进度标签
        self.progress_label = QLabel("准备下载...")
        self.progress_label.setFont(QFont("Microsoft YaHei UI", 9))
        progress_layout.addWidget(self.progress_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        progress_layout.addWidget(self.progress_bar)
        
        # 详细信息
        details_layout = QHBoxLayout()
        
        self.speed_label = QLabel("速度: 0 KB/s")
        self.speed_label.setFont(QFont("Microsoft YaHei UI", 8))
        details_layout.addWidget(self.speed_label)
        
        details_layout.addStretch()
        
        self.time_remaining_label = QLabel("剩余时间: 计算中...")
        self.time_remaining_label.setFont(QFont("Microsoft YaHei UI", 8))
        details_layout.addWidget(self.time_remaining_label)
        
        progress_layout.addLayout(details_layout)
        
        layout.addWidget(self.progress_frame)
    
    def create_buttons(self, layout):
        """创建按钮区域"""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)  # 增加按钮间距
        button_layout.addStretch()

        self.later_button = QPushButton("稍后提醒")
        self.later_button.setMinimumSize(120, 40)  # 增加按钮大小
        self.later_button.setFont(QFont("Microsoft YaHei UI", 11))  # 增加字体大小
        button_layout.addWidget(self.later_button)

        self.skip_button = QPushButton("跳过此版本")
        self.skip_button.setMinimumSize(120, 40)  # 增加按钮大小
        self.skip_button.setFont(QFont("Microsoft YaHei UI", 11))  # 增加字体大小
        button_layout.addWidget(self.skip_button)

        self.download_button = QPushButton("立即更新")
        self.download_button.setMinimumSize(120, 40)  # 增加按钮大小
        self.download_button.setFont(QFont("Microsoft YaHei UI", 11))  # 增加字体大小
        self.download_button.setDefault(True)
        button_layout.addWidget(self.download_button)

        layout.addLayout(button_layout)
    
    def setup_connections(self):
        """设置信号连接"""
        # 按钮连接
        self.later_button.clicked.connect(self.reject)
        self.skip_button.clicked.connect(self.skip_version)
        self.download_button.clicked.connect(self.start_download_or_install)
        
        # 更新管理器信号连接
        if hasattr(self.update_manager, 'update_manager'):
            # 如果是主窗口，获取其更新管理器
            actual_update_manager = self.update_manager.update_manager
            actual_update_manager.download_progress.connect(self.update_progress)
            actual_update_manager.update_ready.connect(self.on_update_ready)
            actual_update_manager.update_failed.connect(self.on_update_failed)
            actual_update_manager.update_installed.connect(self.on_update_installed)
        else:
            # 如果直接是更新管理器
            self.update_manager.download_progress.connect(self.update_progress)
            self.update_manager.update_ready.connect(self.on_update_ready)
            self.update_manager.update_failed.connect(self.on_update_failed)
            self.update_manager.update_installed.connect(self.on_update_installed)
        
        # 设置变更连接
        self.auto_check_cb.toggled.connect(self.save_settings)
        self.check_interval_spin.valueChanged.connect(self.save_settings)
        self.update_channel_combo.currentTextChanged.connect(self.save_settings)
        self.auto_download_cb.toggled.connect(self.save_settings)
        self.auto_install_cb.toggled.connect(self.save_settings)
    
    def show_update_info(self, version_info: VersionInfo):
        """显示更新信息"""
        self.version_info = version_info
        
        # 更新版本信息
        if hasattr(self.update_manager, 'update_manager'):
            current_version = self.update_manager.update_manager._get_current_version()
        else:
            current_version = self.update_manager._get_current_version()
        self.current_version_label.setText(current_version)
        self.latest_version_label.setText(version_info.version)
        self.release_date_label.setText(version_info.release_date.strftime("%Y-%m-%d"))
        
        # 格式化文件大小
        size_mb = version_info.file_size / (1024 * 1024)
        self.file_size_label.setText(f"{size_mb:.1f} MB")
        
        # 显示更新日志
        formatted_changelog = format_changelog(version_info.changelog)
        self.changelog_text.setHtml(formatted_changelog)
        
        # 加载当前设置
        self.load_settings()
    
    def load_settings(self):
        """加载设置"""
        settings = self.update_manager.get_update_settings()
        
        self.auto_check_cb.setChecked(settings.get('auto_check', True))
        self.check_interval_spin.setValue(settings.get('check_interval', 24))
        
        # 更新渠道映射
        channel_map = {'stable': 0, 'beta': 1, 'alpha': 2}
        channel_index = channel_map.get(settings.get('update_channel', 'stable'), 0)
        self.update_channel_combo.setCurrentIndex(channel_index)
        
        self.auto_download_cb.setChecked(settings.get('auto_download', False))
        self.auto_install_cb.setChecked(settings.get('auto_install', False))
    
    def save_settings(self):
        """保存设置"""
        # 渠道映射
        channel_map = {0: 'stable', 1: 'beta', 2: 'alpha'}
        
        settings = {
            'auto_check': self.auto_check_cb.isChecked(),
            'check_interval': self.check_interval_spin.value(),
            'update_channel': channel_map.get(self.update_channel_combo.currentIndex(), 'stable'),
            'auto_download': self.auto_download_cb.isChecked(),
            'auto_install': self.auto_install_cb.isChecked()
        }
        
        self.update_manager.update_settings(settings)
    
    def start_download_or_install(self):
        """开始下载或安装"""
        if self.is_ready_to_install:
            # 安装更新
            self.install_update()
        elif not self.is_downloading:
            # 开始下载
            self.start_download()
    
    def start_download(self):
        """开始下载"""
        if not self.version_info:
            return
        
        self.is_downloading = True
        self.progress_frame.setVisible(True)
        
        # 更新按钮状态
        self.download_button.setText("取消下载")
        self.download_button.clicked.disconnect()
        self.download_button.clicked.connect(self.cancel_download)
        
        # 开始下载
        success = self.update_manager.download_update(self.version_info)
        if not success:
            self.on_update_failed("启动下载失败")
    
    def cancel_download(self):
        """取消下载"""
        self.update_manager.cancel_update()
        self.reset_ui_state()
    
    def install_update(self):
        """安装更新"""
        reply = QMessageBox.question(
            self, "确认安装",
            "安装更新需要重启应用程序，确定要继续吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.update_manager.install_update()
    
    def skip_version(self):
        """跳过此版本"""
        if self.version_info:
            # 这里可以记录跳过的版本，下次不再提醒
            pass
        self.reject()
    
    def update_progress(self, downloaded: int, total: int, speed: int):
        """更新下载进度"""
        if total > 0:
            progress = int((downloaded / total) * 100)
            self.progress_bar.setValue(progress)
            
            # 更新进度标签
            downloaded_mb = downloaded / (1024 * 1024)
            total_mb = total / (1024 * 1024)
            self.progress_label.setText(f"正在下载... {downloaded_mb:.1f} MB / {total_mb:.1f} MB")
            
            # 更新速度和剩余时间
            self.speed_label.setText(f"速度: {format_speed(speed)}")
            
            remaining_bytes = total - downloaded
            self.time_remaining_label.setText(f"剩余时间: {format_time_remaining(remaining_bytes, speed)}")
    
    def on_update_ready(self, file_path: str):
        """更新准备就绪"""
        self.is_downloading = False
        self.is_ready_to_install = True
        
        self.progress_label.setText("下载完成，准备安装...")
        self.progress_bar.setValue(100)
        
        # 更新按钮
        self.download_button.setText("立即安装")
        self.download_button.clicked.disconnect()
        self.download_button.clicked.connect(self.install_update)
    
    def on_update_failed(self, error_message: str):
        """更新失败"""
        QMessageBox.warning(self, "更新失败", f"更新过程中发生错误:\n{error_message}")
        self.reset_ui_state()
    
    def on_update_installed(self):
        """更新安装完成"""
        QMessageBox.information(self, "更新完成", "更新安装完成，应用程序将重启。")
        self.accept()
    
    def reset_ui_state(self):
        """重置UI状态"""
        self.is_downloading = False
        self.is_ready_to_install = False
        self.progress_frame.setVisible(False)
        
        self.download_button.setText("立即更新")
        self.download_button.clicked.disconnect()
        self.download_button.clicked.connect(self.start_download_or_install)
