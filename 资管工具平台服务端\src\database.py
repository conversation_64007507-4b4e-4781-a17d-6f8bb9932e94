#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理模块
"""

import os
from pathlib import Path
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy import event
from sqlalchemy.engine import Engine
from loguru import logger

# 创建数据库实例
db = SQLAlchemy()


def init_db():
    """初始化数据库"""
    try:
        # 确保数据库目录存在
        db_path = Path(db.engine.url.database)
        if db_path.name != ':memory:':
            db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 创建所有表
        db.create_all()
        
        # 创建初始数据
        create_initial_data()
        
        logger.info("数据库初始化完成")
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise


def create_initial_data():
    """创建初始数据"""
    try:
        from src.license.models import LicenseRecord
        from src.update.models import VersionInfo
        from datetime import datetime
        
        # 检查是否已有数据
        if LicenseRecord.query.first() is None:
            logger.info("创建初始许可证数据...")
            # 这里可以添加初始许可证数据
        
        if VersionInfo.query.first() is None:
            logger.info("创建初始版本数据...")
            # 创建初始版本信息
            initial_version = VersionInfo(
                version='1.0.0',
                release_date='2025-01-01',
                description='初始版本',
                download_url='',
                file_size=0,
                is_active=True,
                is_force_update=False
            )
            db.session.add(initial_version)
        
        db.session.commit()
        
    except Exception as e:
        logger.error(f"创建初始数据失败: {e}")
        db.session.rollback()


@event.listens_for(Engine, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    """设置SQLite优化参数"""
    if 'sqlite' in str(dbapi_connection):
        cursor = dbapi_connection.cursor()
        
        # 启用外键约束
        cursor.execute("PRAGMA foreign_keys=ON")
        
        # 设置WAL模式以提高并发性能
        cursor.execute("PRAGMA journal_mode=WAL")
        
        # 设置同步模式
        cursor.execute("PRAGMA synchronous=NORMAL")
        
        # 设置缓存大小
        cursor.execute("PRAGMA cache_size=10000")
        
        # 设置临时存储
        cursor.execute("PRAGMA temp_store=MEMORY")
        
        cursor.close()


class DatabaseManager:
    """数据库管理器"""
    
    @staticmethod
    def backup_database(backup_path: str = None):
        """备份数据库"""
        try:
            if backup_path is None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                backup_path = f"backups/database_backup_{timestamp}.db"
            
            # 确保备份目录存在
            Path(backup_path).parent.mkdir(parents=True, exist_ok=True)
            
            # 获取数据库文件路径
            db_path = Path(db.engine.url.database)
            
            if db_path.exists():
                import shutil
                shutil.copy2(db_path, backup_path)
                logger.info(f"数据库备份完成: {backup_path}")
                return True, backup_path
            else:
                logger.warning("数据库文件不存在，无法备份")
                return False, "数据库文件不存在"
                
        except Exception as e:
            logger.error(f"数据库备份失败: {e}")
            return False, str(e)
    
    @staticmethod
    def restore_database(backup_path: str):
        """恢复数据库"""
        try:
            backup_file = Path(backup_path)
            if not backup_file.exists():
                return False, "备份文件不存在"
            
            # 获取数据库文件路径
            db_path = Path(db.engine.url.database)
            
            # 关闭数据库连接
            db.session.close()
            db.engine.dispose()
            
            # 恢复数据库文件
            import shutil
            shutil.copy2(backup_file, db_path)
            
            logger.info(f"数据库恢复完成: {backup_path}")
            return True, "数据库恢复成功"
            
        except Exception as e:
            logger.error(f"数据库恢复失败: {e}")
            return False, str(e)
    
    @staticmethod
    def get_database_info():
        """获取数据库信息"""
        try:
            # 获取数据库文件信息
            db_path = Path(db.engine.url.database)
            
            info = {
                'database_path': str(db_path),
                'database_exists': db_path.exists(),
                'database_size': 0,
                'table_count': 0,
                'record_counts': {}
            }
            
            if db_path.exists():
                info['database_size'] = db_path.stat().st_size
            
            # 获取表信息
            inspector = db.inspect(db.engine)
            tables = inspector.get_table_names()
            info['table_count'] = len(tables)
            
            # 获取每个表的记录数
            for table in tables:
                try:
                    result = db.session.execute(f"SELECT COUNT(*) FROM {table}")
                    count = result.scalar()
                    info['record_counts'][table] = count
                except Exception:
                    info['record_counts'][table] = 'N/A'
            
            return info
            
        except Exception as e:
            logger.error(f"获取数据库信息失败: {e}")
            return {'error': str(e)}
    
    @staticmethod
    def optimize_database():
        """优化数据库"""
        try:
            # SQLite优化命令
            if 'sqlite' in str(db.engine.url):
                db.session.execute("VACUUM")
                db.session.execute("ANALYZE")
                db.session.commit()
                
                logger.info("数据库优化完成")
                return True, "数据库优化成功"
            else:
                return False, "当前数据库不支持此优化操作"
                
        except Exception as e:
            logger.error(f"数据库优化失败: {e}")
            return False, str(e)
    
    @staticmethod
    def check_database_health():
        """检查数据库健康状态"""
        try:
            # 执行简单查询测试连接
            db.session.execute("SELECT 1")
            
            # 检查数据库文件
            db_path = Path(db.engine.url.database)
            if db_path.name != ':memory:' and not db_path.exists():
                return False, "数据库文件不存在"
            
            # 检查表结构
            inspector = db.inspect(db.engine)
            tables = inspector.get_table_names()
            
            if not tables:
                return False, "数据库中没有表"
            
            return True, f"数据库健康，包含 {len(tables)} 个表"
            
        except Exception as e:
            logger.error(f"数据库健康检查失败: {e}")
            return False, str(e)
