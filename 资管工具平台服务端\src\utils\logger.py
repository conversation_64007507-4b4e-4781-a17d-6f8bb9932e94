#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志工具模块
"""

import sys
from pathlib import Path
from loguru import logger


def setup_logging(config):
    """设置日志配置"""
    # 移除默认处理器
    logger.remove()
    
    # 控制台输出
    logger.add(
        sys.stdout,
        level=config.LOG_LEVEL,
        format=config.LOG_FORMAT or "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
        colorize=True
    )
    
    # 应用日志文件
    if config.LOG_FILES.get('app'):
        app_log_path = Path(config.LOG_FILES['app'])
        app_log_path.parent.mkdir(parents=True, exist_ok=True)
        
        logger.add(
            app_log_path,
            level=config.LOG_LEVEL,
            format=config.LOG_FORMAT or "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
            rotation=config.LOG_ROTATION,
            retention=config.LOG_RETENTION,
            compression="gz" if hasattr(config, 'LOG_COMPRESSION') else None
        )
    
    # 错误日志文件
    if config.LOG_FILES.get('error'):
        error_log_path = Path(config.LOG_FILES['error'])
        error_log_path.parent.mkdir(parents=True, exist_ok=True)
        
        logger.add(
            error_log_path,
            level="ERROR",
            format=config.LOG_FORMAT or "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
            rotation=config.LOG_ROTATION,
            retention=config.LOG_RETENTION,
            compression="gz" if hasattr(config, 'LOG_COMPRESSION') else None
        )
    
    # 许可证日志文件
    if config.LOG_FILES.get('license'):
        license_log_path = Path(config.LOG_FILES['license'])
        license_log_path.parent.mkdir(parents=True, exist_ok=True)
        
        logger.add(
            license_log_path,
            level="INFO",
            format=config.LOG_FORMAT or "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
            rotation=config.LOG_ROTATION,
            retention=config.LOG_RETENTION,
            compression="gz" if hasattr(config, 'LOG_COMPRESSION') else None,
            filter=lambda record: "license" in record["name"].lower()
        )
    
    # 更新日志文件
    if config.LOG_FILES.get('update'):
        update_log_path = Path(config.LOG_FILES['update'])
        update_log_path.parent.mkdir(parents=True, exist_ok=True)
        
        logger.add(
            update_log_path,
            level="INFO",
            format=config.LOG_FORMAT or "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
            rotation=config.LOG_ROTATION,
            retention=config.LOG_RETENTION,
            compression="gz" if hasattr(config, 'LOG_COMPRESSION') else None,
            filter=lambda record: "update" in record["name"].lower()
        )
