#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器
"""

import os
import sys
import json
import configparser
import shutil
from pathlib import Path
from typing import Dict, Any, Optional


class ConfigManager:
    """配置管理器类 - 单例模式"""

    _instance = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ConfigManager, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        # 避免重复初始化
        if ConfigManager._initialized:
            return

        # 检测是否为打包后的可执行文件
        if getattr(sys, 'frozen', False):
            # 打包后的可执行文件
            self.project_root = Path(sys.executable).parent
        else:
            # 开发环境
            self.project_root = Path(__file__).parent.parent.parent

        self.config_dir = self.project_root / "config"
        self.user_config_file = self.config_dir / "user_config.json"

        # 确保配置目录存在
        self.config_dir.mkdir(exist_ok=True)

        # 如果是首次运行，复制默认配置文件
        self._ensure_config_files()

        # 加载配置
        self.user_config = self.load_user_config()
        self.merge_rules = self.load_merge_rules()
        self.xml_config = self.load_xml_config()

        ConfigManager._initialized = True

    def _ensure_config_files(self):
        """确保配置文件存在（发布版本用）"""
        try:
            # 检查是否为打包后的可执行文件
            if getattr(sys, 'frozen', False):
                # 从内嵌资源复制配置文件模板
                import tempfile

                # 默认配置文件内容
                default_config = {
                    "archive_processing": {
                        "default_compression_level": 5,
                        "supported_formats": [".zip", ".7z", ".rar", ".tar", ".gz"],
                        "max_file_size_mb": 1024,
                        "temp_dir": "temp"
                    },
                    "package_merger": {
                        "merge_strategy": "smart",
                        "conflict_resolution": "ask",
                        "backup_original": True
                    },
                    "svn_uploader": {
                        "default_server": "",
                        "timeout_seconds": 30,
                        "retry_count": 3,
                        "auto_cleanup": True
                    },
                    "ui": {
                        "theme": "default",
                        "language": "zh_CN",
                        "window_size": [1200, 800],
                        "remember_window_state": True
                    },
                    "security": {
                        "enable_encryption": True,
                        "auto_backup": True,
                        "log_level": "INFO"
                    }
                }

                # 如果配置文件不存在，创建默认配置
                default_config_file = self.config_dir / "default_config.json"
                if not default_config_file.exists():
                    with open(default_config_file, 'w', encoding='utf-8') as f:
                        json.dump(default_config, f, indent=4, ensure_ascii=False)

                # 如果用户配置文件不存在，创建空的用户配置
                if not self.user_config_file.exists():
                    user_config = {
                        "user_preferences": {
                            "first_run": True,
                            "last_update_check": "",
                            "custom_settings": {}
                        }
                    }
                    with open(self.user_config_file, 'w', encoding='utf-8') as f:
                        json.dump(user_config, f, indent=4, ensure_ascii=False)

        except Exception as e:
            print(f"配置文件初始化失败: {e}")
        
    def load_user_config(self) -> Dict[str, Any]:
        """加载用户配置"""
        # 首先尝试加载 default_config.json
        default_config = self.load_default_config()

        # 如果没有 default_config.json，使用硬编码默认值
        if not default_config:
            default_config = {
                "window": {
                    "width": 1400,
                    "height": 900,
                    "maximized": False,
                    "x": 100,
                    "y": 100
                },
                "theme": {
                    "name": "default",
                    "dark_mode": False
                },
                "general": {
                    "remember_window": True,
                    "startup_tab": 0,
                    "auto_save_settings": True
                },
                "appearance": {
                    "theme": "default"
                },
                "behavior": {
                    "minimize_to_tray": True,
                    "confirm_exit": False,
                    "tray_tip_shown": False
                },
                "paths": {
                    "last_archive_dir": "",
                    "last_source_dir": "",
                    "last_output_dir": "",
                    "last_checkout_dir": "",
                    "last_game_root": "",
                    "last_complete_dir": ""
                },
                "archive_processor": {
                    "dry_run": False,
                    "confirm_delete": False,
                    "backup_files": False,
                    "delete_invalid": False
                },
                "package_merger": {
                    "default_encoding": "gbk",
                    "backup_original": True
                },
                "svn_uploader": {
                    "remember_credentials": False,
                    "auto_compress": True
                }
            }
        
        if self.user_config_file.exists():
            try:
                with open(self.user_config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    # 合并默认配置和加载的配置
                    return self._merge_configs(default_config, loaded_config)
            except Exception as e:
                print(f"加载用户配置失败: {e}")
                
        return default_config

    def load_default_config(self) -> Dict[str, Any]:
        """加载默认配置文件"""
        default_config_file = self.config_dir / "default_config.json"
        if default_config_file.exists():
            try:
                with open(default_config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"加载默认配置失败: {e}")
        return {}

    def load_merge_rules(self) -> Optional[configparser.ConfigParser]:
        """加载合并规则配置"""
        merge_rules_file = self.config_dir / "merge_rules.ini"
        if merge_rules_file.exists():
            config = configparser.ConfigParser()
            try:
                config.read(merge_rules_file, encoding='utf-8')
                return config
            except Exception as e:
                print(f"加载合并规则配置失败: {e}")
        return None
        
    def load_xml_config(self) -> Dict[str, Any]:
        """加载XML配置"""
        xml_config_file = self.config_dir / "xml_config.json"
        if xml_config_file.exists():
            try:
                with open(xml_config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"加载XML配置失败: {e}")
        return {}
        
    def save_user_config(self):
        """保存用户配置"""
        try:
            with open(self.user_config_file, 'w', encoding='utf-8') as f:
                json.dump(self.user_config, f, indent=4, ensure_ascii=False)
        except Exception as e:
            print(f"保存用户配置失败: {e}")
            
    def get_config(self, key_path: str, default=None):
        """获取配置值
        
        Args:
            key_path: 配置键路径，如 "window.width"
            default: 默认值
        """
        keys = key_path.split('.')
        value = self.user_config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
            
    def set_config(self, key_path: str, value: Any):
        """设置配置值

        Args:
            key_path: 配置键路径，如 "window.width"
            value: 配置值
        """
        keys = key_path.split('.')
        config = self.user_config

        # 导航到父级字典
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]

        # 设置值
        config[keys[-1]] = value
        
    def get_merge_rule_files(self, rule_name: str) -> list:
        """获取指定规则的文件列表"""
        if not self.merge_rules or not self.merge_rules.has_section('Rules'):
            return []
            
        try:
            files_str = self.merge_rules.get('Rules', rule_name, fallback='')
            return [f.strip() for f in files_str.split(',') if f.strip()]
        except Exception:
            return []
            
    def get_encryption_files(self) -> list:
        """获取需要加密的文件列表"""
        if not self.merge_rules or not self.merge_rules.has_section('Encryption'):
            return []
            
        try:
            files_str = self.merge_rules.get('Encryption', 'required_files', fallback='')
            return [f.strip() for f in files_str.split(',') if f.strip()]
        except Exception:
            return []
            
    def _merge_configs(self, default: Dict, loaded: Dict) -> Dict:
        """递归合并配置字典"""
        result = default.copy()
        
        for key, value in loaded.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
                
        return result
        
    def reset_to_defaults(self):
        """重置为默认配置"""
        if self.user_config_file.exists():
            self.user_config_file.unlink()
        self.user_config = self.load_user_config()
        
    def export_config(self, file_path: str):
        """导出配置到文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.user_config, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"导出配置失败: {e}")
            return False
            
    def import_config(self, file_path: str):
        """从文件导入配置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
                self.user_config = self._merge_configs(self.user_config, imported_config)
                self.save_user_config()
            return True
        except Exception as e:
            print(f"导入配置失败: {e}")
            return False
