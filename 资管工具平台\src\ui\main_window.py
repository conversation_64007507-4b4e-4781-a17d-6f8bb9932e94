#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口界面
"""

from PyQt6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QTabWidget, QMenuBar, QStatusBar, QToolBar,
                            QLabel, QPushButton, QFrame, QSplitter, QApplication)
from PyQt6.QtCore import Qt, QSize, pyqtSignal
from PyQt6.QtGui import QIcon, QAction, QFont

from .archive_processor_widget import ArchiveProcessorWidget
from .package_merger_widget import PackageMergerWidget
from .svn_uploader_widget import SvnUploaderWidget
from .about_dialog import AboutDialog
from .settings_dialog import SettingsDialog
from .activation_dialog import ActivationDialog
from .license_status_widget import LicenseStatusWidget
from .license_dialog import LicenseDialog
from .update_dialog import UpdateDialog
from .system_tray import TrayManager
from utils.theme_manager import ThemeManager
from utils.config_manager import ConfigManager
from license.license_manager import LicenseManager
from updater.update_manager import UpdateManager


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()

        # 初始化管理器
        self.config_manager = ConfigManager()
        self.theme_manager = ThemeManager()
        self.license_manager = LicenseManager(config_manager=self.config_manager)
        self.update_manager = UpdateManager(self.license_manager, self.config_manager)
        self.tray_manager = TrayManager(self.license_manager, self)

        # 操作状态管理
        self.active_operations = {}  # 跟踪各个标签页的活跃操作

        self.init_ui()
        self.setup_connections()
        self.load_window_state()
        self.apply_saved_theme()
        self.check_license_status()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("资管工具平台 v1.0.1")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)
        
        # 设置窗口图标
        self.setWindowIcon(QIcon("resources/icons/app_icon.png"))
        
        # 创建中央部件
        self.setup_central_widget()
        
        # 创建菜单栏
        self.setup_menu_bar()
        
        # 注释掉工具栏，避免重复
        # self.setup_toolbar()
        
        # 创建状态栏
        self.setup_status_bar()
        
        # 应用样式
        self.apply_styles()
        
    def setup_central_widget(self):
        """设置中央部件"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 添加简化的标题
        title_label = QLabel("资管工具平台")
        title_label.setFont(QFont("Microsoft YaHei UI", 14, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("padding: 10px; background-color: #f8f9fa; border-bottom: 1px solid #dee2e6;")
        main_layout.addWidget(title_label)
        
        # 创建选项卡控件
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.TabPosition.North)
        self.tab_widget.setMovable(False)
        self.tab_widget.setTabsClosable(False)
        
        # 添加各个功能模块
        self.archive_widget = ArchiveProcessorWidget()
        self.merger_widget = PackageMergerWidget()
        self.uploader_widget = SvnUploaderWidget()
        
        self.tab_widget.addTab(self.archive_widget, "🗜️ 压缩包处理")
        self.tab_widget.addTab(self.merger_widget, "📦 整包工具")
        self.tab_widget.addTab(self.uploader_widget, "📤 载包上传")
        
        main_layout.addWidget(self.tab_widget)
        
    def create_title_frame(self):
        """创建标题区域"""
        frame = QFrame()
        frame.setFrameStyle(QFrame.Shape.StyledPanel)
        frame.setMaximumHeight(80)
        
        layout = QHBoxLayout(frame)
        layout.setContentsMargins(20, 10, 20, 10)
        
        # 应用图标
        icon_label = QLabel()
        icon_label.setPixmap(QIcon("resources/icons/app_icon.png").pixmap(48, 48))
        layout.addWidget(icon_label)
        
        # 标题和描述
        title_layout = QVBoxLayout()
        
        title_label = QLabel("资管工具平台")
        title_label.setFont(QFont("Microsoft YaHei UI", 16, QFont.Weight.Bold))
        title_layout.addWidget(title_label)
        
        desc_label = QLabel("集成压缩包处理、整包工具、载包上传等功能的统一桌面应用")
        desc_label.setFont(QFont("Microsoft YaHei UI", 9))
        desc_label.setStyleSheet("color: #666666;")
        title_layout.addWidget(desc_label)
        
        layout.addLayout(title_layout)
        layout.addStretch()
        
        return frame
        
    def setup_menu_bar(self):
        """设置菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")
        
        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 工具菜单
        tools_menu = menubar.addMenu("工具(&T)")

        settings_action = QAction("设置(&S)", self)
        settings_action.setShortcut("Ctrl+,")
        settings_action.triggered.connect(self.show_settings)
        tools_menu.addAction(settings_action)

        tools_menu.addSeparator()

        # 许可证菜单
        license_action = QAction("软件激活(&L)", self)
        license_action.setShortcut("Ctrl+L")
        license_action.triggered.connect(self.show_activation_dialog)
        tools_menu.addAction(license_action)

        license_status_action = QAction("许可证状态(&I)", self)
        license_status_action.triggered.connect(self.show_license_status)
        tools_menu.addAction(license_status_action)

        tools_menu.addSeparator()

        # 更新菜单
        check_update_action = QAction("检查更新(&U)", self)
        check_update_action.setShortcut("Ctrl+U")
        check_update_action.triggered.connect(self.check_for_updates)
        tools_menu.addAction(check_update_action)

        update_settings_action = QAction("更新设置(&P)", self)
        update_settings_action.triggered.connect(self.show_update_settings)
        tools_menu.addAction(update_settings_action)

        # 视图菜单
        view_menu = menubar.addMenu("视图(&V)")

        # 切换标签页的快捷键
        tab1_action = QAction("压缩包处理(&1)", self)
        tab1_action.setShortcut("Ctrl+1")
        tab1_action.triggered.connect(lambda: self.tab_widget.setCurrentIndex(0))
        view_menu.addAction(tab1_action)

        tab2_action = QAction("整包工具(&2)", self)
        tab2_action.setShortcut("Ctrl+2")
        tab2_action.triggered.connect(lambda: self.tab_widget.setCurrentIndex(1))
        view_menu.addAction(tab2_action)

        tab3_action = QAction("载包上传(&3)", self)
        tab3_action.setShortcut("Ctrl+3")
        tab3_action.triggered.connect(lambda: self.tab_widget.setCurrentIndex(2))
        view_menu.addAction(tab3_action)

        view_menu.addSeparator()

        # 刷新功能
        refresh_action = QAction("刷新(&R)", self)
        refresh_action.setShortcut("F5")
        refresh_action.triggered.connect(self.refresh_current_tab)
        view_menu.addAction(refresh_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")
        
        about_action = QAction("关于(&A)", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
        
    def setup_toolbar(self):
        """设置工具栏"""
        toolbar = QToolBar("主工具栏")
        toolbar.setToolButtonStyle(Qt.ToolButtonStyle.ToolButtonTextBesideIcon)
        self.addToolBar(toolbar)
        
        # 快速切换按钮
        archive_action = QAction(QIcon("resources/icons/archive.png"), "压缩包处理", self)
        archive_action.triggered.connect(lambda: self.tab_widget.setCurrentIndex(0))
        toolbar.addAction(archive_action)
        
        merger_action = QAction(QIcon("resources/icons/merge.png"), "整包工具", self)
        merger_action.triggered.connect(lambda: self.tab_widget.setCurrentIndex(1))
        toolbar.addAction(merger_action)
        
        uploader_action = QAction(QIcon("resources/icons/upload.png"), "载包上传", self)
        uploader_action.triggered.connect(lambda: self.tab_widget.setCurrentIndex(2))
        toolbar.addAction(uploader_action)
        
    def setup_status_bar(self):
        """设置状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_bar.addWidget(self.status_label)
        
        # 版本信息
        version_label = QLabel("v1.0.1")
        self.status_bar.addPermanentWidget(version_label)
        
    def setup_connections(self):
        """设置信号连接"""
        # 连接各个模块的状态信号
        self.archive_widget.status_changed.connect(self.update_status)
        self.merger_widget.status_changed.connect(self.update_status)
        self.uploader_widget.status_changed.connect(self.update_status)

        # 连接标签页切换信号
        self.tab_widget.currentChanged.connect(self.on_tab_changed)
        
    def apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QTabWidget::pane {
                border: 1px solid #c0c0c0;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #e1e1e1;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #0078d4;
            }
            QFrame {
                background-color: white;
                border-radius: 6px;
            }
        """)
        
    def update_status(self, message):
        """更新状态栏信息"""
        self.status_label.setText(message)
        
    def show_settings(self):
        """显示设置对话框"""
        dialog = SettingsDialog(self)
        dialog.theme_changed.connect(self.on_theme_changed)
        dialog.settings_applied.connect(self.on_settings_applied)
        dialog.exec()
        
    def show_about(self):
        """显示关于对话框"""
        dialog = AboutDialog(self)
        dialog.exec()
        
    def on_theme_changed(self, theme_name):
        """主题变更处理"""
        self.theme_manager.apply_theme(QApplication.instance(), theme_name)

    def on_settings_applied(self):
        """设置应用处理"""
        # 重新加载配置
        self.config_manager = ConfigManager()

        # 应用主题变更
        self.apply_saved_theme()

        # 应用启动标签页设置（下次启动生效）
        startup_tab = self.config_manager.get_config("general.startup_tab", 0)
        if 0 <= startup_tab < self.tab_widget.count():
            self.tab_widget.setCurrentIndex(startup_tab)

        self.update_status("设置已应用")

    def load_window_state(self):
        """加载窗口状态"""
        if self.config_manager.get_config("general.remember_window", True):
            # 加载窗口大小和位置
            width = self.config_manager.get_config("window.width", 1400)
            height = self.config_manager.get_config("window.height", 900)
            x = self.config_manager.get_config("window.x", 100)
            y = self.config_manager.get_config("window.y", 100)

            self.resize(width, height)
            self.move(x, y)

            # 加载最大化状态
            maximized = self.config_manager.get_config("window.maximized", False)
            if maximized:
                self.showMaximized()

            # 加载标签页状态
            startup_tab = self.config_manager.get_config("general.startup_tab", 0)
            if 0 <= startup_tab < self.tab_widget.count():
                self.tab_widget.setCurrentIndex(startup_tab)

    def save_window_state(self):
        """保存窗口状态"""
        if self.config_manager.get_config("general.remember_window", True):
            # 重新加载配置以确保获取最新状态（避免覆盖其他组件的设置）
            self.config_manager.user_config = self.config_manager.load_user_config()

            # 保存窗口大小和位置
            self.config_manager.set_config("window.width", self.width())
            self.config_manager.set_config("window.height", self.height())
            self.config_manager.set_config("window.x", self.x())
            self.config_manager.set_config("window.y", self.y())
            self.config_manager.set_config("window.maximized", self.isMaximized())

            # 保存当前标签页
            self.config_manager.set_config("general.startup_tab",
                                         self.tab_widget.currentIndex())

            # 保存配置到文件
            self.config_manager.save_user_config()

    def apply_saved_theme(self):
        """应用保存的主题"""
        # 优先使用 theme.name，如果没有则使用 appearance.theme
        theme = self.config_manager.get_config("theme.name",
                    self.config_manager.get_config("appearance.theme", "default"))
        self.theme_manager.apply_theme(QApplication.instance(), theme)

    def closeEvent(self, event):
        """窗口关闭事件"""
        # 如果系统托盘可用且设置了最小化到托盘
        if (self.tray_manager.is_available() and
            self.config_manager.get_config("behavior.minimize_to_tray", True)):

            # 隐藏窗口而不是关闭
            self.hide()

            # 显示托盘提示（仅第一次）
            if not self.config_manager.get_config("behavior.tray_tip_shown", False):
                self.tray_manager.show_message(
                    "资管工具平台",
                    "应用程序已最小化到系统托盘。\n双击托盘图标可重新打开窗口。"
                )
                self.config_manager.set_config("behavior.tray_tip_shown", True)
                self.config_manager.save_user_config()

            event.ignore()
            return

        # 检查是否需要确认退出
        if self.config_manager.get_config("behavior.confirm_exit", False):
            from PyQt6.QtWidgets import QMessageBox
            reply = QMessageBox.question(self, "确认退出",
                                       "确定要退出程序吗？",
                                       QMessageBox.StandardButton.Yes |
                                       QMessageBox.StandardButton.No)
            if reply == QMessageBox.StandardButton.No:
                event.ignore()
                return

        # 保存窗口状态和用户设置
        self.save_window_state()
        event.accept()

    def refresh_current_tab(self):
        """刷新当前标签页"""
        current_index = self.tab_widget.currentIndex()
        current_widget = self.tab_widget.currentWidget()

        if hasattr(current_widget, 'clear_log'):
            current_widget.clear_log()

        # 更新状态栏
        tab_names = ["压缩包处理", "整包工具", "载包上传"]
        if current_index < len(tab_names):
            self.status_bar.showMessage(f"已刷新 {tab_names[current_index]} 标签页", 2000)

    def keyPressEvent(self, event):
        """键盘按键事件"""
        # Ctrl+Tab 切换到下一个标签页
        if event.key() == Qt.Key.Key_Tab and event.modifiers() == Qt.KeyboardModifier.ControlModifier:
            current = self.tab_widget.currentIndex()
            next_index = (current + 1) % self.tab_widget.count()
            self.tab_widget.setCurrentIndex(next_index)
            event.accept()
            return

        # Ctrl+Shift+Tab 切换到上一个标签页
        if (event.key() == Qt.Key.Key_Tab and
            event.modifiers() == (Qt.KeyboardModifier.ControlModifier | Qt.KeyboardModifier.ShiftModifier)):
            current = self.tab_widget.currentIndex()
            prev_index = (current - 1) % self.tab_widget.count()
            self.tab_widget.setCurrentIndex(prev_index)
            event.accept()
            return

        # Escape 键关闭对话框或取消操作
        if event.key() == Qt.Key.Key_Escape:
            # 可以在这里添加取消当前操作的逻辑
            pass

        super().keyPressEvent(event)

    def check_license_status(self):
        """检查许可证状态"""
        # 检查功能权限并相应地启用/禁用功能
        self.update_feature_access()

        # 连接许可证管理器信号
        self.license_manager.license_expired.connect(self.on_license_expired)
        self.license_manager.license_warning.connect(self.on_license_warning)

        # 连接更新管理器信号
        self.update_manager.update_available.connect(self.on_update_available)
        self.update_manager.update_check_finished.connect(self.on_update_check_finished)

    def update_feature_access(self):
        """更新功能访问权限"""
        # 检查各个功能模块的访问权限
        archive_enabled = self.license_manager.is_feature_enabled("archive_processor")
        merger_enabled = self.license_manager.is_feature_enabled("package_merger")
        uploader_enabled = self.license_manager.is_feature_enabled("svn_uploader")

        # 根据许可证状态启用/禁用标签页
        self.tab_widget.setTabEnabled(0, archive_enabled)
        self.tab_widget.setTabEnabled(1, merger_enabled)
        self.tab_widget.setTabEnabled(2, uploader_enabled)

        # 如果当前标签页被禁用，切换到可用的标签页
        current_index = self.tab_widget.currentIndex()
        if not self.tab_widget.isTabEnabled(current_index):
            for i in range(self.tab_widget.count()):
                if self.tab_widget.isTabEnabled(i):
                    self.tab_widget.setCurrentIndex(i)
                    break

    def show_activation_dialog(self):
        """显示激活对话框"""
        dialog = ActivationDialog(self.license_manager, self)
        dialog.activation_success.connect(self.on_activation_success)
        dialog.exec()

    def show_license_status(self):
        """显示许可证管理对话框"""
        dialog = LicenseDialog(self.license_manager, self)
        dialog.license_updated.connect(self.on_activation_success)
        dialog.exec()

    def on_activation_success(self):
        """激活成功处理"""
        self.update_feature_access()
        self.status_bar.showMessage("软件激活成功！", 3000)

    def on_license_expired(self):
        """许可证过期处理"""
        from PyQt6.QtWidgets import QMessageBox

        QMessageBox.warning(self, "许可证过期",
                          "您的许可证已过期，部分功能将被限制。\n请联系我们续费或重新激活。")
        self.update_feature_access()

    def on_license_warning(self, days_left: int):
        """许可证警告处理"""
        from PyQt6.QtWidgets import QMessageBox

        QMessageBox.information(self, "许可证即将过期",
                              f"您的许可证将在 {days_left} 天后过期。\n请及时续费以避免功能受限。")

    def check_for_updates(self):
        """检查更新"""
        self.status_bar.showMessage("正在检查更新...", 2000)
        self.update_manager.check_for_updates(manual=True)

    def show_update_settings(self):
        """显示更新设置"""
        # 创建一个简单的更新设置对话框
        dialog = UpdateDialog(self.update_manager, parent=self)
        dialog.tab_widget.setCurrentIndex(1)  # 切换到设置标签页
        dialog.exec()

    def on_update_available(self, version_info):
        """发现新版本"""
        # 显示更新对话框
        dialog = UpdateDialog(self.update_manager, version_info, self)
        dialog.exec()

    def on_update_check_finished(self, result):
        """更新检查完成"""
        if result.error_message:
            # 不显示技术错误信息，显示友好提示
            self.status_bar.showMessage("更新检查完成", 3000)
        elif not result.has_update:
            self.status_bar.showMessage("当前已是最新版本", 3000)
        # 如果有更新，会通过 update_available 信号处理

    def on_tab_changed(self, index):
        """标签页切换处理"""
        # 检查是否有正在进行的操作
        current_tab_name = self.get_tab_name(index)
        active_operations = self.check_active_operations()

        if active_operations:
            # 显示警告但允许切换
            from PyQt6.QtWidgets import QMessageBox
            operations_text = "、".join(active_operations)
            reply = QMessageBox.question(
                self,
                "操作进行中",
                f"{operations_text}正在进行中，切换标签页不会中断操作，但可能影响进度显示。\n\n是否继续切换？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.No:
                # 阻止切换，返回到有活跃操作的标签页
                if "SVN操作" in active_operations:
                    self.tab_widget.setCurrentIndex(2)  # SVN上传器是第3个标签页
                elif "整包处理" in active_operations or "SVN更新清理" in active_operations:
                    self.tab_widget.setCurrentIndex(1)  # 整包工具是第2个标签页
                return
            else:
                # 用户选择继续，显示提示信息
                self.status_bar.showMessage(f"{operations_text}在后台继续进行...", 5000)

    def get_tab_name(self, index):
        """获取标签页名称"""
        tab_names = ["压缩包处理", "整包工具", "SVN上传"]
        return tab_names[index] if 0 <= index < len(tab_names) else "未知"

    def check_active_operations(self):
        """检查是否有活跃的操作"""
        active_ops = []

        # 检查SVN上传器
        if hasattr(self.uploader_widget, 'worker') and self.uploader_widget.worker:
            if self.uploader_widget.worker.isRunning():
                active_ops.append("SVN操作")

        # 检查整包工具
        if hasattr(self.merger_widget, 'is_operation_running') and self.merger_widget.is_operation_running():
            operation_type = self.merger_widget.get_current_operation()
            if operation_type:
                active_ops.append(operation_type)

        # 可以添加其他模块的检查
        # if hasattr(self.archive_widget, 'worker') and self.archive_widget.worker:
        #     if self.archive_widget.worker.isRunning():
        #         active_ops.append("压缩包处理")

        return active_ops
