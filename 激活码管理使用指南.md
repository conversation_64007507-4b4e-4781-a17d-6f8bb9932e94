# 🔑 激活码管理系统使用指南

## 🚀 快速开始

### 1. 启动服务端
```bash
cd 资管工具平台服务端
python simple_app.py
```

### 2. 访问管理界面
- **地址**: http://localhost:5000/admin
- **用户名**: admin
- **密码**: admin123

## 📊 管理界面功能

### 🏠 仪表板 (/)
- **统计概览**: 显示总激活码数、活跃数、已用数、过期数
- **最近活动**: 显示最新创建的10个激活码
- **快速导航**: 快速访问各个管理功能

### 🔑 激活码管理 (/admin/codes)

#### 查看激活码列表
- 显示所有激活码的详细信息
- 包含激活码、类型、设备数、时间、状态等
- 支持状态筛选和搜索

#### 生成新激活码
**参数说明**:
- **数量**: 一次生成1-10个激活码
- **类型**: 
  - `trial` - 试用版
  - `standard` - 标准版  
  - `professional` - 专业版
  - `enterprise` - 企业版
- **最大设备数**: 1-100台设备
- **有效期**: 1-3650天
- **备注**: 可选的说明信息

#### 状态管理
- **active** - 活跃状态，可以被激活
- **used** - 已使用，设备已激活
- **disabled** - 已禁用，无法使用
- **expired** - 已过期，自动禁用

### 🛡️ 活跃许可证 (/admin/licenses)
- 查看所有已激活的设备
- 显示机器ID、激活时间、最后检查时间
- 监控许可证使用情况

## 🔧 API接口使用

### 验证激活码
```http
POST /api/license/validate
Content-Type: application/json
X-API-Key: client-api-key-123

{
  "activation_code": "DEMO-1234-5678-ABCD",
  "machine_id": "ABC123DEF456",
  "machine_info": {
    "platform": "Windows-10",
    "processor": "Intel Core i7"
  }
}
```

**成功响应**:
```json
{
  "success": true,
  "message": "激活成功",
  "license_info": {
    "activation_code": "DEMO-1234-5678-ABCD",
    "license_type": "trial",
    "expires_at": "2024-09-03 23:59:59",
    "max_devices": 1,
    "current_devices": 1
  }
}
```

**失败响应**:
```json
{
  "success": false,
  "error": "invalid_code",
  "message": "激活码不存在"
}
```

### 检查许可证状态
```http
POST /api/license/status
Content-Type: application/json
X-API-Key: client-api-key-123

{
  "activation_code": "DEMO-1234-5678-ABCD",
  "machine_id": "ABC123DEF456"
}
```

## 💻 客户端集成

### Python客户端示例
```python
from license.license_validator import LicenseValidator

# 初始化验证器
validator = LicenseValidator(
    server_url="http://localhost:5000",
    api_key="client-api-key-123"
)

# 验证激活码
success, result = validator.validate_activation_code("DEMO-1234-5678-ABCD")
if success:
    print("✅ 激活成功!")
    license_info = result['license_info']
    print(f"许可证类型: {license_info['license_type']}")
    print(f"过期时间: {license_info['expires_at']}")
else:
    print(f"❌ 激活失败: {result['message']}")

# 检查状态
success, result = validator.check_license_status()
if success:
    print("✅ 许可证有效")
    if result.get('offline_mode'):
        print("📱 离线模式")
else:
    print(f"❌ 许可证无效: {result['message']}")
```

### 错误处理
```python
try:
    success, result = validator.validate_activation_code(code)
    if not success:
        error_code = result.get('error')
        if error_code == 'invalid_code':
            print("激活码不存在")
        elif error_code == 'code_expired':
            print("激活码已过期")
        elif error_code == 'device_limit_exceeded':
            print("设备数量超限")
        elif error_code == 'network_error':
            print("网络连接失败")
except Exception as e:
    print(f"验证过程出错: {e}")
```

## 🛡️ 安全特性

### 硬件绑定
- 基于多种硬件信息生成机器ID
- 包含平台、处理器、MAC地址等
- 防止激活码在不同机器上使用

### 设备限制
- 每个激活码可设置最大设备数
- 超过限制时自动拒绝新的激活
- 支持查看当前激活设备列表

### 时效控制
- 支持设置激活码有效期
- 过期后自动禁用
- 管理员可手动延长有效期

### API安全
- 使用API密钥验证请求
- 支持请求频率限制
- 记录所有验证操作日志

## 📋 预置激活码

系统已包含3个示例激活码供测试：

| 激活码 | 类型 | 设备数 | 有效期 | 说明 |
|--------|------|--------|--------|------|
| DEMO-1234-5678-ABCD | trial | 1 | 30天 | 试用版 |
| FULL-ABCD-EFGH-1234 | professional | 3 | 1年 | 专业版 |
| ENTP-5678-9012-WXYZ | enterprise | 10 | 1年 | 企业版 |

## 🔧 常见问题

### Q: 激活码验证失败怎么办？
A: 检查以下几点：
1. 激活码是否正确
2. 激活码是否已过期
3. 设备数量是否超限
4. 网络连接是否正常
5. API密钥是否正确

### Q: 如何重置管理员密码？
A: 修改 `simple_app.py` 中的登录验证逻辑：
```python
if username == 'admin' and password == 'your-new-password':
```

### Q: 如何修改API密钥？
A: 修改 `simple_app.py` 中的API密钥列表：
```python
if api_key not in ['your-new-client-key', 'your-new-admin-key']:
```

### Q: 如何备份激活码数据？
A: 复制数据库文件：
```bash
cp data/database/license.db license_backup.db
```

### Q: 如何部署到生产环境？
A: 
1. 使用 gunicorn 替代开发服务器
2. 配置 HTTPS 和防火墙
3. 修改默认密码和API密钥
4. 设置数据库备份策略

## 🚀 高级功能

### 批量操作
- 支持批量生成激活码
- 支持批量修改状态
- 支持导出激活码列表

### 监控统计
- 实时查看激活码使用情况
- 监控设备激活状态
- 查看验证操作日志

### 扩展开发
- 支持自定义许可证类型
- 支持自定义验证规则
- 支持集成第三方认证

---

## ✅ 系统已就绪

激活码管理系统现在完全可用，包括：
- ✅ 完整的Web管理界面
- ✅ 强大的API接口
- ✅ 安全的验证机制
- ✅ 灵活的配置选项

现在您可以：
1. 🌐 通过Web界面管理激活码
2. 🔑 生成和分发激活码给用户
3. 📊 监控许可证使用情况
4. 🛡️ 保护软件免受盗版
