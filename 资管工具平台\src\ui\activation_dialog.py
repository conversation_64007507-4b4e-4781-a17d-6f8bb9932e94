#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
激活对话框
"""

import os
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTabWidget,
                            QWidget, QLabel, QLineEdit, QPushButton, QTextEdit,
                            QGroupBox, QGridLayout, QMessageBox, QProgressBar,
                            QFrame, QSpacerItem, QSizePolicy)
from PyQt6.QtCore import Qt, pyqtSignal, QThread, QTimer
from PyQt6.QtGui import QFont, QPixmap, QIcon

from license.license_manager import LicenseManager
from license.license_types import LicenseType, LicenseFeatures


class ActivationWorker(QThread):
    """激活工作线程"""
    
    activation_finished = pyqtSignal(bool, str)  # 激活完成信号
    
    def __init__(self, license_manager: LicenseManager, activation_code: str, user_info: dict):
        super().__init__()
        self.license_manager = license_manager
        self.activation_code = activation_code
        self.user_info = user_info
    
    def run(self):
        """执行激活"""
        try:
            success, message = self.license_manager.activate_license(self.activation_code, self.user_info)
            self.activation_finished.emit(success, message)
        except Exception as e:
            self.activation_finished.emit(False, f"激活过程出错: {str(e)}")


class ActivationDialog(QDialog):
    """激活对话框"""
    
    activation_success = pyqtSignal()  # 激活成功信号
    
    def __init__(self, license_manager: LicenseManager, parent=None):
        super().__init__(parent)
        self.license_manager = license_manager
        self.worker = None
        self.init_ui()
        self.setup_connections()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("软件激活")
        self.setModal(True)
        self.setFixedSize(650, 750)  # 增加窗口大小
        
        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(30, 30, 30, 30)  # 增加边距
        layout.setSpacing(25)  # 增加间距
        
        # 创建标题区域
        self.create_header(layout)
        
        # 创建标签页
        self.create_tabs(layout)
        
        # 创建按钮区域
        self.create_buttons(layout)
    
    def create_header(self, layout):
        """创建标题区域"""
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(15, 15, 15, 15)
        
        # 应用图标
        icon_label = QLabel()
        try:
            icon_label.setPixmap(QIcon("resources/icons/app_icon.png").pixmap(48, 48))
        except:
            icon_label.setText("🔐")
            icon_label.setFont(QFont("Arial", 24))
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(icon_label)
        
        # 标题信息
        title_layout = QVBoxLayout()
        
        title_label = QLabel("资管工具平台激活")
        title_label.setFont(QFont("Microsoft YaHei UI", 20, QFont.Weight.Bold))  # 增加字体大小
        title_label.setStyleSheet("margin: 5px 0px;")  # 增加上下边距
        title_layout.addWidget(title_label)

        subtitle_label = QLabel("请输入激活码以解锁完整功能")
        subtitle_label.setFont(QFont("Microsoft YaHei UI", 12))  # 增加字体大小
        subtitle_label.setStyleSheet("color: #666666; margin-bottom: 10px;")  # 增加下边距
        title_layout.addWidget(subtitle_label)
        
        header_layout.addLayout(title_layout)
        header_layout.addStretch()
        
        layout.addWidget(header_frame)
    
    def create_tabs(self, layout):
        """创建标签页"""
        self.tab_widget = QTabWidget()
        self.tab_widget.setFont(QFont("Microsoft YaHei UI", 11))  # 增加标签页字体大小
        
        # 激活标签页
        self.create_activation_tab()
        
        # 试用标签页
        self.create_trial_tab()
        
        # 许可证信息标签页
        self.create_license_info_tab()
        
        layout.addWidget(self.tab_widget)
    
    def create_activation_tab(self):
        """创建激活标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(20)  # 增加间距

        # 激活码输入组
        activation_group = QGroupBox("激活码")
        activation_group.setFont(QFont("Microsoft YaHei UI", 12))  # 增加组标题字体
        activation_layout = QVBoxLayout(activation_group)
        activation_layout.setSpacing(12)  # 增加内部间距
        
        # 激活码输入框
        self.activation_code_entry = QLineEdit()
        self.activation_code_entry.setPlaceholderText("请输入激活码 (格式: XXXX-XXXX-XXXX-XXXX-XXXX-XXXX-XX)")
        self.activation_code_entry.setFont(QFont("Consolas", 12))  # 增加字体大小
        self.activation_code_entry.setMinimumHeight(35)  # 增加输入框高度
        activation_layout.addWidget(self.activation_code_entry)

        # 格式提示
        format_label = QLabel("激活码格式: XXXX-XXXX-XXXX-XXXX-XXXX-XXXX-XX")
        format_label.setStyleSheet("color: #888888; font-size: 10pt; margin-top: 5px;")  # 增加字体大小
        activation_layout.addWidget(format_label)
        
        layout.addWidget(activation_group)
        
        # 用户信息组
        user_group = QGroupBox("用户信息 (可选)")
        user_group.setFont(QFont("Microsoft YaHei UI", 12))  # 增加组标题字体
        user_layout = QGridLayout(user_group)
        user_layout.setSpacing(12)  # 增加间距

        # 创建标签并设置字体
        user_name_label = QLabel("用户名:")
        user_name_label.setFont(QFont("Microsoft YaHei UI", 11))
        user_layout.addWidget(user_name_label, 0, 0)
        self.user_name_entry = QLineEdit()
        self.user_name_entry.setFont(QFont("Microsoft YaHei UI", 11))
        self.user_name_entry.setMinimumHeight(30)
        user_layout.addWidget(self.user_name_entry, 0, 1)

        company_label = QLabel("公司名称:")
        company_label.setFont(QFont("Microsoft YaHei UI", 11))
        user_layout.addWidget(company_label, 1, 0)
        self.company_name_entry = QLineEdit()
        self.company_name_entry.setFont(QFont("Microsoft YaHei UI", 11))
        self.company_name_entry.setMinimumHeight(30)
        user_layout.addWidget(self.company_name_entry, 1, 1)

        email_label = QLabel("邮箱:")
        email_label.setFont(QFont("Microsoft YaHei UI", 11))
        user_layout.addWidget(email_label, 2, 0)
        self.email_entry = QLineEdit()
        self.email_entry.setFont(QFont("Microsoft YaHei UI", 11))
        self.email_entry.setMinimumHeight(30)
        user_layout.addWidget(self.email_entry, 2, 1)
        
        layout.addWidget(user_group)
        
        # 硬件信息
        hardware_group = QGroupBox("硬件信息")
        hardware_group.setFont(QFont("Microsoft YaHei UI", 12))  # 增加组标题字体
        hardware_layout = QVBoxLayout(hardware_group)
        hardware_layout.setSpacing(8)

        hardware_id_label = QLabel(f"硬件ID: {self.license_manager.hardware_id}")
        hardware_id_label.setFont(QFont("Consolas", 10))  # 增加字体大小
        hardware_id_label.setStyleSheet("color: #666666; padding: 5px;")
        hardware_layout.addWidget(hardware_id_label)

        hardware_note = QLabel("注意: 激活码将绑定到此设备")
        hardware_note.setStyleSheet("color: #888888; font-size: 10pt; padding: 2px;")  # 增加字体大小
        hardware_layout.addWidget(hardware_note)
        
        layout.addWidget(hardware_group)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        layout.addStretch()
        self.tab_widget.addTab(widget, "激活")
    
    def create_trial_tab(self):
        """创建试用标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(20)  # 增加间距

        # 试用说明
        trial_info = QGroupBox("试用版说明")
        trial_info.setFont(QFont("Microsoft YaHei UI", 12))  # 增加组标题字体
        trial_layout = QVBoxLayout(trial_info)

        info_text = QTextEdit()
        info_text.setReadOnly(True)
        info_text.setMaximumHeight(180)  # 增加高度
        info_text.setFont(QFont("Microsoft YaHei UI", 11))  # 增加字体大小
        info_text.setHtml("""
        <h3 style="color: #2c5aa0;">试用版功能</h3>
        <ul style="line-height: 1.6;">
        <li>30天免费试用期</li>
        <li>完整功能体验</li>
        <li>所有工具模块</li>
        <li>多种界面主题</li>
        </ul>
        <p style="color: #d63031;"><b>注意:</b> 每台设备只能试用一次</p>
        """)
        trial_layout.addWidget(info_text)
        
        layout.addWidget(trial_info)
        
        # 试用用户信息
        trial_user_group = QGroupBox("试用用户信息")
        trial_user_group.setFont(QFont("Microsoft YaHei UI", 12))  # 增加组标题字体
        trial_user_layout = QGridLayout(trial_user_group)
        trial_user_layout.setSpacing(12)  # 增加间距

        name_label = QLabel("姓名:")
        name_label.setFont(QFont("Microsoft YaHei UI", 11))
        trial_user_layout.addWidget(name_label, 0, 0)
        self.trial_user_name_entry = QLineEdit()
        self.trial_user_name_entry.setFont(QFont("Microsoft YaHei UI", 11))
        self.trial_user_name_entry.setMinimumHeight(30)
        trial_user_layout.addWidget(self.trial_user_name_entry, 0, 1)

        email_label = QLabel("邮箱:")
        email_label.setFont(QFont("Microsoft YaHei UI", 11))
        trial_user_layout.addWidget(email_label, 1, 0)
        self.trial_email_entry = QLineEdit()
        self.trial_email_entry.setFont(QFont("Microsoft YaHei UI", 11))
        self.trial_email_entry.setMinimumHeight(30)
        trial_user_layout.addWidget(self.trial_email_entry, 1, 1)

        layout.addWidget(trial_user_group)

        # 试用按钮
        self.trial_button = QPushButton("开始试用")
        self.trial_button.setMinimumHeight(45)  # 增加按钮高度
        self.trial_button.setFont(QFont("Microsoft YaHei UI", 12))  # 增加字体大小
        layout.addWidget(self.trial_button)
        
        layout.addStretch()
        self.tab_widget.addTab(widget, "试用")
    
    def create_license_info_tab(self):
        """创建许可证信息标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        
        # 版本对比
        comparison_group = QGroupBox("版本对比")
        comparison_layout = QVBoxLayout(comparison_group)
        
        comparison_text = QTextEdit()
        comparison_text.setReadOnly(True)
        comparison_text.setHtml(self._get_version_comparison_html())
        comparison_layout.addWidget(comparison_text)
        
        layout.addWidget(comparison_group)
        
        # 购买信息
        purchase_group = QGroupBox("购买信息")
        purchase_layout = QVBoxLayout(purchase_group)
        
        purchase_label = QLabel("如需购买正式版本，请联系:")
        purchase_layout.addWidget(purchase_label)
        
        contact_label = QLabel("邮箱: <EMAIL>\n电话: 400-123-4567")
        contact_label.setStyleSheet("color: #0066cc; font-weight: bold;")
        purchase_layout.addWidget(contact_label)
        
        layout.addWidget(purchase_group)
        
        layout.addStretch()
        self.tab_widget.addTab(widget, "版本信息")
    
    def create_buttons(self, layout):
        """创建按钮区域"""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)  # 增加按钮间距
        button_layout.addStretch()

        self.activate_button = QPushButton("激活")
        self.activate_button.setMinimumSize(120, 40)  # 增加按钮大小
        self.activate_button.setFont(QFont("Microsoft YaHei UI", 12))  # 增加字体大小
        self.activate_button.setDefault(True)
        button_layout.addWidget(self.activate_button)

        self.cancel_button = QPushButton("取消")
        self.cancel_button.setMinimumSize(120, 40)  # 增加按钮大小
        self.cancel_button.setFont(QFont("Microsoft YaHei UI", 12))  # 增加字体大小
        button_layout.addWidget(self.cancel_button)

        layout.addLayout(button_layout)
    
    def setup_connections(self):
        """设置信号连接"""
        self.activate_button.clicked.connect(self.activate_license)
        self.trial_button.clicked.connect(self.start_trial)
        self.cancel_button.clicked.connect(self.reject)
        
        # 激活码输入格式化
        self.activation_code_entry.textChanged.connect(self.format_activation_code)
    
    def format_activation_code(self, text):
        """格式化激活码输入"""
        # 移除所有非字母数字字符
        clean_text = ''.join(c for c in text.upper() if c.isalnum())
        
        # 添加分隔符
        formatted = ''
        for i, char in enumerate(clean_text):
            if i > 0 and i % 4 == 0:
                formatted += '-'
            formatted += char
        
        # 限制长度（26个字符 + 6个分隔符 = 32个字符）
        if len(formatted) > 32:
            formatted = formatted[:32]
        
        # 更新输入框
        if formatted != text:
            cursor_pos = self.activation_code_entry.cursorPosition()
            self.activation_code_entry.setText(formatted)
            self.activation_code_entry.setCursorPosition(min(cursor_pos, len(formatted)))
    
    def activate_license(self):
        """激活许可证"""
        activation_code = self.activation_code_entry.text().strip()
        
        if not activation_code:
            QMessageBox.warning(self, "警告", "请输入激活码")
            return
        
        # 收集用户信息
        user_info = {
            'user_name': self.user_name_entry.text().strip(),
            'company_name': self.company_name_entry.text().strip(),
            'email': self.email_entry.text().strip()
        }
        
        # 显示进度
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 无限进度条
        self.activate_button.setEnabled(False)
        
        # 启动激活线程
        self.worker = ActivationWorker(self.license_manager, activation_code, user_info)
        self.worker.activation_finished.connect(self.on_activation_finished)
        self.worker.start()
    
    def start_trial(self):
        """开始试用"""
        user_info = {
            'user_name': self.trial_user_name_entry.text().strip(),
            'email': self.trial_email_entry.text().strip()
        }
        
        success, message = self.license_manager.start_trial(user_info)
        
        if success:
            QMessageBox.information(self, "成功", message)
            self.activation_success.emit()
            self.accept()
        else:
            QMessageBox.warning(self, "失败", message)
    
    def on_activation_finished(self, success: bool, message: str):
        """激活完成处理"""
        self.progress_bar.setVisible(False)
        self.activate_button.setEnabled(True)
        
        if success:
            QMessageBox.information(self, "成功", message)
            self.activation_success.emit()
            self.accept()
        else:
            QMessageBox.warning(self, "失败", message)
    
    def _get_version_comparison_html(self) -> str:
        """获取版本对比HTML"""
        return """
        <table border="1" cellpadding="5" cellspacing="0" style="width: 100%;">
        <tr style="background-color: #f0f0f0;">
            <th>功能</th>
            <th>免费版</th>
            <th>试用版</th>
            <th>专业版</th>
            <th>企业版</th>
        </tr>
        <tr>
            <td>压缩包处理</td>
            <td>✓ (限制)</td>
            <td>✓</td>
            <td>✓</td>
            <td>✓</td>
        </tr>
        <tr>
            <td>整包工具</td>
            <td>✗</td>
            <td>✓</td>
            <td>✓</td>
            <td>✓</td>
        </tr>
        <tr>
            <td>SVN上传</td>
            <td>✗</td>
            <td>✓</td>
            <td>✓</td>
            <td>✓</td>
        </tr>
        <tr>
            <td>主题切换</td>
            <td>✗</td>
            <td>✓</td>
            <td>✓</td>
            <td>✓</td>
        </tr>
        <tr>
            <td>自动更新</td>
            <td>✗</td>
            <td>✗</td>
            <td>✓</td>
            <td>✓</td>
        </tr>
        <tr>
            <td>技术支持</td>
            <td>✗</td>
            <td>✗</td>
            <td>✓</td>
            <td>✓</td>
        </tr>
        <tr>
            <td>自定义规则</td>
            <td>✗</td>
            <td>✗</td>
            <td>✗</td>
            <td>✓</td>
        </tr>
        <tr>
            <td>使用期限</td>
            <td>永久</td>
            <td>30天</td>
            <td>1年</td>
            <td>1年</td>
        </tr>
        </table>
        """
