#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统托盘功能
"""

from PyQt6.QtWidgets import (QSystemTrayIcon, QMenu, QApplication, 
                            QMessageBox, QWidget)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QIcon, QPixmap, QAction

from license.license_manager import LicenseManager
from license.license_types import LicenseStatus, LicenseFeatures


class SystemTrayIcon(QSystemTrayIcon):
    """系统托盘图标"""
    
    # 信号定义
    show_main_window = pyqtSignal()
    show_activation_dialog = pyqtSignal()
    show_license_dialog = pyqtSignal()
    show_update_dialog = pyqtSignal()
    quit_application = pyqtSignal()
    
    def __init__(self, license_manager: LicenseManager, parent: QWidget = None):
        super().__init__(parent)
        self.license_manager = license_manager
        self.parent_widget = parent
        
        self.init_ui()
        self.setup_connections()
        self.update_icon_and_tooltip()
        
        # 定时更新状态
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_icon_and_tooltip)
        self.update_timer.start(60000)  # 每分钟更新一次
    
    def init_ui(self):
        """初始化用户界面"""
        # 设置图标
        self.setIcon(self._get_status_icon())
        
        # 创建右键菜单
        self.create_context_menu()
        
        # 显示托盘图标
        self.show()
    
    def create_context_menu(self):
        """创建右键菜单"""
        menu = QMenu()
        
        # 显示主窗口
        show_action = QAction("显示主窗口", self)
        show_action.triggered.connect(self.show_main_window.emit)
        menu.addAction(show_action)
        
        menu.addSeparator()
        
        # 许可证相关
        license_menu = menu.addMenu("许可证")
        
        activate_action = QAction("激活软件", self)
        activate_action.triggered.connect(self.show_activation_dialog.emit)
        license_menu.addAction(activate_action)
        
        license_status_action = QAction("许可证状态", self)
        license_status_action.triggered.connect(self.show_license_dialog.emit)
        license_menu.addAction(license_status_action)
        
        # 更新相关
        update_menu = menu.addMenu("更新")
        
        check_update_action = QAction("检查更新", self)
        check_update_action.triggered.connect(self.show_update_dialog.emit)
        update_menu.addAction(check_update_action)
        
        menu.addSeparator()
        
        # 退出
        quit_action = QAction("退出", self)
        quit_action.triggered.connect(self.quit_application.emit)
        menu.addAction(quit_action)
        
        self.setContextMenu(menu)
    
    def setup_connections(self):
        """设置信号连接"""
        # 双击托盘图标显示主窗口
        self.activated.connect(self.on_tray_activated)
        
        # 连接许可证管理器信号
        self.license_manager.license_changed.connect(self.on_license_changed)
        self.license_manager.license_expired.connect(self.on_license_expired)
        self.license_manager.license_warning.connect(self.on_license_warning)
    
    def on_tray_activated(self, reason):
        """托盘图标激活处理"""
        if reason == QSystemTrayIcon.ActivationReason.DoubleClick:
            self.show_main_window.emit()
    
    def update_icon_and_tooltip(self):
        """更新图标和工具提示"""
        # 更新图标
        self.setIcon(self._get_status_icon())
        
        # 更新工具提示
        tooltip = self._get_status_tooltip()
        self.setToolTip(tooltip)
    
    def _get_status_icon(self) -> QIcon:
        """获取状态图标"""
        try:
            # 尝试加载应用程序图标
            icon = QIcon("resources/icons/app_icon.png")
            if not icon.isNull():
                return icon
        except:
            pass
        
        # 如果无法加载图标，创建一个简单的图标
        pixmap = QPixmap(16, 16)
        
        # 根据许可证状态设置颜色
        status = self.license_manager.get_license_status()
        if status == LicenseStatus.VALID:
            pixmap.fill(Qt.GlobalColor.green)
        elif status == LicenseStatus.EXPIRED:
            pixmap.fill(Qt.GlobalColor.red)
        elif status == LicenseStatus.TRIAL_EXPIRED:
            pixmap.fill(Qt.GlobalColor.yellow)
        else:
            pixmap.fill(Qt.GlobalColor.gray)
        
        return QIcon(pixmap)
    
    def _get_status_tooltip(self) -> str:
        """获取状态工具提示"""
        license_info = self.license_manager.current_license
        if not license_info:
            return "资管工具平台 - 未激活"
        
        # 许可证类型
        license_type_name = LicenseFeatures.get_license_display_name(license_info.license_type)
        
        # 状态
        status = self.license_manager.get_license_status()
        status_text = {
            LicenseStatus.VALID: "正常",
            LicenseStatus.EXPIRED: "已过期",
            LicenseStatus.TRIAL_EXPIRED: "试用已过期",
            LicenseStatus.INVALID: "无效",
            LicenseStatus.NOT_ACTIVATED: "未激活"
        }.get(status, "未知")
        
        # 到期信息
        days_left = self.license_manager.get_days_until_expiry()
        if days_left is not None:
            if days_left > 0:
                expiry_text = f"剩余 {days_left} 天"
            else:
                expiry_text = "已过期"
        else:
            expiry_text = "永久有效"
        
        return f"资管工具平台\n许可证: {license_type_name}\n状态: {status_text}\n{expiry_text}"
    
    def show_message(self, title: str, message: str, icon=None):
        """显示托盘消息"""
        if icon is None:
            icon = QSystemTrayIcon.MessageIcon.Information
        
        self.showMessage(title, message, icon, 5000)
    
    def on_license_changed(self, license_info):
        """许可证变更处理"""
        self.update_icon_and_tooltip()
        
        # 显示通知
        license_type_name = LicenseFeatures.get_license_display_name(license_info.license_type)
        self.show_message(
            "许可证更新",
            f"许可证已更新为: {license_type_name}",
            QSystemTrayIcon.MessageIcon.Information
        )
    
    def on_license_expired(self):
        """许可证过期处理"""
        self.update_icon_and_tooltip()
        
        # 显示警告通知
        self.show_message(
            "许可证过期",
            "您的许可证已过期，部分功能将被限制。\n请及时续费或重新激活。",
            QSystemTrayIcon.MessageIcon.Warning
        )
    
    def on_license_warning(self, days_left: int):
        """许可证警告处理"""
        self.update_icon_and_tooltip()
        
        # 显示提醒通知
        self.show_message(
            "许可证即将过期",
            f"您的许可证将在 {days_left} 天后过期。\n请及时续费以避免功能受限。",
            QSystemTrayIcon.MessageIcon.Warning
        )
    
    def check_system_tray_available(self) -> bool:
        """检查系统托盘是否可用"""
        return QSystemTrayIcon.isSystemTrayAvailable()
    
    def hide_tray(self):
        """隐藏托盘图标"""
        self.hide()
    
    def show_tray(self):
        """显示托盘图标"""
        if self.check_system_tray_available():
            self.show()
        else:
            QMessageBox.critical(
                self.parent_widget,
                "系统托盘不可用",
                "系统托盘功能在此系统上不可用。"
            )


class TrayManager:
    """托盘管理器"""
    
    def __init__(self, license_manager: LicenseManager, main_window=None):
        self.license_manager = license_manager
        self.main_window = main_window
        self.tray_icon = None
        
        # 检查系统托盘是否可用
        if QSystemTrayIcon.isSystemTrayAvailable():
            self.create_tray_icon()
    
    def create_tray_icon(self):
        """创建托盘图标"""
        self.tray_icon = SystemTrayIcon(self.license_manager, self.main_window)
        
        # 连接信号
        if self.main_window:
            self.tray_icon.show_main_window.connect(self.show_main_window)
            self.tray_icon.show_activation_dialog.connect(self.show_activation_dialog)
            self.tray_icon.show_license_dialog.connect(self.show_license_dialog)
            self.tray_icon.show_update_dialog.connect(self.show_update_dialog)
            self.tray_icon.quit_application.connect(self.quit_application)
    
    def show_main_window(self):
        """显示主窗口"""
        if self.main_window:
            self.main_window.show()
            self.main_window.raise_()
            self.main_window.activateWindow()
    
    def show_activation_dialog(self):
        """显示激活对话框"""
        if self.main_window:
            self.main_window.show_activation_dialog()
    
    def show_license_dialog(self):
        """显示许可证对话框"""
        if self.main_window:
            self.main_window.show_license_status()
    
    def show_update_dialog(self):
        """显示更新对话框"""
        if self.main_window:
            self.main_window.check_for_updates()
    
    def quit_application(self):
        """退出应用程序"""
        QApplication.instance().quit()
    
    def is_available(self) -> bool:
        """检查托盘是否可用"""
        return self.tray_icon is not None
    
    def show_message(self, title: str, message: str, icon=None):
        """显示托盘消息"""
        if self.tray_icon:
            self.tray_icon.show_message(title, message, icon)
    
    def hide_tray(self):
        """隐藏托盘"""
        if self.tray_icon:
            self.tray_icon.hide_tray()
    
    def show_tray(self):
        """显示托盘"""
        if self.tray_icon:
            self.tray_icon.show_tray()
