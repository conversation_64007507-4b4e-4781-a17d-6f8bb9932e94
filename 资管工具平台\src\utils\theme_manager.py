#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主题管理器
"""

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QObject, pyqtSignal
from pathlib import Path


class ThemeManager(QObject):
    """主题管理器类"""
    
    theme_changed = pyqtSignal(str)  # 主题变更信号
    
    def __init__(self):
        super().__init__()
        self.current_theme = "default"
        self.themes_dir = Path(__file__).parent.parent.parent / "resources" / "themes"
        
    def get_available_themes(self):
        """获取可用主题列表"""
        return ["default", "dark", "blue"]
        
    def apply_theme(self, app: QApplication, theme_name: str = "default"):
        """应用主题"""
        self.current_theme = theme_name
        
        if theme_name == "dark":
            style = self._get_dark_theme()
        elif theme_name == "blue":
            style = self._get_blue_theme()
        else:
            style = self._get_default_theme()
            
        app.setStyleSheet(style)
        self.theme_changed.emit(theme_name)
        
    def _get_default_theme(self):
        """获取默认主题样式 - 现代清新风格"""
        return """
        /* 现代清新主题 */
        QMainWindow {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #f8fafc, stop:1 #e2e8f0);
            color: #1e293b;
        }

        QWidget {
            background-color: rgba(255, 255, 255, 0.95);
            color: #1e293b;
            font-family: "Microsoft YaHei UI", "Segoe UI", "SF Pro Display", Arial, sans-serif;
            font-size: 9pt;
        }

        /* 按钮样式 - 现代渐变 */
        QPushButton {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #3b82f6, stop:1 #2563eb);
            color: white;
            border: 2px solid rgba(59, 130, 246, 0.3);
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 9pt;
            min-width: 90px;
        }

        QPushButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #60a5fa, stop:1 #3b82f6);
            border: 2px solid #60a5fa;
        }

        QPushButton:pressed {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #1d4ed8, stop:1 #1e40af);
            border: 2px solid #1d4ed8;
        }

        QPushButton:disabled {
            background: #e2e8f0;
            color: #94a3b8;
            border: 2px solid #cbd5e1;
        }

        /* 输入框样式 - 现代边框 */
        QLineEdit, QTextEdit, QPlainTextEdit {
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.9);
            selection-background-color: #3b82f6;
            font-size: 9pt;
        }

        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
            border: 2px solid #3b82f6;
            background: rgba(255, 255, 255, 1);
        }

        /* 选项卡样式 - 现代卡片 */
        QTabWidget::pane {
            border: 2px solid #e2e8f0;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            margin-top: 8px;
        }

        QTabBar::tab {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(241, 245, 249, 0.9), stop:1 rgba(226, 232, 240, 0.9));
            color: #64748b;
            padding: 12px 24px;
            margin-right: 4px;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            border: 2px solid #e2e8f0;
            border-bottom: none;
            font-weight: 500;
            font-size: 9pt;
        }

        QTabBar::tab:selected {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(255, 255, 255, 1), stop:1 rgba(248, 250, 252, 1));
            color: #3b82f6;
            font-weight: 700;
            border-bottom: 3px solid #3b82f6;
        }

        QTabBar::tab:hover:!selected {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(226, 232, 240, 0.9), stop:1 rgba(203, 213, 224, 0.9));
            color: #1e293b;
        }
        
        /* 框架样式 - 现代卡片 */
        QFrame {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(255, 255, 255, 0.95), stop:1 rgba(248, 250, 252, 0.95));
            border: 2px solid rgba(59, 130, 246, 0.1);
            border-radius: 12px;
        }

        /* 分组框样式 - 现代边框 */
        QGroupBox {
            font-weight: 700;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            margin-top: 15px;
            padding-top: 15px;
            color: #1e293b;
            font-size: 10pt;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 15px;
            padding: 0 10px 0 10px;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(255, 255, 255, 1), stop:1 rgba(248, 250, 252, 1));
            color: #3b82f6;
        }

        /* 列表样式 - 现代列表 */
        QListWidget, QTreeWidget, QTableWidget {
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.95);
            alternate-background-color: rgba(248, 250, 252, 0.8);
            gridline-color: #f1f5f9;
        }

        QListWidget::item, QTreeWidget::item, QTableWidget::item {
            padding: 8px;
            border-bottom: 1px solid #f1f5f9;
            border-radius: 4px;
        }

        QListWidget::item:selected, QTreeWidget::item:selected, QTableWidget::item:selected {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #3b82f6, stop:1 #2563eb);
            color: white;
        }

        QListWidget::item:hover, QTreeWidget::item:hover, QTableWidget::item:hover {
            background: rgba(59, 130, 246, 0.1);
            color: #1e293b;
        }

        /* 滚动条样式 - 现代滚动条 */
        QScrollBar:vertical {
            background: rgba(226, 232, 240, 0.5);
            width: 14px;
            border-radius: 7px;
            margin: 0;
        }

        QScrollBar::handle:vertical {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #cbd5e1, stop:1 #94a3b8);
            border-radius: 7px;
            min-height: 30px;
        }

        QScrollBar::handle:vertical:hover {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #94a3b8, stop:1 #64748b);
        }

        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
            height: 0px;
        }

        /* 进度条样式 - 现代渐变 */
        QProgressBar {
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            text-align: center;
            background: rgba(241, 245, 249, 0.8);
            color: #1e293b;
            font-weight: 600;
            font-size: 9pt;
            height: 24px;
        }

        QProgressBar::chunk {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #3b82f6, stop:0.5 #60a5fa, stop:1 #3b82f6);
            border-radius: 8px;
        }

        /* 状态栏样式 - 现代状态栏 */
        QStatusBar {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #f8fafc, stop:1 #f1f5f9);
            border-top: 2px solid #e2e8f0;
            color: #64748b;
            font-weight: 500;
        }

        /* 菜单样式 - 现代菜单 */
        QMenuBar {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #f8fafc, stop:1 #f1f5f9);
            border-bottom: 2px solid #e2e8f0;
            color: #1e293b;
            font-weight: 600;
        }

        QMenuBar::item {
            padding: 8px 16px;
            background: transparent;
            border-radius: 6px;
        }

        QMenuBar::item:selected {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
        }

        QMenu {
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid #e2e8f0;
            border-radius: 8px;
        }

        QMenu::item {
            padding: 8px 24px;
            border-radius: 4px;
        }

        QMenu::item:selected {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #3b82f6, stop:1 #2563eb);
            color: white;
        }

        /* 工具栏样式 - 现代工具栏 */
        QToolBar {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #f8fafc, stop:1 #f1f5f9);
            border: none;
            spacing: 6px;
            padding: 4px;
        }

        QToolButton {
            background: transparent;
            border: 2px solid transparent;
            padding: 8px;
            border-radius: 8px;
        }

        QToolButton:hover {
            background: rgba(59, 130, 246, 0.1);
            border: 2px solid rgba(59, 130, 246, 0.2);
        }

        QToolButton:pressed {
            background: rgba(59, 130, 246, 0.2);
            border: 2px solid rgba(59, 130, 246, 0.3);
        }
        """
        
    def _get_dark_theme(self):
        """获取深色主题样式 - 现代深色风格"""
        return """
        /* 现代深色主题 */
        QMainWindow {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #1e293b, stop:1 #0f172a);
            color: #f1f5f9;
        }

        QWidget {
            background: rgba(30, 41, 59, 0.95);
            color: #f1f5f9;
            font-family: "Microsoft YaHei UI", "Segoe UI", "SF Pro Display", Arial, sans-serif;
            font-size: 9pt;
        }

        /* 按钮样式 - 深色渐变 */
        QPushButton {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #0ea5e9, stop:1 #0284c7);
            color: white;
            border: 2px solid rgba(14, 165, 233, 0.3);
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 9pt;
            min-width: 90px;
        }

        QPushButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #38bdf8, stop:1 #0ea5e9);
            border: 2px solid #38bdf8;
        }

        QPushButton:pressed {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #0369a1, stop:1 #075985);
            border: 2px solid #0369a1;
        }

        QPushButton:disabled {
            background: #374151;
            color: #6b7280;
            border: 2px solid #4b5563;
        }

        /* 输入框样式 - 深色边框 */
        QLineEdit, QTextEdit, QPlainTextEdit {
            border: 2px solid #374151;
            border-radius: 8px;
            padding: 8px 12px;
            background: rgba(15, 23, 42, 0.8);
            color: #f1f5f9;
            selection-background-color: #0ea5e9;
            font-size: 9pt;
        }

        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
            border: 2px solid #0ea5e9;
            background: rgba(15, 23, 42, 1);
        }

        /* 选项卡样式 - 深色卡片 */
        QTabWidget::pane {
            border: 2px solid #374151;
            background: rgba(30, 41, 59, 0.95);
            border-radius: 12px;
            margin-top: 8px;
        }

        QTabBar::tab {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(55, 65, 81, 0.9), stop:1 rgba(31, 41, 55, 0.9));
            color: #9ca3af;
            padding: 12px 24px;
            margin-right: 4px;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            border: 2px solid #374151;
            border-bottom: none;
            font-weight: 500;
            font-size: 9pt;
        }

        QTabBar::tab:selected {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(30, 41, 59, 1), stop:1 rgba(15, 23, 42, 1));
            color: #0ea5e9;
            font-weight: 700;
            border-bottom: 3px solid #0ea5e9;
        }

        QTabBar::tab:hover:!selected {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(75, 85, 99, 0.8), stop:1 rgba(55, 65, 81, 0.9));
            color: #e5e7eb;
        }

        /* 框架样式 - 深色卡片 */
        QFrame {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(30, 41, 59, 0.95), stop:1 rgba(15, 23, 42, 0.95));
            border: 2px solid rgba(14, 165, 233, 0.2);
            border-radius: 12px;
        }

        /* 分组框样式 - 深色边框 */
        QGroupBox {
            font-weight: 700;
            border: 2px solid #374151;
            border-radius: 10px;
            margin-top: 15px;
            padding-top: 15px;
            color: #f1f5f9;
            font-size: 10pt;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 15px;
            padding: 0 10px 0 10px;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(30, 41, 59, 1), stop:1 rgba(15, 23, 42, 1));
            color: #0ea5e9;
        }

        /* 列表样式 - 深色列表 */
        QListWidget, QTreeWidget, QTableWidget {
            border: 2px solid #374151;
            border-radius: 8px;
            background: rgba(15, 23, 42, 0.8);
            alternate-background-color: rgba(30, 41, 59, 0.6);
            gridline-color: #374151;
            color: #f1f5f9;
        }

        QListWidget::item, QTreeWidget::item, QTableWidget::item {
            padding: 8px;
            border-bottom: 1px solid #374151;
            border-radius: 4px;
        }

        QListWidget::item:selected, QTreeWidget::item:selected, QTableWidget::item:selected {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #0ea5e9, stop:1 #0284c7);
            color: white;
        }

        QListWidget::item:hover, QTreeWidget::item:hover, QTableWidget::item:hover {
            background: rgba(14, 165, 233, 0.2);
            color: #f1f5f9;
        }

        /* 滚动条样式 - 深色滚动条 */
        QScrollBar:vertical {
            background: rgba(55, 65, 81, 0.5);
            width: 14px;
            border-radius: 7px;
            margin: 0;
        }

        QScrollBar::handle:vertical {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #6b7280, stop:1 #4b5563);
            border-radius: 7px;
            min-height: 30px;
        }

        QScrollBar::handle:vertical:hover {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #9ca3af, stop:1 #6b7280);
        }

        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
            height: 0px;
        }

        /* 进度条样式 - 深色渐变 */
        QProgressBar {
            border: 2px solid #374151;
            border-radius: 10px;
            text-align: center;
            background: rgba(55, 65, 81, 0.8);
            color: #f1f5f9;
            font-weight: 600;
            font-size: 9pt;
            height: 24px;
        }

        QProgressBar::chunk {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #0ea5e9, stop:0.5 #38bdf8, stop:1 #0ea5e9);
            border-radius: 8px;
        }

        /* 状态栏样式 - 深色状态栏 */
        QStatusBar {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #1e293b, stop:1 #0f172a);
            border-top: 2px solid #374151;
            color: #9ca3af;
            font-weight: 500;
        }

        /* 菜单样式 - 深色菜单 */
        QMenuBar {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #1e293b, stop:1 #0f172a);
            border-bottom: 2px solid #374151;
            color: #f1f5f9;
            font-weight: 600;
        }

        QMenuBar::item {
            padding: 8px 16px;
            background: transparent;
            border-radius: 6px;
        }

        QMenuBar::item:selected {
            background: rgba(14, 165, 233, 0.2);
            color: #0ea5e9;
        }

        QMenu {
            background: rgba(30, 41, 59, 0.95);
            border: 2px solid #374151;
            border-radius: 8px;
        }

        QMenu::item {
            padding: 8px 24px;
            border-radius: 4px;
            color: #f1f5f9;
        }

        QMenu::item:selected {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #0ea5e9, stop:1 #0284c7);
            color: white;
        }

        /* 工具栏样式 - 深色工具栏 */
        QToolBar {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #1e293b, stop:1 #0f172a);
            border: none;
            spacing: 6px;
            padding: 4px;
        }

        QToolButton {
            background: transparent;
            border: 2px solid transparent;
            padding: 8px;
            border-radius: 8px;
            color: #f1f5f9;
        }

        QToolButton:hover {
            background: rgba(14, 165, 233, 0.2);
            border: 2px solid rgba(14, 165, 233, 0.3);
        }

        QToolButton:pressed {
            background: rgba(14, 165, 233, 0.3);
            border: 2px solid rgba(14, 165, 233, 0.4);
        }

        /* 标签样式 */
        QLabel {
            color: #f1f5f9;
        }
        """
        
    def _get_blue_theme(self):
        """获取蓝色主题样式 - 海洋蓝风格"""
        return """
        /* 海洋蓝主题 */
        QMainWindow {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #dbeafe, stop:1 #bfdbfe);
            color: #1e3a8a;
        }

        QWidget {
            background: rgba(255, 255, 255, 0.9);
            color: #1e3a8a;
            font-family: "Microsoft YaHei UI", "Segoe UI", "SF Pro Display", Arial, sans-serif;
            font-size: 9pt;
        }

        /* 按钮样式 - 海洋蓝渐变 */
        QPushButton {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #1d4ed8, stop:1 #1e40af);
            color: white;
            border: 2px solid rgba(29, 78, 216, 0.3);
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 9pt;
            min-width: 90px;
        }

        QPushButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #3b82f6, stop:1 #1d4ed8);
            border: 2px solid #3b82f6;
        }

        QPushButton:pressed {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #1e40af, stop:1 #1e3a8a);
            border: 2px solid #1e40af;
        }

        QPushButton:disabled {
            background: #e0e7ff;
            color: #a5b4fc;
            border: 2px solid #c7d2fe;
        }

        /* 输入框样式 - 海洋蓝边框 */
        QLineEdit, QTextEdit, QPlainTextEdit {
            border: 2px solid #c7d2fe;
            border-radius: 8px;
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.95);
            color: #1e3a8a;
            selection-background-color: #1d4ed8;
            font-size: 9pt;
        }

        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
            border: 2px solid #1d4ed8;
            background: rgba(255, 255, 255, 1);
        }

        /* 选项卡样式 - 海洋蓝卡片 */
        QTabWidget::pane {
            border: 2px solid #c7d2fe;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            margin-top: 8px;
        }

        QTabBar::tab {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(224, 231, 255, 0.9), stop:1 rgba(199, 210, 254, 0.9));
            color: #6366f1;
            padding: 12px 24px;
            margin-right: 4px;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            border: 2px solid #c7d2fe;
            border-bottom: none;
            font-weight: 500;
            font-size: 9pt;
        }

        QTabBar::tab:selected {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(255, 255, 255, 1), stop:1 rgba(248, 250, 252, 1));
            color: #1d4ed8;
            font-weight: 700;
            border-bottom: 3px solid #1d4ed8;
        }

        QTabBar::tab:hover:!selected {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(199, 210, 254, 0.9), stop:1 rgba(165, 180, 252, 0.9));
            color: #1e3a8a;
        }

        /* 框架样式 - 海洋蓝卡片 */
        QFrame {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(255, 255, 255, 0.95), stop:1 rgba(248, 250, 252, 0.95));
            border: 2px solid rgba(29, 78, 216, 0.2);
            border-radius: 12px;
        }

        /* 分组框样式 - 海洋蓝边框 */
        QGroupBox {
            font-weight: 700;
            border: 2px solid #c7d2fe;
            border-radius: 10px;
            margin-top: 15px;
            padding-top: 15px;
            color: #1e3a8a;
            font-size: 10pt;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 15px;
            padding: 0 10px 0 10px;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(255, 255, 255, 1), stop:1 rgba(248, 250, 252, 1));
            color: #1d4ed8;
        }

        /* 列表样式 - 海洋蓝列表 */
        QListWidget, QTreeWidget, QTableWidget {
            border: 2px solid #c7d2fe;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.95);
            alternate-background-color: rgba(224, 231, 255, 0.5);
            gridline-color: #e0e7ff;
            color: #1e3a8a;
        }

        QListWidget::item, QTreeWidget::item, QTableWidget::item {
            padding: 8px;
            border-bottom: 1px solid #e0e7ff;
            border-radius: 4px;
        }

        QListWidget::item:selected, QTreeWidget::item:selected, QTableWidget::item:selected {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #1d4ed8, stop:1 #1e40af);
            color: white;
        }

        QListWidget::item:hover, QTreeWidget::item:hover, QTableWidget::item:hover {
            background: rgba(29, 78, 216, 0.1);
            color: #1e3a8a;
        }

        /* 滚动条样式 - 海洋蓝滚动条 */
        QScrollBar:vertical {
            background: rgba(199, 210, 254, 0.5);
            width: 14px;
            border-radius: 7px;
            margin: 0;
        }

        QScrollBar::handle:vertical {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #a5b4fc, stop:1 #818cf8);
            border-radius: 7px;
            min-height: 30px;
        }

        QScrollBar::handle:vertical:hover {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #818cf8, stop:1 #6366f1);
        }

        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
            height: 0px;
        }

        /* 进度条样式 - 海洋蓝渐变 */
        QProgressBar {
            border: 2px solid #c7d2fe;
            border-radius: 10px;
            text-align: center;
            background: rgba(224, 231, 255, 0.8);
            color: #1e3a8a;
            font-weight: 600;
            font-size: 9pt;
            height: 24px;
        }

        QProgressBar::chunk {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #1d4ed8, stop:0.5 #3b82f6, stop:1 #1d4ed8);
            border-radius: 8px;
        }

        /* 状态栏样式 - 海洋蓝状态栏 */
        QStatusBar {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #dbeafe, stop:1 #bfdbfe);
            border-top: 2px solid #c7d2fe;
            color: #6366f1;
            font-weight: 500;
        }

        /* 菜单样式 - 海洋蓝菜单 */
        QMenuBar {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #dbeafe, stop:1 #bfdbfe);
            border-bottom: 2px solid #c7d2fe;
            color: #1e3a8a;
            font-weight: 600;
        }

        QMenuBar::item {
            padding: 8px 16px;
            background: transparent;
            border-radius: 6px;
        }

        QMenuBar::item:selected {
            background: rgba(29, 78, 216, 0.1);
            color: #1d4ed8;
        }

        QMenu {
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid #c7d2fe;
            border-radius: 8px;
        }

        QMenu::item {
            padding: 8px 24px;
            border-radius: 4px;
            color: #1e3a8a;
        }

        QMenu::item:selected {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #1d4ed8, stop:1 #1e40af);
            color: white;
        }

        /* 工具栏样式 - 海洋蓝工具栏 */
        QToolBar {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #dbeafe, stop:1 #bfdbfe);
            border: none;
            spacing: 6px;
            padding: 4px;
        }

        QToolButton {
            background: transparent;
            border: 2px solid transparent;
            padding: 8px;
            border-radius: 8px;
            color: #1e3a8a;
        }

        QToolButton:hover {
            background: rgba(29, 78, 216, 0.1);
            border: 2px solid rgba(29, 78, 216, 0.2);
        }

        QToolButton:pressed {
            background: rgba(29, 78, 216, 0.2);
            border: 2px solid rgba(29, 78, 216, 0.3);
        }

        /* 标签样式 */
        QLabel {
            color: #1e3a8a;
        }
        """
